<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Api\AuthController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

// API v1 routes
Route::prefix('v1')->group(function () {

  // Auth - Public
  Route::prefix('auth')->middleware('api.ratelimit:30')->group(function () {
    Route::post('register', [AuthController::class, 'register']);
    Route::post('login', [AuthController::class, 'login']);
    Route::post('refresh', [AuthController::class, 'refresh']);
    Route::post('verify', [AuthController::class, 'verify']);
    Route::post('resend-verification', [AuthController::class, 'resendVerification']);
    Route::post('forgot-password', [AuthController::class, 'forgotPassword']);
    Route::post('verify-reset-otp', [AuthController::class, 'verifyOtpForPasswordReset']);
    Route::post('reset-password', [AuthController::class, 'resetPassword']);
  });

  // Auth - Protected
  Route::middleware(['auth.api', 'api.ratelimit:30'])->prefix('auth')->group(function () {
    Route::post('logout', [AuthController::class, 'logout']);
    Route::post('logout-all', [AuthController::class, 'logoutAll']);
    Route::get('profile', [AuthController::class, 'profile']);
    Route::put('profile', [AuthController::class, 'updateProfile']);
    Route::post('change-password', [AuthController::class, 'changePassword']);
  });

  // Các API khác sẽ được thêm vào đây
  // Route::apiResource('stores', StoreController::class);
  // Route::apiResource('services', ServiceController::class);
});
