<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Admin\AuthController;
use App\Http\Controllers\Admin\BeautiController;
use App\Http\Controllers\Admin\BeautiPackageController;
use App\Http\Controllers\Admin\BeautiServiceController;
use App\Http\Controllers\Admin\BeautiStoreController;
use App\Http\Controllers\Admin\BeautiTypeController;
use App\Http\Controllers\Admin\DashboardController;
use App\Http\Controllers\Admin\HostController;
use App\Http\Controllers\Admin\LocationAreasController;
use App\Http\Controllers\Admin\LocationDistrictsController;
use App\Http\Controllers\Admin\LocationProvinceController;
use App\Http\Controllers\Admin\LocationStreetsController;
use App\Http\Controllers\Admin\LocationWardsController;
use App\Http\Controllers\Admin\MemberController;
use App\Http\Controllers\Admin\NewsArticleController;
use App\Http\Controllers\Admin\NewsCategoriesController;
use App\Http\Controllers\Admin\SystemConfigController;
use App\Http\Controllers\Admin\TeamController;
use App\Http\Controllers\Admin\UserController;

Route::middleware('guest:admin')->group(function () {
    Route::get('login', [AuthController::class, 'showLogin'])->name('login');
    Route::post('login', [AuthController::class, 'login'])->name('login.submit');
});

Route::middleware(['auth.admin'])->group(function () {
    Route::post('logout', [AuthController::class, 'logout'])->name('logout');
    Route::get('/', [DashboardController::class, 'index'])->name('dashboard');

    // users
    Route::resource('users', UserController::class)->except(['show']);
    Route::get('users/data', [UserController::class, 'data'])->name('users.data');
    Route::post('users/{id}/toggle-status', [UserController::class, 'toggleStatus'])->name('user.toggle-status');
    

    // host
    Route::resource('host', HostController::class)->except(['show']);
    Route::get('host/data', [HostController::class, 'data'])->name('host.data');
    Route::post('host/{id}/toggle-status', [HostController::class, 'toggleStatus'])->name('host.toggle-status');

    // members
    Route::resource('members', MemberController::class)->except(['show']);
    Route::get('members/data', [MemberController::class, 'data'])->name('members.data');
    Route::post('members/{id}/toggle-status', [MemberController::class, 'toggleStatus'])->name('member.toggle-status');
    Route::post('members/{id}/reset-password', [MemberController::class, 'resetPassWord'])->name('members.reset-password');

    // teams
    Route::resource('teams', TeamController::class)->except(['show']);
    Route::get('teams/data', [TeamController::class, 'data'])->name('teams.data');
    Route::post('teams/{id}/toggle-status', [TeamController::class, 'toggleStatus'])->name('team.toggle-status');

    //location-province
    Route::resource('location-provinces', LocationProvinceController::class)->except(['show']);
    Route::get('location-provinces/data', [LocationProvinceController::class, 'data'])->name('location-provinces.data');
    Route::post('location-provinces/{id}/toggle-status', [LocationProvinceController::class, 'toggleStatus'])->name('locationprovince.toggle-status');

    // location-districts
    Route::resource('location-districts', LocationDistrictsController::class)->except(['show']);
    Route::get('location-districts/data', [LocationDistrictsController::class, 'data'])->name('location-districts.data');

    // location-wards
    Route::resource('location-wards', LocationWardsController::class)->except(['show']);
    Route::get('location-wards/data', [LocationWardsController::class, 'data'])->name('location-wards.data');

    // location-streets
    Route::resource('location-streets', LocationStreetsController::class)->except(['show']);
    Route::get('location-streets/data', [LocationStreetsController::class, 'data'])->name('location-streets.data');

    // location-areas
    Route::resource('location-areas', LocationAreasController::class)->except(['show']);
    Route::get('location-areas/data', [LocationAreasController::class, 'data'])->name('location-areas.data');

    // system-config
    Route::resource('system-config', SystemConfigController::class)->except(['show']);
    Route::get('system-config/data', [SystemConfigController::class, 'data'])->name('system-config.data');
    Route::post('system-config/{id}/toggle-status', [SystemConfigController::class, 'toggleStatus'])->name('systemconfig.toggle-status');

    // news
    Route::resource('news-article', NewsArticleController::class)->except(['show']);
    Route::get('news-article/data', [NewsArticleController::class, 'data'])->name('news-article.data');
    Route::post('news/{id}/toggle-status', [NewsArticleController::class, 'toggleStatus'])->name('newsarticle.toggle-status');

    //categories
    Route::resource('news-categories', NewsCategoriesController::class)->except(['show']);
    Route::get('news-categories/data', [NewsCategoriesController::class, 'data'])->name('news-categories.data');
    Route::post('news-categories/{id}/toggle-status', [NewsCategoriesController::class, 'toggleStatus'])->name('newscategories.toggle-status');

    //  Beauti-types 
    Route::resource('beauti-types', BeautiTypeController::class)->except(['show']);
    Route::get('beauti-types/data', [BeautiTypeController::class, 'data'])->name('beauti-types.data');
    Route::post('beauti-types/{id}/toggle-status', [BeautiTypeController::class, 'toggleStatus'])->name('beautitype.toggle-status');

    //  Beauti-services 
    Route::resource('beauti-services', BeautiServiceController::class)->except(['show']);
    Route::get('beauti-services/data', [BeautiServiceController::class, 'data'])->name('beauti-services.data');
    Route::post('beauti-services/{id}/toggle-status', [BeautiServiceController::class, 'toggleStatus'])->name('beautiservice.toggle-status');

    //  Beauti-services 
    Route::resource('beauti-stores', BeautiStoreController::class)->except(['show']);
    Route::get('beauti-stores/data', [BeautiStoreController::class, 'data'])->name('beauti-stores.data');
    Route::post('beauti-stores/{id}/toggle-status', [BeautiStoreController::class, 'toggleStatus'])->name('beautistore.toggle-status');

    //  Beautis 
    Route::resource('beauties', BeautiController::class)->except(['show']);
    Route::get('beauties/data', [BeautiController::class, 'data'])->name('beauties.data');
    Route::post('beauties/{id}/toggle-status', [BeautiController::class, 'toggleStatus'])->name('beauti.toggle-status');

    //  Beauti-packages 
    Route::resource('beauti-packages', BeautiPackageController::class)->except(['show']);
    Route::get('beauti-packages/data', [BeautiPackageController::class, 'data'])->name('beauti-packages.data');
    Route::post('beauti-packages/{id}/toggle-status', [BeautiPackageController::class, 'toggleStatus'])->name('beautipackage.toggle-status');

    Route::get('getStores', [BeautiStoreController::class, 'byBeauti']);
    Route::get('getServices', [BeautiServiceController::class, 'byStore']);
    Route::get('services-by-types', [BeautiServiceController::class, 'byType'])->name('services.bytype');
});
