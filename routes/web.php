<?php

use App\Http\Controllers\AuthController;
use App\Http\Controllers\BeautiStoreController;
use App\Http\Controllers\BeautiTypeController;
use App\Http\Controllers\HomeController;
use App\Http\Controllers\ImageUploadController;
use App\Http\Controllers\LocationLookupController;
use App\Http\Controllers\NewsArticleController;
use Illuminate\Support\Facades\Route;
use CKSource\CKFinderBridge\Controller\CKFinderController;

Route::any('/ckfinder/connector', [CKFinderController::class, 'requestAction'])
    ->name('ckfinder_connector');
   

Route::any('/ckfinder/browser', [CKFinderController::class, 'browserAction'])
    ->name('ckfinder_browser');
    

Route::post('images/upload', [ImageUploadController::class, 'uploadImage'])->name('upload.image');
// location
Route::get('districts/{provinceCode}', [LocationLookupController::class, 'getDistricts'])->name('districts');
Route::get('wards/{districtCode}', [LocationLookupController::class, 'getWards'])->name('wards');
Route::get('streets/{provinceCode}/{districtCode}/{wardCode}', [LocationLookupController::class, 'getStreets'])->name('streets');
Route::get('location-full/{province_code}', [LocationLookupController::class, 'getFullLocation']);
Route::get('location-full/{provinceCode}/{districtCode}', [LocationLookupController::class, 'getWardsAndStreets']);

// home 
Route::get('/', [HomeController::class, 'index'])->name('home');
// auth 
Route::middleware('guest')->group(function () {
    Route::post('/register', [AuthController::class, 'register'])->name('register');
    Route::post('/login',    [AuthController::class, 'login'])->name('login');
});

Route::post('/logout', [AuthController::class, 'logout'])
    ->middleware('auth')
    ->name('logout');

Route::get('/verify-email', [AuthController::class, 'verifyEmail'])
    ->name('verify.email');

Route::post('/resend-verification', [AuthController::class, 'resendVerification'])->name('verification.resend');

Route::post('/verify-phone', [AuthController::class, 'verifyPhone'])
    ->name('verify.phone');

// stores 

Route::get('loai-hinh/{type}/co-so/{province}', [BeautiStoreController::class, 'showStores'])
    ->name('beautitype.stores');
// services 
Route::get('co-so/{slug}', [BeautiStoreController::class, 'showServices'])
    ->name('beautistore.services');

Route::get('co-so/dich-vu/{slug}', [BeautiStoreController::class, 'showPackageServices'])
    ->name('beautistore.package-services');

Route::get('/news', [NewsArticleController::class, 'index'])->name('news.index');

// Route::get('/instruct', [GuideController::class, 'index'])->name('instruct');