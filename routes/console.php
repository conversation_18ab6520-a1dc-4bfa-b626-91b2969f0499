<?php

use Illuminate\Foundation\Inspiring;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Schedule;

Artisan::command('inspire', function () {
  $this->comment(Inspiring::quote());
})->purpose('Display an inspiring quote');

// Cleanup expired tokens tự động hàng ngày lúc 2:00 AM
Schedule::command('tokens:cleanup')
  ->daily()
  ->at('02:00')
  ->onOneServer()
  ->withoutOverlapping()
  ->description('Cleanup expired refresh tokens and JWT blacklist');
