<?php

return [
  /*
    |--------------------------------------------------------------------------
    | Validation Regex Patterns
    |--------------------------------------------------------------------------
    |
    | <PERSON><PERSON><PERSON> regex patterns được sử dụng cho validation trong toàn bộ project
    |
    */

  'regex' => [
    /*
        |--------------------------------------------------------------------------
        | Email Regex
        |--------------------------------------------------------------------------
        |
        | Regex pattern cho email validation
        | Tuân thủ RFC 5322 standard
        |
        */
    'email' => '/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/',

    /*
        |--------------------------------------------------------------------------
        | Vietnam Phone Number Regex
        |--------------------------------------------------------------------------
        |
        | Regex patterns cho số điện thoại Việt Nam
        | Hỗ trợ các đầu số: 03, 05, 07, 08, 09 và các đầu số mới
        |
        */
    'phone' => [
      // Số điện thoại di động Việt Nam (10-11 số)
      'mobile' => '/^(0|\+84)(3[2-9]|5[689]|7[06-9]|8[1-689]|9[0-46-9])[0-9]{7,8}$/',

      // Số điện thoại cố định Việt Nam
      'landline' => '/^(0|\+84)(2[0-9])[0-9]{8}$/',

      // Tất cả số điện thoại Việt Nam (di động + cố định)
      'all' => '/^(0|\+84)((3[2-9]|5[689]|7[06-9]|8[1-689]|9[0-46-9])[0-9]{7,8}|(2[0-9])[0-9]{8})$/',
    ],

    /*
        |--------------------------------------------------------------------------
        | Password Regex
        |--------------------------------------------------------------------------
        |
        | Regex patterns cho password validation
        |
        */
    'password' => [
      // Mật khẩu cơ bản: ít nhất 6 ký tự
      'basic' => '/^.{6,}$/',

      // Mật khẩu mạnh: ít nhất 8 ký tự, có chữ hoa, chữ thường, số
      'strong' => '/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d@$!%*?&]{8,}$/',

      // Mật khẩu rất mạnh: ít nhất 8 ký tự, có chữ hoa, chữ thường, số, ký tự đặc biệt
      'very_strong' => '/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/',
    ],

    /*
        |--------------------------------------------------------------------------
        | Other Common Patterns
        |--------------------------------------------------------------------------
        */
    'fullname' => '/^[a-zA-ZÀ-ỹ\s]{2,50}$/', // Tên tiếng Việt có dấu
    'username' => '/^[a-zA-Z0-9_]{3,20}$/', // Username chỉ chữ, số, gạch dưới
    'slug' => '/^[a-z0-9-]+$/', // Slug cho URL
  ],

  /*
    |--------------------------------------------------------------------------
    | Validation Messages
    |--------------------------------------------------------------------------
    |
    | Các message tương ứng với từng regex pattern
    |
    */
  'messages' => [
    'email' => 'Email không đúng định dạng',
    'phone' => [
      'mobile' => 'Số điện thoại di động không đúng định dạng (VD: 0912345678)',
      'landline' => 'Số điện thoại cố định không đúng định dạng (VD: 02412345678)',
      'all' => 'Số điện thoại không đúng định dạng (VD: 0912345678)',
    ],
    'password' => [
      'basic' => 'Mật khẩu phải có ít nhất 6 ký tự',
      'strong' => 'Mật khẩu phải có ít nhất 8 ký tự, bao gồm chữ hoa, chữ thường và số',
      'very_strong' => 'Mật khẩu phải có ít nhất 8 ký tự, bao gồm chữ hoa, chữ thường, số và ký tự đặc biệt',
    ],
    'fullname' => 'Họ tên chỉ được chứa chữ cái và khoảng trắng (2-50 ký tự)',
    'username' => 'Tên đăng nhập chỉ được chứa chữ cái, số và dấu gạch dưới (3-20 ký tự)',
    'slug' => 'Slug chỉ được chứa chữ cái thường, số và dấu gạch ngang',
  ],

  /*
    |--------------------------------------------------------------------------
    | Phone Number Prefixes
    |--------------------------------------------------------------------------
    |
    | Danh sách các đầu số điện thoại Việt Nam theo nhà mạng
    |
    */
  'phone_prefixes' => [
    'viettel' => ['032', '033', '034', '035', '036', '037', '038', '039', '086', '096', '097', '098'],
    'vinaphone' => ['081', '082', '083', '084', '085', '088', '091', '094'],
    'mobifone' => ['070', '076', '077', '078', '079', '089', '090', '093'],
    'vietnamobile' => ['052', '056', '058', '092'],
    'gmobile' => ['059', '099'],
    'landline' => ['024', '028', '0203', '0204', '0205', '0206', '0207', '0208', '0209', '0210', '0211', '0212', '0213', '0214', '0215', '0216', '0218', '0219', '0220', '0221', '0222', '0225', '0226', '0227', '0228', '0229', '0232', '0233', '0234', '0235', '0236', '0237', '0238', '0239', '0251', '0252', '0254', '0255', '0256', '0258', '0259', '0260', '0261', '0262', '0263', '0269', '0270', '0271', '0272', '0273', '0274', '0275', '0276', '0277', '0290', '0291', '0292', '0293', '0294', '0296', '0297', '0299'],
  ],
];
