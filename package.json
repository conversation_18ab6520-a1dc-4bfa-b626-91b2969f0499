{"private": true, "type": "module", "scripts": {"build": "vite build", "dev": "vite"}, "devDependencies": {"@tailwindcss/vite": "^4.0.0", "axios": "^1.8.2", "concurrently": "^9.0.1", "fs-extra": "^11.3.0", "laravel-vite-plugin": "^1.2.0", "vite": "^6.2.4"}, "dependencies": {"@mdi/font": "^7.4.47", "@popperjs/core": "^2.11.8", "apexcharts": "^4.7.0", "bootstrap": "^5.3.6", "choices.js": "^11.1.0", "datatables.net": "^2.3.1", "datatables.net-bs5": "^2.3.1", "datatables.net-buttons": "^3.2.3", "datatables.net-buttons-bs5": "^3.2.3", "datatables.net-responsive-bs5": "^3.0.4", "feather-icons": "^4.29.2", "jquery": "^3.7.1", "jszip": "^3.10.1", "pdfmake": "^0.2.20", "sass": "^1.88.0", "select2": "^4.1.0-rc.0", "select2-bootstrap-5-theme": "^1.3.0", "simplebar": "^6.3.1", "slick-carousel": "^1.8.1", "sweetalert2": "^11.21.2", "swiper": "^11.2.8", "tinymce": "^7.9.0"}}