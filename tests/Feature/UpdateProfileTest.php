<?php

namespace Tests\Feature;

use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Tests\TestCase;

class UpdateProfileTest extends TestCase
{
  use RefreshDatabase;

  protected function setUp(): void
  {
    parent::setUp();
    Storage::fake('public');
  }

  public function test_user_can_update_profile_without_avatar()
  {
    $user = User::factory()->create([
      'fullname' => 'John Doe',
      'email' => '<EMAIL>',
      'phone' => '0123456789'
    ]);

    $response = $this->actingAs($user, 'api')
      ->putJson('/api/v1/auth/profile', [
        'fullname' => 'Jane Doe',
        'address' => '123 Main St'
      ]);

    $response->assertStatus(200)
      ->assertJson([
        'success' => true,
        'message' => 'Cập nhật thông tin thành công'
      ]);

    $this->assertDatabaseHas('users', [
      'id' => $user->id,
      'fullname' => '<PERSON>',
      'address' => '123 Main St'
    ]);
  }

  public function test_user_can_update_profile_with_avatar()
  {
    $user = User::factory()->create();

    $file = UploadedFile::fake()->image('avatar.jpg', 100, 100);

    $response = $this->actingAs($user, 'api')
      ->putJson('/api/v1/auth/profile', [
        'fullname' => 'Jane Doe',
        'avatar' => $file
      ]);

    $response->assertStatus(200)
      ->assertJson([
        'success' => true,
        'message' => 'Cập nhật thông tin thành công'
      ]);

    $this->assertDatabaseHas('users', [
      'id' => $user->id,
      'fullname' => 'Jane Doe'
    ]);

    // Kiểm tra avatar đã được lưu
    $user->refresh();
    $this->assertNotNull($user->avatar);
    $this->assertStringContainsString('uploads/users/avatars', $user->avatar);
    $this->assertStringContainsString('user-' . $user->id, $user->avatar);
  }

  public function test_user_can_remove_avatar()
  {
    $user = User::factory()->create([
      'avatar' => 'uploads/users/avatars/old-avatar.jpg'
    ]);

    $response = $this->actingAs($user, 'api')
      ->putJson('/api/v1/auth/profile', [
        'avatar_removed' => true
      ]);

    $response->assertStatus(200);

    $user->refresh();
    $this->assertNull($user->avatar);
  }

  public function test_user_can_update_email_and_needs_verification()
  {
    $user = User::factory()->create([
      'email' => '<EMAIL>'
    ]);

    $response = $this->actingAs($user, 'api')
      ->putJson('/api/v1/auth/profile', [
        'email' => '<EMAIL>'
      ]);

    $response->assertStatus(200)
      ->assertJson([
        'success' => true,
        'message' => 'Cập nhật thành công. Vui lòng xác thực email mới của bạn.'
      ])
      ->assertJsonStructure([
        'data' => [
          'user' => [
            'id',
            'fullname',
            'email',
            'avatar'
          ],
          'verification' => [
            'type',
            'identifier'
          ]
        ]
      ]);
  }

  public function test_user_can_update_phone_and_needs_verification()
  {
    $user = User::factory()->create([
      'phone' => '0123456789'
    ]);

    $response = $this->actingAs($user, 'api')
      ->putJson('/api/v1/auth/profile', [
        'phone' => '0987654321'
      ]);

    $response->assertStatus(200)
      ->assertJson([
        'success' => true,
        'message' => 'Cập nhật thành công. Vui lòng xác thực số điện thoại mới của bạn qua Zalo.'
      ])
      ->assertJsonStructure([
        'data' => [
          'user' => [
            'id',
            'fullname',
            'phone',
            'avatar'
          ],
          'verification' => [
            'type',
            'identifier'
          ]
        ]
      ]);
  }

  public function test_avatar_validation()
  {
    $user = User::factory()->create();

    // Test invalid file type
    $invalidFile = UploadedFile::fake()->create('document.txt', 100);

    $response = $this->actingAs($user, 'api')
      ->putJson('/api/v1/auth/profile', [
        'avatar' => $invalidFile
      ]);

    $response->assertStatus(422)
      ->assertJsonValidationErrors(['avatar']);

    // Test file too large
    $largeFile = UploadedFile::fake()->image('large.jpg')->size(3000);

    $response = $this->actingAs($user, 'api')
      ->putJson('/api/v1/auth/profile', [
        'avatar' => $largeFile
      ]);

    $response->assertStatus(422)
      ->assertJsonValidationErrors(['avatar']);
  }

  public function test_avatar_url_is_returned_in_response()
  {
    $user = User::factory()->create([
      'avatar' => 'uploads/users/avatars/test-avatar.jpg'
    ]);

    $response = $this->actingAs($user, 'api')
      ->getJson('/api/v1/auth/profile');

    $response->assertStatus(200)
      ->assertJsonStructure([
        'data' => [
          'user' => [
            'id',
            'fullname',
            'avatar'
          ]
        ]
      ]);

    $avatarUrl = $response->json('data.user.avatar');
    $this->assertNotNull($avatarUrl);
    $this->assertStringContainsString('http', $avatarUrl);
  }
}
