<?php

use App\Http\Middleware\Admin\AdminAuthenticate;
use App\Providers\TokenServiceProvider;
use App\Providers\ValidationServiceProvider;
use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;
use Spatie\Permission\Middleware\PermissionMiddleware;
use Spatie\Permission\Middleware\RoleMiddleware;
use Spatie\Permission\Middleware\RoleOrPermissionMiddleware;

return Application::configure(basePath: dirname(__DIR__))
  ->withRouting(
    web: __DIR__ . '/../routes/web.php',
    api: __DIR__ . '/../routes/api.php',
    commands: __DIR__ . '/../routes/console.php',
    health: '/up',
    then: function () {
      Route::namespace('App\Http\Controllers\Admin')
        ->prefix('admin')
        ->middleware(['web'])
        ->name('admin.')
        ->group(base_path('routes/admin.php'));
    }
  )
  ->withMiddleware(function (Middleware $middleware) {
    $middleware->alias([
      'auth.admin'    => AdminAuthenticate::class,
      'auth.api'      => \App\Http\Middleware\ApiAuthenticate::class,
      'api.ratelimit' => \App\Http\Middleware\ApiRateLimit::class,
      'parse.multipart' => \App\Http\Middleware\ParseMultipartFormData::class,
    ]);
  })
  ->withProviders([
    TokenServiceProvider::class,
    ValidationServiceProvider::class,
  ])
  ->withExceptions(function (Exceptions $exceptions) {
    $exceptions->report(function (Throwable $e) {});
    $exceptions->render(function (InvalidOrderException $e, Request $request) {
      return response()->view('errors.invalid-order', status: 500);
    });
  })->create();
