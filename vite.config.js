import { defineConfig } from "vite";
import laravel from "laravel-vite-plugin";
import path from "path";
import fsExtra from "fs-extra";
const { copySync, removeSync } = fsExtra;

export default defineConfig({
    plugins: [
        laravel({
            input: [
                "resources/scss/styles.scss",
                "resources/scss/admin/admin.scss",
                "resources/js/app.js",
                "resources/js/admin/main.js",
            ],
            refresh: true,
            buildDirectory: "build",
        }),
        {
            name: "copy-tinymce-assets",
            buildStart() {
                const sourcePath = path.resolve(
                    __dirname,
                    "node_modules/tinymce"
                );
                const destPath = path.resolve(__dirname, "public/tinymce");
                removeSync(destPath);
                copySync(sourcePath, destPath, { overwrite: true });
            },
        },
    ],
    resolve: {
        alias: {
            tinymce: path.resolve(__dirname, "node_modules/tinymce"),
            "@": path.resolve(__dirname, "resources"),
            "@js": path.resolve(__dirname, "resources/js"),
            '@lib': path.resolve(__dirname, 'resources/lib'),
        },
    },
    build: {
        outDir: "public/build",
        manifest: true,
        emptyOutDir: true,
        rollupOptions: {
            input: [
                "resources/scss/styles.scss",
                "resources/scss/admin/admin.scss",
                "resources/js/app.js",
                "resources/js/admin/main.js",
            ],
        },
        assetsDir: "",
    },
});
