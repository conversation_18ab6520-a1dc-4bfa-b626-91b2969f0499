# ImageService Documentation

## Tổng quan

ImageService là một service class được thiết kế để xử lý tất cả các thao tác liên quan đến hình ảnh trong ứng dụng Laravel. Service này cung cấp các tính năng upload, resize, optimize, delete và các helper methods khác.

## Cài đặt

### 1. Đăng ký Service Provider

Thêm `ImageServiceProvider` vào `config/app.php`:

```php
'providers' => [
    // ...
    App\Providers\ImageServiceProvider::class,
],
```

### 2. Cài đặt Intervention Image (nếu chưa có)

```bash
composer require intervention/image
```

## Cấu trúc thư mục

```
app/
├── Services/
│   ├── ImageService.php                    # Main service class
│   └── Interfaces/
│       └── ImageServiceInterface.php       # Interface
├── Helpers/
│   └── ImageHelper.php                     # Helper class
└── Providers/
    └── ImageServiceProvider.php            # Service provider
```

## C<PERSON>ch sử dụng

### 1. Sử dụng trực tiếp ImageService

```php
use App\Services\Interfaces\ImageServiceInterface;

class UserController extends Controller
{
    public function updateAvatar(Request $request)
    {
        $imageService = app(ImageServiceInterface::class);

        $file = $request->file('avatar');
        $path = $imageService->upload(
            $file,
            'uploads/users/avatars',
            "user-{$user->id}",
            [
                'resize'     => true,
                'max_width'  => 800,
                'max_height' => 800,
                'quality'    => 85
            ]
        );

        $user->avatar = $path;
        $user->save();
    }
}
```

### 2. Sử dụng ImageHelper (Khuyến nghị)

```php
use App\Helpers\ImageHelper;

class UserController extends Controller
{
    public function updateAvatar(Request $request)
    {
        $file = $request->file('avatar');
        $path = ImageHelper::uploadUserAvatar($file, $user->id);

        $user->avatar = $path;
        $user->save();
    }

    public function showProfile()
    {
        $avatarUrl = ImageHelper::getUserAvatarUrl($user->avatar);
        return view('profile', compact('avatarUrl'));
    }
}
```

## API Reference

### ImageService Methods

#### upload()

Upload và xử lý hình ảnh

```php
public function upload(
    UploadedFile $file,
    string $directory,
    ?string $filenamePrefix = null,
    array $options = []
): string
```

**Parameters:**

-   `$file`: File upload từ request
-   `$directory`: Thư mục lưu trữ (relative path)
-   `$filenamePrefix`: Prefix cho tên file (optional)
-   `$options`: Các tùy chọn xử lý

**Options:**

```php
$options = [
    'resize'     => true,        // Có resize hay không
    'max_width'  => 1920,        // Chiều rộng tối đa
    'max_height' => 1080,        // Chiều cao tối đa
    'quality'    => 85           // Chất lượng (0-100)
];
```

#### uploadMultiple()

Upload nhiều hình ảnh cùng lúc

```php
public function uploadMultiple(
    array $files,
    string $directory,
    ?string $filenamePrefix = null,
    array $options = []
): array
```

#### resize()

Resize hình ảnh hiện có

```php
public function resize(
    string $imagePath,
    int $width,
    int $height,
    ?string $outputPath = null
): string
```

#### createThumbnail()

Tạo thumbnail từ hình ảnh

```php
public function createThumbnail(
    string $imagePath,
    int $width = 300,
    int $height = 300
): string
```

#### optimize()

Tối ưu hóa chất lượng và kích thước

```php
public function optimize(string $imagePath, int $quality = 85): string
```

#### delete()

Xóa hình ảnh

```php
public function delete(?string $relativePath): bool
```

#### getImageInfo()

Lấy thông tin hình ảnh

```php
public function getImageInfo(string $imagePath): array
```

**Returns:**

```php
[
    'width'      => 800,
    'height'     => 600,
    'size'       => '245.5 KB',
    'size_bytes' => 251392,
    'mime_type'  => 'image/jpeg',
    'extension'  => 'jpg',
    'filename'   => 'image.jpg',
    'path'       => 'uploads/images/image.jpg',
    'url'        => 'http://domain.com/uploads/images/image.jpg'
]
```

### ImageHelper Methods

#### Upload Methods

```php
// Upload user avatar
ImageHelper::uploadUserAvatar($file, $userId, $options);

// Upload member avatar
ImageHelper::uploadMemberAvatar($file, $username, $options);

// Upload beauti store image
ImageHelper::uploadBeautiStoreImage($file, $storeName, $options);

// Upload beauti service image
ImageHelper::uploadBeautiServiceImage($file, $serviceName, $options);

// Upload news article image
ImageHelper::uploadNewsArticleImage($file, $articleTitle, $options);

// Upload TinyMCE editor image
ImageHelper::uploadTinyMCEImage($file, $options);
```

#### URL Methods

```php
// Get image URL với fallback
ImageHelper::getImageUrl($imagePath, $defaultImage);

// Get specific image URLs
ImageHelper::getUserAvatarUrl($avatarPath);
ImageHelper::getMemberAvatarUrl($avatarPath);
ImageHelper::getBeautiStoreImageUrl($imagePath);
ImageHelper::getBeautiServiceImageUrl($imagePath);
ImageHelper::getNewsArticleImageUrl($imagePath);
```

#### Utility Methods

```php
// Validate image
ImageHelper::isValidImage($file);

// Get image info
ImageHelper::getImageInfo($imagePath);

// Create thumbnail
ImageHelper::createThumbnail($imagePath, $width, $height);

// Optimize image
ImageHelper::optimizeImage($imagePath, $quality);

// Delete image với logging
ImageHelper::deleteImage($imagePath, $context);
```

## Ví dụ sử dụng

### 1. Upload Avatar trong Controller

```php
public function updateProfile(Request $request)
{
    $data = UpdateProfileData::fromRequest($request);

    if ($data->avatar instanceof UploadedFile) {
        // Xóa avatar cũ
        if ($user->avatar) {
            ImageHelper::deleteImage($user->avatar, 'user_profile_update');
        }

        // Upload avatar mới
        $avatarPath = ImageHelper::uploadUserAvatar($data->avatar, $user->id);
        $data->avatar = $avatarPath;
    }

    $user->update($data->toUpdateArray());

    return response()->json([
        'message' => 'Cập nhật thành công',
        'avatar_url' => ImageHelper::getUserAvatarUrl($user->avatar)
    ]);
}
```

### 2. Upload Multiple Images

```php
public function storeBeautiService(Request $request)
{
    $files = $request->file('images');
    $paths = ImageHelper::uploadMultiple(
        $files,
        'uploads/beauti-services',
        $request->input('name')
    );

    // Lưu paths vào database
    $service = BeautiService::create([
        'name' => $request->input('name'),
        'images' => json_encode($paths)
    ]);
}
```

### 3. Tạo Thumbnail

```php
public function createThumbnails()
{
    $images = Image::where('thumbnail', null)->get();

    foreach ($images as $image) {
        $thumbnailPath = ImageHelper::createThumbnail($image->path, 300, 300);
        $image->update(['thumbnail' => $thumbnailPath]);
    }
}
```

### 4. Optimize Images

```php
public function optimizeAllImages()
{
    $images = Image::all();

    foreach ($images as $image) {
        ImageHelper::optimizeImage($image->path, 80);
    }
}
```

## Cấu hình

### 1. Thêm vào .env

```env
# Image processing settings
IMAGE_MAX_WIDTH=1920
IMAGE_MAX_HEIGHT=1080
IMAGE_QUALITY=85
IMAGE_THUMBNAIL_WIDTH=300
IMAGE_THUMBNAIL_HEIGHT=300
```

### 2. Tạo config file (optional)

```php
// config/image.php
return [
    'max_width'  => env('IMAGE_MAX_WIDTH', 1920),
    'max_height' => env('IMAGE_MAX_HEIGHT', 1080),
    'quality'    => env('IMAGE_QUALITY', 85),
    'thumbnail'  => [
        'width'  => env('IMAGE_THUMBNAIL_WIDTH', 300),
        'height' => env('IMAGE_THUMBNAIL_HEIGHT', 300),
    ],
    'directories' => [
        'users'      => 'uploads/users/avatars',
        'members'    => 'uploads/avatars',
        'stores'     => 'uploads/beauti-stores',
        'services'   => 'uploads/beauti-services',
        'news'       => 'uploads/news-articles',
        'tinymce'    => 'uploads/images',
    ]
];
```

## Testing

Chạy unit tests:

```bash
php artisan test tests/Unit/ImageServiceTest.php
```

## Lưu ý

1. **Backup**: Luôn backup thư mục `public/uploads/` khi deploy
2. **Permissions**: Đảm bảo thư mục upload có quyền ghi (755)
3. **Storage**: Có thể migrate lên cloud storage (S3) sau này
4. **Validation**: Luôn validate file trước khi upload
5. **Cleanup**: Xóa file cũ khi update để tiết kiệm dung lượng

## Troubleshooting

### Lỗi thường gặp

1. **"Không thể tạo thư mục"**

    - Kiểm tra quyền ghi của thư mục `public/uploads/`
    - Đảm bảo đường dẫn không chứa ký tự đặc biệt

2. **"File không phải là ảnh hợp lệ"**

    - Kiểm tra extension và mime type
    - Đảm bảo file không bị corrupt

3. **"Lỗi khi resize ảnh"**
    - Kiểm tra GD hoặc Imagick extension
    - Đảm bảo đủ memory để xử lý ảnh lớn

### Debug

```php
// Log image processing
Log::info('Image upload', [
    'file' => $file->getClientOriginalName(),
    'size' => $file->getSize(),
    'mime' => $file->getMimeType()
]);

// Check image info
$info = ImageHelper::getImageInfo($path);
dd($info);
```
