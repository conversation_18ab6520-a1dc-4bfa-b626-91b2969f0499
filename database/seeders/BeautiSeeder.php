<?php

namespace Database\Seeders;

use App\Models\BeautiType;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Str;

class BeautiSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
       $types = [
            [
                'name' => 'Salon Tóc',
                'status' => true,
                'position' => 1,
                'seo_title' => 'Salon Tóc Chuyên Nghiệp',
                'seo_description' => 'Các dịch vụ tóc nam nữ, cắt, uốn, nhuộm, gội đầu dưỡng sinh.',
                'seo_keywords' => 'salon toc, cat toc, uon toc, nhuom toc',
                'creator_id' => 1, 
                'editor_id' => 1,  
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'Thẩm Mỹ Viện',
                'status' => true,
                'position' => 2,
                'seo_title' => 'Thẩm <PERSON>',
                'seo_description' => 'Chuyên cung cấp các dịch vụ thẩm mỹ không phẫu thuật và phẫu thuật.',
                'seo_keywords' => 'tham my vien, nang mui, got cam, tiem filler',
                'creator_id' => 1,
                'editor_id' => 1,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'Spa Massage',
                'status' => true,
                'position' => 3,
                'seo_title' => 'Spa Massage Thư Giãn',
                'seo_description' => 'Massage body, massage đá nóng, xông hơi, chăm sóc da mặt.',
                'seo_keywords' => 'spa, massage, thu gian, cham soc da',
                'creator_id' => 1,
                'editor_id' => 1,
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ];

        foreach ($types as $type) {
            $type['slug'] = Str::slug($type['name']);
            BeautiType::create($type);
        }
    }
}
