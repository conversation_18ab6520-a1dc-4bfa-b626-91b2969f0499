<?php

namespace Database\Seeders;

use App\Models\NewsCategory;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class NewsCategorySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $parent = NewsCategory::firstOrCreate(
            ['code' => 'tin-tuc-chinh'],
            ['title' => 'Tin tức chính', 'status' => 1]
        );

        $children = [
            ['title' => 'Thời sự',   'code' => 'thoi-su'],
            ['title' => 'Góc nhìn',  'code' => 'goc-nhin'],
            ['title' => 'Công nghệ', 'code' => 'cong-nghe'],
        ];

        foreach ($children as $item) {
            NewsCategory::firstOrCreate(
                ['code' => $item['code']],
                ['title'   => $item['title'],
                 'parents' => $parent->id,
                 'status'  => 1]
            );
        }
    }
}
