<?php

namespace Database\Seeders;

use App\Models\NewsCategory;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class NewsArticleSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $categories = NewsCategory::all();
        foreach ($categories as $cat) {
            for ($i = 1; $i <= 3; $i++) {
                $title = "Bài viết {$i} trong danh mục {$cat->title}";
                $content = "Nội dung của bài viết {$i} trong danh mục {$cat->title}.";

                $cat->articles()->create([
                    'title' => $title,
                    'content' => $content,
                    'description' => "Mô tả ngắn cho bài viết {$i} trong danh mục {$cat->title}.",
                    'category_id' => $cat->id,
                    'creator_id' => 1,
                    'editor_id' => 1,
                    'published'   => now(),
                    'status' => 1,
                ]);
            }
        }
    }
}
