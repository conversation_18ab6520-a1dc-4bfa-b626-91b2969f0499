<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\ErpMember;
use Illuminate\Support\Facades\Hash;
use Spatie\Permission\Models\Role;

class ErpMemberSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $members = [
            [
                'email' => '<EMAIL>',
                'firstname' => 'Admin',
                'username' => 'admin',
                'code' => 'admin',
                'role' => 'admin',
            ],
            [
                'email' => '<EMAIL>',
                'firstname' => 'Staff',
                'username' => 'staff',
                'code' => 'staff',
                'role' => 'staff',
            ],
        ];

        foreach ($members as $data) {
            $member = ErpMember::firstOrCreate(
                ['email' => $data['email']],
                [
                    'firstname' => $data['firstname'],
                    'username' => $data['username'],
                    'code' => $data['code'],
                    'status' => true,
                ]
            );
            $member->assignRole($data['role']);
        }
    }
}
