<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

class PermissionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $permission = ['full', 'limit'];

        foreach ($permission as $key => $value) {
            Permission::create(['name' => $value, 'guard_name' => 'admin']);
        }

        $roles = [
            'admin' => ['full'],
            'staff' => ['limit'],
        ];

        foreach ($roles as $role => $perms) {
            $roleModel = Role::firstOrCreate(['name' => $role, 'guard_name' => 'admin']);
            $roleModel->syncPermissions($perms);
        }
    }
}
