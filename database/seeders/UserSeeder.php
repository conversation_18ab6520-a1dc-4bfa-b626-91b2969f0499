<?php

namespace Database\Seeders;

use Carbon\Carbon;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
          DB::table('users')->insert([
            [
                'fullname' => 'Hồ Thị Quỳnh',
                'username' => 'quynh.ho',
                'email' => '<EMAIL>',
                'address' => '123 Đường ABC, Quận 1',
                'status' => 1,
                'creator_id' => 1,
                'editor_id' => 1,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'fullname' => 'Nguyễn Văn Quang',
                'username' => 'quang.nguyen',
                'email' => '<EMAIL>',
                'address' => '123 Đường ABC, Quận 1',
                'status' => 1,
                'creator_id' => 1,
                'editor_id' => 1,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
        ]);
    }
}
