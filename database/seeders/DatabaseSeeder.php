<?php

namespace Database\Seeders;

use App\Models\SystemConfig;
use App\Models\User;
// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        $this->call(PermissionSeeder::class);
        $this->call(ErpMemberSeeder::class);
        $this->call(TeamSeeder::class);
        $this->call(UserSeeder::class);
        $this->call(SystemConfigSeeder::class);
        $this->call(NewsCategorySeeder::class);
        $this->call(NewsArticleSeeder::class);
        $this->call(NewsArticleCategorySeeder::class);
        $this->call(HostSeeder::class);
        $this->call(BeautiSeeder::class);
    }
}
