<?php

namespace Database\Seeders;

use App\Models\ErpDepartment;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class TeamSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $teams = [
            [
                'name' => 'Director',
                'code' => 'director',
                'telegram' => '-641807643',
            ],
            [
                'name' => 'Service',
                'code' => 'service',
                'telegram' => '-4082721587',
            ],
            [
                'name' => 'Kinh doanh',
                'code' => 'sales',
                'telegram' => '-4778946519',
            ],
            [
                'name' => 'Technical',
                'code' => 'technical',
                'telegram' => '-459049606',
            ],
        ];

        foreach ($teams as $data) {
            ErpDepartment::firstOrCreate(
                [
                    'name' => $data['name'],
                    'code' => $data['code'],
                    'telegram' => $data['telegram'],
                ],

            );
        }
    }
}
