<?php

namespace Database\Seeders;

use Carbon\Carbon;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class HostSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        DB::table('host')->insert([
            [
                'user_id' => 1,
                'name' => 'Nguyễn <PERSON>n A',
                'alias_name' => 'ngvana',
                'email' => '<EMAIL>',
                'phone' => '0909123456',
                'zalo' => '0909123456',
                'address' => '123 Đường ABC, Quận 1',
                'status' => 1,
                'in_charge_id' => 2, 
                'creator_id' => 1,
                'editor_id' => 1,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'user_id' => 2,
                'name' => 'Trần Thị B',
                'alias_name' => 'ttb',
                'email' => '<EMAIL>',
                'phone' => '0909988776',
                'zalo' => '0909988776',
                'address' => '456 Đường XYZ, Quận 3',
                'status' => 1,
                'in_charge_id' => 2,
                'creator_id' => 1,
                'editor_id' => 1,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'user_id' => 2,
                'name' => 'Lê Văn C',
                'alias_name' => 'lvc',
                'email' => '<EMAIL>',
                'phone' => '0911222333',
                'zalo' => '0911222333',
                'address' => '789 Đường DEF, Quận 5',
                'status' => 1,
                'in_charge_id' => 2,
                'creator_id' => 2,
                'editor_id' => 2,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
        ]);
    }
}
