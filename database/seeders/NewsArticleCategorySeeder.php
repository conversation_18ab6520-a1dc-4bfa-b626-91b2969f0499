<?php

namespace Database\Seeders;

use App\Models\NewsArticle;
use App\Models\NewsCategory;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class NewsArticleCategorySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $articles   = NewsArticle::all();
        $categories = NewsCategory::all();

        foreach ($articles as $article) {
            $randomCat = $categories->random()->id;

            DB::table('news_article_category')->insert([
                'news_id'     => $article->id,
                'category_id' => $randomCat,
                'created_at'  => now(),
                'updated_at'  => now(),
            ]);
        }
    }
}
