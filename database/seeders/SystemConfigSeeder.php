<?php

namespace Database\Seeders;

use App\Models\SystemConfig;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class SystemConfigSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
         $sysconf = [
            [
                'name' => 'Tên công ty',
                'code' => 'company',
                'data' => 'CÔNG TY TNHH TRỰC TUYẾN OHI',
            ],
            [
                'name' => 'Địa chỉ',
                'code' => 'address',
                'data' => '1/119 Hùng Vương, TP.Huế',
            ],
            [
                'name' => 'Hotline',
                'code' => 'hotline',
                'data' => '<p>033.266.1579</p>',
            ],
           
        ];

        foreach ($sysconf as $data) {
            SystemConfig::firstOrCreate(
                [
                    'name' => $data['name'],
                    'code' => $data['code'],
                    'data' => $data['data'],
                ],

            );
        }
    }
}
