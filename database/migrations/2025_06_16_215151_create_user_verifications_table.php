<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
  /**
   * Run the migrations.
   */
  public function up(): void
  {
    Schema::create('user_verifications', function (Blueprint $table) {
      $table->id();
      $table->foreignId('user_id')->constrained()->onDelete('cascade');
      $table->enum('type', ['email', 'phone']); // Loại xác thực
      $table->string('token', 255)->nullable(); // Token cho email hoặc code cho phone
      $table->timestamp('expires_at')->nullable(); // Thời gian hết hạn
      $table->timestamp('verified_at')->nullable(); // Thời gian đã xác thực
      $table->timestamps();

      // Indexes
      $table->index(['user_id', 'type']);
      $table->index(['token']);
      $table->index(['expires_at']);
      $table->index(['verified_at']);

      // Unique constraint: một user chỉ có một verification record cho mỗi type
      $table->unique(['user_id', 'type']);
    });
  }

  /**
   * Reverse the migrations.
   */
  public function down(): void
  {
    Schema::dropIfExists('user_verifications');
  }
};
