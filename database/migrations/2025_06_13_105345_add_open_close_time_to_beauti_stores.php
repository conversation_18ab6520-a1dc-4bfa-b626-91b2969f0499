<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('beauti_stores', function (Blueprint $table) {
           $table->time('opening_time')
                  ->after('status')
                  ->nullable()
                  ->comment('Giờ mở cửa (HH:MM:SS)');
            
            // Giờ đóng cửa
            $table->time('closing_time')
                  ->after('opening_time')
                  ->nullable()
                  ->comment('<PERSON>iờ đóng cửa (HH:MM:SS)');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('beauti_stores', function (Blueprint $table) {
             $table->dropColumn(['opening_time', 'closing_time']);
        });
    }
};
