<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('news_article_category', function (Blueprint $table) {
            $table->id();
            $table->timestamps();
            $table->foreignId('news_id')->nullable()
                ->constrained('news_articles')->nullOnDelete();
            $table->foreignId('category_id')->nullable()
                ->constrained('news_categories')->nullOnDelete();
            $table->index(['news_id', 'category_id'], 'i_new_article_category_2f');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('news_article_category');
    }
};
