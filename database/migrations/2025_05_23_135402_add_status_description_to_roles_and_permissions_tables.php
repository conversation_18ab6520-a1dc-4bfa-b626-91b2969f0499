<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
       Schema::table('roles', function (Blueprint $table) {
            $table->boolean('status')->default(true)->after('guard_name');
            $table->string('description')->nullable()->after('status');
        });
        Schema::table('permissions', function (Blueprint $table) {
            $table->boolean('status')->default(true)->after('guard_name');
            $table->string('description')->nullable()->after('status');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('roles', function (Blueprint $table) {
            $table->dropColumn(['status', 'description']);
        });
        Schema::table('permissions', function (Blueprint $table) {
            $table->dropColumn(['status', 'description']);
        });
    }
};
