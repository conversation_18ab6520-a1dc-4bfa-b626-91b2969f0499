<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
  /**
   * Run the migrations.
   */
  public function up(): void
  {
    // Migrate existing verification data from users table to user_verifications table
    DB::transaction(function () {
      // Migrate email verification data
      DB::statement("
                INSERT INTO user_verifications (user_id, type, token, expires_at, verified_at, created_at, updated_at)
                SELECT
                    id as user_id,
                    'email' as type,
                    email_verification_token as token,
                    NULL as expires_at,
                    email_verified_at as verified_at,
                    created_at,
                    updated_at
                FROM users
                WHERE email_verification_token IS NOT NULL OR email_verified_at IS NOT NULL
            ");

      // Migrate phone verification data
      DB::statement("
                INSERT INTO user_verifications (user_id, type, token, expires_at, verified_at, created_at, updated_at)
                SELECT
                    id as user_id,
                    'phone' as type,
                    phone_verification_code as token,
                    phone_verification_expires_at as expires_at,
                    phone_verified_at as verified_at,
                    created_at,
                    updated_at
                FROM users
                WHERE phone_verification_code IS NOT NULL OR phone_verified_at IS NOT NULL
            ");
    });

    // Remove verification columns from users table
    Schema::table('users', function (Blueprint $table) {
      $table->dropColumn([
        'email_verification_token',
        'email_verified_at',
        'phone_verification_code',
        'phone_verified_at',
        'phone_verification_expires_at',
      ]);
    });
  }

  /**
   * Reverse the migrations.
   */
  public function down(): void
  {
    // Add verification columns back to users table
    Schema::table('users', function (Blueprint $table) {
      $table->timestamp('email_verified_at')->nullable()->after('email');
      $table->string('email_verification_token', 64)->nullable()->after('email_verified_at');
      $table->timestamp('phone_verified_at')->nullable()->after('phone');
      $table->string('phone_verification_code', 6)->nullable()->after('phone_verified_at');
      $table->timestamp('phone_verification_expires_at')->nullable()->after('phone_verification_code');
    });

    // Migrate data back from user_verifications to users table
    DB::transaction(function () {
      // Migrate email verification data back
      DB::statement("
                UPDATE users u
                INNER JOIN user_verifications uv ON u.id = uv.user_id
                SET
                    u.email_verification_token = uv.token,
                    u.email_verified_at = uv.verified_at
                WHERE uv.type = 'email'
            ");

      // Migrate phone verification data back
      DB::statement("
                UPDATE users u
                INNER JOIN user_verifications uv ON u.id = uv.user_id
                SET
                    u.phone_verification_code = uv.token,
                    u.phone_verified_at = uv.verified_at,
                    u.phone_verification_expires_at = uv.expires_at
                WHERE uv.type = 'phone'
            ");
    });
  }
};
