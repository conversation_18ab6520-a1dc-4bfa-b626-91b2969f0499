<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('news_articles', function (Blueprint $table) {
            $table->id();
            $table->string('title')->nullable();
            $table->string('code')->nullable();
            $table->mediumText('description')->nullable();
            $table->mediumText('content')->nullable();
            $table->integer('hits')->default(0);
            $table->string('image')->nullable();
            $table->string('embed')->nullable();
            $table->boolean('featured')->default(0);
            $table->string('link', 250)->nullable();
            $table->string('url_target', 20)->nullable();
            $table->tinyInteger('status')->default(0);
            $table->string('seo_title', 250)->nullable();
            $table->text('seo_description')->nullable();
            $table->string('seo_keywords', 250)->nullable();
            $table->string('seo_image')->nullable();
            $table->text('tags')->nullable();
            $table->dateTime('published')->nullable();
            $table->integer('position')->nullable();
            
            $table->foreignId('creator_id')->nullable()
            ->constrained('erp_members')->nullOnDelete();
            $table->foreignId('editor_id')->nullable()
            ->constrained('erp_members')->nullOnDelete();
            $table->foreignId('category_id')->nullable()
            ->constrained('news_categories')->nullOnDelete();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('news_articles');
    }
};
