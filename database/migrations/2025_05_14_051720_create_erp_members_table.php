<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('erp_members', function (Blueprint $table) {
            $table->id();
            $table->string('fullname', 100)->default('');
            $table->string('firstname', 70)->nullable();
            $table->string('lastname', 30)->nullable();
            $table->string('username', 50);
            $table->string('password', 255)->nullable();
            $table->string('salt', 255)->nullable();
            $table->string('code', 50)->nullable();
            $table->unsignedBigInteger('department_id')->nullable();
            $table->tinyInteger('is_leader')->nullable();
            $table->string('code_position', 5)->nullable();
            $table->string('title', 20)->nullable();
            $table->string('staff_position', 50)->nullable();
            $table->string('telegram_id', 30)->nullable();
            $table->string('avatar', 255)->nullable();
            $table->string('email', 50)->nullable();
            $table->string('phone', 30)->nullable();
            $table->tinyInteger('gender')->default(0);
            $table->text('content')->nullable();
            $table->char('color', 15)->nullable();
            $table->boolean('status')->default(1);
            $table->dateTime('visited')->nullable();
            $table->tinyInteger('creator_id')->nullable();
            $table->tinyInteger('editor_id')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('erp_members');
    }
};
