<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
  /**
   * Run the migrations.
   */
  public function up(): void
  {
    Schema::create('password_reset_tokens', function (Blueprint $table) {
      $table->id();
      $table->string('email')->index();
      $table->string('token', 64)->unique();
      $table->timestamp('expires_at');
      $table->timestamp('used_at')->nullable();
      $table->timestamps();

      $table->index(['email', 'token']);
      $table->index(['token', 'used_at']);
    });
  }

  /**
   * Reverse the migrations.
   */
  public function down(): void
  {
    Schema::dropIfExists('password_reset_tokens');
  }
};
