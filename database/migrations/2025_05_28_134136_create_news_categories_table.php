<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('news_categories', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('parents')->nullable();
            $table->string('title', 100)->nullable();
            $table->string('code', 100)->nullable();
            $table->string('image')->nullable();
            $table->text('description')->nullable();
            $table->string('seo_title', 100)->nullable();
            $table->text('seo_description')->nullable();
            $table->text('seo_keywords')->nullable();
            $table->string('seo_image')->nullable();
            $table->tinyInteger('status')->default(1);
            $table->tinyInteger('creator_id')->nullable();
            $table->tinyInteger('editor_id')->nullable();
            $table->tinyInteger('position')->nullable();
            $table->timestamps();

            $table->foreign('parents')
              ->references('id')->on('news_categories')
              ->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('news_categories');
    }
};
