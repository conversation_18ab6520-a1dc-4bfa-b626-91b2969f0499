<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('beauti_stores', function (Blueprint $table) {
            $table->id();
            $table->string('name', 150)->nullable(false);
            $table->foreignId('beauti_id')->nullable()
                ->constrained('beauties')
                ->nullOnDelete();
            $table->string('slug', 150)->unique()->nullable();
            $table->text('description')->nullable();
            $table->string('logo', 255)->nullable();
            $table->string('image', 255)->nullable();
            $table->unsignedBigInteger('price_from')->nullable()->comment('Giá khởi điểm các dịch vụ tại cơ sở(VND)');;
            $table->string('street', 255)->nullable();
            $table->string('ward_code', 20)->nullable();
            $table->string('district_code', 20)->nullable();
            $table->string('province_code', 20)->nullable();
            $table->string('full_address', 255)->nullable();
            $table->boolean('status')->default(1);
            $table->tinyInteger('position')->nullable();
            $table->string('seo_title', 100)->nullable();
            $table->text('seo_description')->nullable();
            $table->text('seo_keywords')->nullable();
            $table->string('seo_image')->nullable();
            $table->foreignId('creator_id')->nullable()
                ->constrained('erp_members')->nullOnDelete();
            $table->foreignId('editor_id')->nullable()
                ->constrained('erp_members')->nullOnDelete();
            $table->timestamps();

            $table->index('name');
            $table->index('slug');
            $table->index('province_code');
            $table->index('district_code');
            $table->index('ward_code');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('beauti_stores');
    }
};
