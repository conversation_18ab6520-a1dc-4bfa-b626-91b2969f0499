<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
  /**
   * Run the migrations.
   */
  public function up(): void
  {
    Schema::create('zalo_oauth', function (Blueprint $table) {
      $table->id();
      $table->text('access_token');
      $table->text('refresh_token');
      $table->timestamp('expired_access_token');
      $table->timestamp('expired_refresh_token');
      $table->timestamp('created');
    });
  }

  /**
   * Reverse the migrations.
   */
  public function down(): void
  {
    Schema::dropIfExists('zalo_oauth');
  }
};
