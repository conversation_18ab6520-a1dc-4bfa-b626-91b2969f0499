<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('beauti_type_service', function (Blueprint $table) {
            $table->unsignedBigInteger('type_id');
            $table->unsignedBigInteger('service_id');

            $table->timestamps();

            $table->primary(['type_id', 'service_id']);

            $table->index('service_id', 'idx_ts_service');

            $table->foreign('type_id')
                  ->references('id')->on('beauti_types')
                  ->onDelete('cascade')
                  ->onUpdate('cascade');

            $table->foreign('service_id')
                  ->references('id')->on('beauti_services')
                  ->onDelete('cascade')
                  ->onUpdate('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('type_service');
    }
};
