<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('beauties', function (Blueprint $table) {
            $table->id();
            $table->string('name', 255)->nullable(false);
            $table->foreignId('user_id')->nullable()
                ->constrained('users')
                ->nullOnDelete();
                  $table->string('address', 255)->nullable(); 
            $table->string('phone', 50)->nullable();  
            $table->string('fanpage', 255)->nullable(); 
            $table->string('tiktok', 255)->nullable();
            $table->string('website', 100)->nullable();
            $table->string('email', 50)->nullable();    
            $table->text('description')->nullable();    
            $table->boolean('status')->default(1);
            $table->foreignId('creator_id')->nullable()
                ->constrained('erp_members')->nullOnDelete();
            $table->foreignId('editor_id')->nullable()
                ->constrained('erp_members')->nullOnDelete();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('beautis');
    }
};
