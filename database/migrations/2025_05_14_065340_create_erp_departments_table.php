<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('erp_departments', function (Blueprint $table) {
            $table->id();
            $table->string('name', 50)->nullable();
            $table->string('code', 50)->nullable();
            $table->unsignedBigInteger('manager_id')->nullable();
            $table->text('content')->nullable();
            $table->char('color', 15)->nullable();
            $table->string('telegram', 30)->nullable();
            $table->boolean('status')->default(1);
            $table->unsignedBigInteger('creator_id')->nullable();
            $table->unsignedBigInteger('editor_id')->nullable();
            $table->tinyInteger('position')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('erp_departments');
    }
};
