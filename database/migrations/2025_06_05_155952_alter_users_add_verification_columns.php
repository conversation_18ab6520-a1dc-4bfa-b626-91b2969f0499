<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->timestamp('email_verified_at')
                ->nullable()
                ->after('email');
            $table->string('email_verification_token', 64)
                ->nullable()
                ->after('email_verified_at');
            $table->timestamp('phone_verified_at')
                ->nullable()
                ->after('phone');
            $table->string('phone_verification_code', 6)
                ->nullable()
                ->after('phone_verified_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn([
                'email_verified_at',
                'email_verification_token',
                'phone_verified_at',
                'phone_verification_code',
            ]);
        });
    }
};
