<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('user_tokens', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->string('token_type')->default('refresh_token'); // refresh_token, access_token
            $table->text('token'); // Hashed token
            $table->string('jti')->unique(); // JWT ID để liên kết với JWT
            $table->timestamp('expires_at');
            $table->string('device_name')->nullable(); // Tên thiết bị
            $table->string('ip_address', 45)->nullable(); // IP address
            $table->string('user_agent')->nullable(); // User agent
            $table->timestamp('last_used_at')->nullable();
            $table->timestamps();

            // Indexes
            $table->index(['user_id', 'token_type']);
            $table->index(['expires_at']);
            $table->index(['jti']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('user_tokens');
    }
};
