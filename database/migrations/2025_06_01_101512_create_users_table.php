<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('users', function (Blueprint $table) {
            $table->id();
            $table->string('fullname', 255)->nullable();
            $table->string('username', 45)->nullable()->unique();
            $table->string('email', 45)->nullable()->unique();
            $table->string('phone', 30)->nullable();
            $table->string('password', 255)->nullable();
            $table->string('salt', 255)->nullable();
            $table->string('address', 255)->nullable();
            $table->boolean('status')->default(1);
            $table->foreignId('creator_id')->nullable()
                ->constrained('erp_members')->nullOnDelete();
            $table->foreignId('editor_id')->nullable()
                ->constrained('erp_members')->nullOnDelete();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('users');
    }
};
