<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('beauti_store_type', function (Blueprint $table) {
            $table->foreignId('store_id')->constrained('beauti_stores')->onDelete('cascade');
            $table->foreignId('type_id')->constrained('beauti_types')->onDelete('cascade');
            $table->timestamps();

             $table->primary(['store_id', 'type_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('beauti_store_type');
    }
};
