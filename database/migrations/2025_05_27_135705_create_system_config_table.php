<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('system_config', function (Blueprint $table) {
            $table->id();
            $table->string('name')->nullable();
            $table->string('code')->default('');
            $table->string('title')->nullable();
            $table->string('description')->nullable();
            $table->text('data')->nullable();
            $table->string('image')->nullable();
            $table->text('link')->nullable();
            $table->char('url_target', 10)->nullable();
            $table->boolean('status')->default(1);
            $table->unsignedBigInteger('creator_id')->nullable();
            $table->unsignedBigInteger('editor_id')->nullable();
            $table->timestamps();

            $table->foreign('creator_id')
                ->references('id')->on('erp_members')
                ->onDelete('set null');
            $table->foreign('editor_id')
                ->references('id')->on('erp_members')
                ->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('system_config');
    }
};
