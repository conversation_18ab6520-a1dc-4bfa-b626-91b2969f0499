<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('beauti_store_packages', function (Blueprint $table) {
            $table->id();
            $table->foreignId('store_service_id')->constrained('beauti_store_services')->onDelete('cascade');
            $table->string('name', 200)->nullable(false);
            $table->string('slug', 150)->unique()->nullable();
            $table->unsignedInteger('duration_days')->nullable();
            $table->unsignedInteger('warranty_days')->nullable();
            $table->unsignedBigInteger('price_old')->nullable(false);
            $table->unsignedBigInteger('price_new')->nullable(false);
            $table->text('description')->nullable();
            $table->boolean('status')->default(1);
            $table->string('seo_title', 100)->nullable();
            $table->text('seo_description')->nullable();
            $table->text('seo_keywords')->nullable();
            $table->string('seo_image')->nullable();
            $table->foreignId('creator_id')->nullable()
                ->constrained('erp_members')->nullOnDelete();
            $table->foreignId('editor_id')->nullable()
                ->constrained('erp_members')->nullOnDelete();
            $table->timestamps();

            $table->index('status', 'idx_store_packages_status');
            $table->index('store_service_id', 'idx_store_packages_store_service');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('beauti_store_packages');
    }
};
