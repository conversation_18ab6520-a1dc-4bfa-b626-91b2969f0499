<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('beauti_store_services', function (Blueprint $table) {
            $table->id();
            $table->foreignId('store_id')->constrained('beauti_stores')->onDelete('cascade');
            $table->foreignId('service_id')->constrained('beauti_services')->onDelete('cascade');
            $table->unsignedBigInteger('price_from')->nullable()->comment('Giá khởi điểm dịch vụ (VND)');;
            $table->boolean('status')->default(1);
            $table->timestamps();

            $table->unique(['store_id', 'service_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('beauti_store_services');
    }
};
