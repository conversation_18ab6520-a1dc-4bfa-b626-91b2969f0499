<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('erp_members', function (Blueprint $table) {
            $table->foreign('department_id')
                ->references('id')->on('erp_departments')
                ->onDelete('set null');
        });

        Schema::table('erp_departments', function (Blueprint $table) {
            $table->foreign('manager_id')
                ->references('id')->on('erp_members')
                ->onDelete('set null');
            $table->foreign('creator_id')
                ->references('id')->on('erp_members')
                ->onDelete('set null');
            $table->foreign('editor_id')
                ->references('id')->on('erp_members')
                ->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('erp_tables', function (Blueprint $table) {
            //
        });
    }
};
