<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('host', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->nullable()
                ->constrained('users')
                ->nullOnDelete();
            $table->string('name')->nullable();
            $table->string('alias_name', 20)->nullable();
            $table->string('email', 50)->nullable();
            $table->string('phone', 50)->nullable();
            $table->string('zalo', 50)->nullable();
            $table->string('address')->nullable();
            $table->boolean('status')->default(1);
            $table->unsignedBigInteger('in_charge_id')->nullable();
            $table->text('description')->nullable();
            $table->foreignId('creator_id')->nullable()
                ->constrained('erp_members')->nullOnDelete();
            $table->foreignId('editor_id')->nullable()
                ->constrained('erp_members')->nullOnDelete();
            $table->timestamps();

            $table->unique(['user_id', 'id'], 'idx_id');
            $table->index(['name', 'phone', 'zalo'], 'idx_fulltext');
            $table->foreign('in_charge_id')->references('id')->on('erp_members')->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('host');
    }
};
