<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="18" height="18" viewBox="0 0 18 18">
  <defs>
    <path id="rotate-a" d="M5.505,4.8075 L0.645,9.675 L5.5125,14.535 L10.38,9.675 L5.505,4.8075 Z M2.7675,9.675 L5.5125,6.93 L8.25,9.675 L5.505,12.42 L2.7675,9.675 Z M14.52,4.98 C13.2075,3.66 11.475,3 9.75,3 L9.75,0.57 L6.57,3.75 L9.75,6.93 L9.75,4.5 C11.0925,4.5 12.435,5.01 13.4625,6.0375 C15.51,8.085 15.51,11.415 13.4625,13.4625 C12.435,14.49 11.0925,15 9.75,15 C9.0225,15 8.295,14.8425 7.62,14.5425 L6.5025,15.66 C7.515,16.215 8.6325,16.5 9.75,16.5 C11.475,16.5 13.2075,15.84 14.52,14.52 C17.16,11.8875 17.16,7.6125 14.52,4.98 Z"/>
  </defs>
  <g fill="none" fill-rule="evenodd">
    <polygon points="0 0 18 0 18 18 0 18"/>
    <mask id="rotate-b" fill="#fff">
      <use xlink:href="#rotate-a"/>
    </mask>
    <use fill="#000" fill-rule="nonzero" xlink:href="#rotate-a"/>
    <g fill="#3B474E" mask="url(#rotate-b)">
      <polygon points="0 0 18 0 18 18 0 18"/>
    </g>
  </g>
</svg>
