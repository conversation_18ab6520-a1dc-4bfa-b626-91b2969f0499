<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg width="16px" height="16px" viewBox="0 0 16 16" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:sketch="http://www.bohemiancoding.com/sketch/ns">
    <!-- Generator: Sketch 3.2.2 (9983) - http://www.bohemiancoding.com/sketch -->
    <title>sepia-black</title>
    <desc>Created with Sketch.</desc>
    <defs>
        <linearGradient x1="60.0119231%" y1="-2.28317149%" x2="60.0119231%" y2="97.3736075%" id="linearGradient-1">
            <stop stop-color="#666666" offset="0%"></stop>
            <stop stop-color="#3F3F3F" offset="100%"></stop>
        </linearGradient>
        <filter x="-50%" y="-50%" width="200%" height="200%" filterUnits="objectBoundingBox" id="filter-2">
            <feOffset dx="0" dy="1" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="0" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 0.7 0" in="shadowBlurOuter1" type="matrix" result="shadowMatrixOuter1"></feColorMatrix>
            <feOffset dx="0" dy="1" in="SourceAlpha" result="shadowOffsetInner1"></feOffset>
            <feGaussianBlur stdDeviation="0" in="shadowOffsetInner1" result="shadowBlurInner1"></feGaussianBlur>
            <feComposite in="shadowBlurInner1" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner1"></feComposite>
            <feColorMatrix values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0  0 0 0 0.613768116 0" in="shadowInnerInner1" type="matrix" result="shadowMatrixInner1"></feColorMatrix>
            <feMerge>
                <feMergeNode in="shadowMatrixOuter1"></feMergeNode>
                <feMergeNode in="SourceGraphic"></feMergeNode>
                <feMergeNode in="shadowMatrixInner1"></feMergeNode>
            </feMerge>
        </filter>
        <path d="M7.90628052,0.833743479 C8.31172399,0.550018699 8.81808809,0.316775213 9.38715754,0.31677519 C10.8396653,0.316775133 12.0315654,1.71683735 12.292173,1.83632819 C13.8354625,2.54393979 15.015625,0.942473531 15.015625,0.942473531 C15.015625,0.942473531 14.4709346,3.35588134 12.292173,4.29442882 C10.8819102,4.90192926 9.03424916,3.69312219 7.90628052,2.76479353 C6.77831187,3.69312219 4.93065085,4.90192926 3.52038803,4.29442882 C1.34162643,3.35588134 0.796936035,0.942473531 0.796936035,0.942473531 C0.796936035,0.942473531 1.97709857,2.54393979 3.52038803,1.83632819 C3.78099564,1.71683735 4.97289576,0.316775133 6.42540349,0.31677519 C6.99447295,0.316775213 7.50083705,0.550018699 7.90628052,0.833743479 Z" id="path-3"></path>
        <filter x="-50%" y="-50%" width="200%" height="200%" filterUnits="objectBoundingBox" id="filter-4">
            <feOffset dx="0" dy="1" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="0" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 0.7 0" in="shadowBlurOuter1" type="matrix" result="shadowMatrixOuter1"></feColorMatrix>
            <feOffset dx="0" dy="1" in="SourceAlpha" result="shadowOffsetInner1"></feOffset>
            <feGaussianBlur stdDeviation="0" in="shadowOffsetInner1" result="shadowBlurInner1"></feGaussianBlur>
            <feComposite in="shadowBlurInner1" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner1"></feComposite>
            <feColorMatrix values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0  0 0 0 0.613768116 0" in="shadowInnerInner1" type="matrix" result="shadowMatrixInner1"></feColorMatrix>
            <feMerge>
                <feMergeNode in="shadowMatrixOuter1"></feMergeNode>
                <feMergeNode in="SourceGraphic"></feMergeNode>
                <feMergeNode in="shadowMatrixInner1"></feMergeNode>
            </feMerge>
        </filter>
        <path d="M10,5 L10.9975267,5 C11.544239,5 12,5.44771525 12,6 C12,6.55613518 11.5511774,7 10.9975267,7 L1.00247329,7 C0.455760956,7 0,6.55228475 0,6 C0,5.44386482 0.448822582,5 1.00247329,5 L2,5 L2,1.00292933 C2,0.437881351 2.4463856,0 2.99703014,0 L9.00296986,0 C9.54696369,0 10,0.449026756 10,1.00292933 L10,5 Z" id="path-5"></path>
    </defs>
    <g id="icons-moono" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" sketch:type="MSPage">
        <g id="sepia" sketch:type="MSArtboardGroup">
            <g sketch:type="MSLayerGroup" transform="translate(0.000000, 2.000000)">
                <g id="stache" transform="translate(0.000000, 8.000000)" filter="url(#filter-2)">
                    <g id="Path-33">
                        <use fill="url(#linearGradient-1)" fill-rule="evenodd" sketch:type="MSShapeGroup" xlink:href="#path-3"></use>
                        <use fill="none" xlink:href="#path-3"></use>
                        <use fill="none" xlink:href="#path-3"></use>
                    </g>
                </g>
                <g id="tophat" transform="translate(2.000000, 0.000000)" filter="url(#filter-4)">
                    <g id="Rectangle-164">
                        <use fill="url(#linearGradient-1)" fill-rule="evenodd" sketch:type="MSShapeGroup" xlink:href="#path-5"></use>
                        <use fill="none" xlink:href="#path-5"></use>
                        <use fill="none" xlink:href="#path-5"></use>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>