<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg width="16px" height="16px" viewBox="0 0 16 16" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:sketch="http://www.bohemiancoding.com/sketch/ns">
    <!-- Generator: Sketch 3.2.2 (9983) - http://www.bohemiancoding.com/sketch -->
    <title>rotate-black</title>
    <desc>Created with Sketch.</desc>
    <defs>
        <linearGradient x1="60.0119231%" y1="-2.28317149%" x2="60.0119231%" y2="97.3736075%" id="linearGradient-1">
            <stop stop-color="#666666" offset="0%"></stop>
            <stop stop-color="#3F3F3F" offset="100%"></stop>
        </linearGradient>
        <filter x="-50%" y="-50%" width="200%" height="200%" filterUnits="objectBoundingBox" id="filter-2">
            <feOffset dx="0" dy="1" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="0" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 0.7 0" in="shadowBlurOuter1" type="matrix" result="shadowMatrixOuter1"></feColorMatrix>
            <feOffset dx="0" dy="1" in="SourceAlpha" result="shadowOffsetInner1"></feOffset>
            <feGaussianBlur stdDeviation="0" in="shadowOffsetInner1" result="shadowBlurInner1"></feGaussianBlur>
            <feComposite in="shadowBlurInner1" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner1"></feComposite>
            <feColorMatrix values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0  0 0 0 0.613768116 0" in="shadowInnerInner1" type="matrix" result="shadowMatrixInner1"></feColorMatrix>
            <feMerge>
                <feMergeNode in="shadowMatrixOuter1"></feMergeNode>
                <feMergeNode in="SourceGraphic"></feMergeNode>
                <feMergeNode in="shadowMatrixInner1"></feMergeNode>
            </feMerge>
        </filter>
        <path d="M7,16 C10.8659932,16 14,12.8659932 14,9 C14,7.44551746 13.4933017,6.00938065 12.6360583,4.84774279 L12.6360583,4.84774279 L10.6837588,7.43853179 C10.8873701,7.9182828 11,8.44598834 11,9 C11,11.209139 9.209139,13 7,13 L7,11.9375 L2.01222307,14.9559417 L7,17.5671674 L7,16 L7,16 Z M7.01222307,2.00001044 C7.00814953,2.00000348 7.00407517,2 7,2 C3.13400675,2 0,5.13400675 0,9 C0,10.6659955 0.582003244,12.1960569 1.55378251,13.3979569 L3.46131216,10.8665796 C3.16677278,10.3093424 3,9.67414713 3,9 C3,6.790861 4.790861,5 7,5 C7.00407578,5 7.00815014,5.0000061 7.01222307,5.00001828 L7.01222307,6.0312499 L12,2.95594168 L7.01222307,0.344715973 L7.01222307,2.00001044 L7.01222307,2.00001044 Z" id="path-3"></path>
    </defs>
    <g id="icons-moono" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" sketch:type="MSPage">
        <g id="rotate" sketch:type="MSArtboardGroup" filter="url(#filter-2)">
            <g sketch:type="MSLayerGroup" transform="translate(1.000000, -1.000000)" id="Oval-8">
                <g>
                    <g id="Shape">
                        <use fill="url(#linearGradient-1)" fill-rule="evenodd" sketch:type="MSShapeGroup" xlink:href="#path-3"></use>
                        <use fill="none" xlink:href="#path-3"></use>
                        <use fill="none" xlink:href="#path-3"></use>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>