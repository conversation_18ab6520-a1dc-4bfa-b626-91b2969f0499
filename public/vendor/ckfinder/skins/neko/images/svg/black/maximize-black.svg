<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg width="16px" height="16px" viewBox="0 0 16 16" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:sketch="http://www.bohemiancoding.com/sketch/ns">
    <!-- Generator: Sketch 3.2.2 (9983) - http://www.bohemiancoding.com/sketch -->
    <title>maximize-black</title>
    <desc>Created with Sketch.</desc>
    <defs>
        <linearGradient x1="60.0119231%" y1="-2.28317149%" x2="60.0119231%" y2="97.3736075%" id="linearGradient-1">
            <stop stop-color="#666666" offset="0%"></stop>
            <stop stop-color="#3F3F3F" offset="100%"></stop>
        </linearGradient>
        <filter x="-50%" y="-50%" width="200%" height="200%" filterUnits="objectBoundingBox" id="filter-2">
            <feOffset dx="0" dy="1" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="0" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 0.7 0" in="shadowBlurOuter1" type="matrix" result="shadowMatrixOuter1"></feColorMatrix>
            <feOffset dx="0" dy="1" in="SourceAlpha" result="shadowOffsetInner1"></feOffset>
            <feGaussianBlur stdDeviation="0" in="shadowOffsetInner1" result="shadowBlurInner1"></feGaussianBlur>
            <feComposite in="shadowBlurInner1" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner1"></feComposite>
            <feColorMatrix values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0  0 0 0 0.613768116 0" in="shadowInnerInner1" type="matrix" result="shadowMatrixInner1"></feColorMatrix>
            <feMerge>
                <feMergeNode in="shadowMatrixOuter1"></feMergeNode>
                <feMergeNode in="SourceGraphic"></feMergeNode>
                <feMergeNode in="shadowMatrixInner1"></feMergeNode>
            </feMerge>
        </filter>
        <path d="M4,8 L4,9 L3,9 L3,10 L2,10 L2,10.875 L1.5,10.375 L0,9 L0,14 L5,14 L3.5,12.375 L3.125,12 L4,12 L4,11 L5,11 L5,10 L6,10 L6,8 L4,8 L4,8 Z M4,6 L4,5 L3,5 L3,4 L2,4 L2,3.125 L1.5,3.625 L0,5 L0,0 L5,0 L3.5,1.625 L3.125,2 L4,2 L4,3 L5,3 L5,4 L6,4 L6,6 L4,6 Z M10,6 L10,5 L11,5 L11,4 L12,4 L12,3.125 L12.5,3.625 L14,5 L14,0 L9,0 L10.5,1.625 L10.875,2 L10,2 L10,3 L9,3 L9,4 L8,4 L8,6 L10,6 Z M10,8 L10,9 L11,9 L11,10 L12,10 L12,10.875 L12.5,10.375 L14,9 L14,14 L9,14 L10.5,12.375 L10.875,12 L10,12 L10,11 L9,11 L9,10 L8,10 L8,8 L10,8 Z" id="path-3"></path>
    </defs>
    <g id="icons-moono" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" sketch:type="MSPage">
        <g id="maximize" sketch:type="MSArtboardGroup" filter="url(#filter-2)">
            <g id="Group" sketch:type="MSLayerGroup" transform="translate(1.000000, 1.000000)">
                <g id="path4744">
                    <use fill="url(#linearGradient-1)" fill-rule="evenodd" sketch:type="MSShapeGroup" xlink:href="#path-3"></use>
                    <use fill="none" xlink:href="#path-3"></use>
                    <use fill="none" xlink:href="#path-3"></use>
                </g>
            </g>
        </g>
    </g>
</svg>