<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg width="16px" height="16px" viewBox="0 0 16 16" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:sketch="http://www.bohemiancoding.com/sketch/ns">
    <!-- Generator: Sketch 3.2.2 (9983) - http://www.bohemiancoding.com/sketch -->
    <title>brightness-black</title>
    <desc>Created with Sketch.</desc>
    <defs>
        <linearGradient x1="60.0119231%" y1="-2.28317149%" x2="60.0119231%" y2="97.3736075%" id="linearGradient-1">
            <stop stop-color="#666666" offset="0%"></stop>
            <stop stop-color="#3F3F3F" offset="100%"></stop>
        </linearGradient>
        <filter x="-50%" y="-50%" width="200%" height="200%" filterUnits="objectBoundingBox" id="filter-2">
            <feOffset dx="0" dy="1" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="0" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 0.7 0" in="shadowBlurOuter1" type="matrix" result="shadowMatrixOuter1"></feColorMatrix>
            <feOffset dx="0" dy="1" in="SourceAlpha" result="shadowOffsetInner1"></feOffset>
            <feGaussianBlur stdDeviation="0" in="shadowOffsetInner1" result="shadowBlurInner1"></feGaussianBlur>
            <feComposite in="shadowBlurInner1" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner1"></feComposite>
            <feColorMatrix values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0  0 0 0 0.613768116 0" in="shadowInnerInner1" type="matrix" result="shadowMatrixInner1"></feColorMatrix>
            <feMerge>
                <feMergeNode in="shadowMatrixOuter1"></feMergeNode>
                <feMergeNode in="SourceGraphic"></feMergeNode>
                <feMergeNode in="shadowMatrixInner1"></feMergeNode>
            </feMerge>
        </filter>
        <path d="M7,10 C8.65685425,10 10,8.65685425 10,7 C10,5.34314575 8.65685425,4 7,4 C5.34314575,4 4,5.34314575 4,7 C4,8.65685425 5.34314575,10 7,10 Z M3.56139799,2.64718443 C3.9515148,3.03730124 3.95390713,3.66741322 3.56066017,4.06066017 C3.17013588,4.45118446 2.54054527,4.45475883 2.14718443,4.06139799 L1.43860201,3.35281557 C1.0484852,2.96269876 1.04609287,2.33258678 1.43933983,1.93933983 C1.82986412,1.54881554 2.45945473,1.54524117 2.85281557,1.93860201 L3.56139799,2.64718443 Z M12.561398,10.6471844 C12.9515148,11.0373012 12.9539071,11.6674132 12.5606602,12.0606602 C12.1701359,12.4511845 11.5405453,12.4547588 11.1471844,12.061398 L10.438602,11.3528156 C10.0484852,10.9626988 10.0460929,10.3325868 10.4393398,9.93933983 C10.8298641,9.54881554 11.4594547,9.54524117 11.8528156,9.93860201 L12.561398,10.6471844 Z M1.43860201,10.6471844 C1.0484852,11.0373012 1.04609287,11.6674132 1.43933983,12.0606602 C1.82986412,12.4511845 2.45945473,12.4547588 2.85281557,12.061398 L3.56139799,11.3528156 C3.9515148,10.9626988 3.95390713,10.3325868 3.56066017,9.93933983 C3.17013588,9.54881554 2.54054527,9.54524117 2.14718443,9.93860201 L1.43860201,10.6471844 Z M10.438602,2.64718443 C10.0484852,3.03730124 10.0460929,3.66741322 10.4393398,4.06066017 C10.8298641,4.45118446 11.4594547,4.45475883 11.8528156,4.06139799 L12.561398,3.35281557 C12.9515148,2.96269876 12.9539071,2.33258678 12.5606602,1.93933983 C12.1701359,1.54881554 11.5405453,1.54524117 11.1471844,1.93860201 L10.438602,2.64718443 Z M11.9989566,6 C11.4472481,6 11,6.44386482 11,7 C11,7.55228475 11.4426603,8 11.9989566,8 L13.0010434,8 C13.5527519,8 14,7.55613518 14,7 C14,6.44771525 13.5573397,6 13.0010434,6 L11.9989566,6 Z M0.998956561,6 C0.447248087,6 0,6.44386482 0,7 C0,7.55228475 0.442660332,8 0.998956561,8 L2.00104344,8 C2.55275191,8 3,7.55613518 3,7 C3,6.44771525 2.55733967,6 2.00104344,6 L0.998956561,6 Z M7,11 C6.44771525,11 6,11.4426603 6,11.9989566 L6,13.0010434 C6,13.5527519 6.44386482,14 7,14 C7.55228475,14 8,13.5573397 8,13.0010434 L8,11.9989566 C8,11.4472481 7.55613518,11 7,11 Z M7,0 C6.44771525,0 6,0.442660332 6,0.998956561 L6,2.00104344 C6,2.55275191 6.44386482,3 7,3 C7.55228475,3 8,2.55733967 8,2.00104344 L8,0.998956561 C8,0.447248087 7.55613518,0 7,0 Z" id="path-3"></path>
    </defs>
    <g id="icons-moono" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" sketch:type="MSPage">
        <g id="brightness" sketch:type="MSArtboardGroup" filter="url(#filter-2)">
            <g sketch:type="MSLayerGroup" transform="translate(1.000000, 1.000000)" id="Oval-96">
                <g>
                    <use fill="url(#linearGradient-1)" fill-rule="evenodd" sketch:type="MSShapeGroup" xlink:href="#path-3"></use>
                    <use fill="none" xlink:href="#path-3"></use>
                    <use fill="none" xlink:href="#path-3"></use>
                </g>
            </g>
        </g>
    </g>
</svg>