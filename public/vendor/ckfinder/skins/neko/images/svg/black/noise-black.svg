<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg width="16px" height="16px" viewBox="0 0 16 16" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:sketch="http://www.bohemiancoding.com/sketch/ns">
    <!-- Generator: Sketch 3.2.2 (9983) - http://www.bohemiancoding.com/sketch -->
    <title>noise-black</title>
    <desc>Created with Sketch.</desc>
    <defs>
        <linearGradient x1="60.0119231%" y1="-2.28317149%" x2="60.0119231%" y2="97.3736075%" id="linearGradient-1">
            <stop stop-color="#666666" offset="0%"></stop>
            <stop stop-color="#3F3F3F" offset="100%"></stop>
        </linearGradient>
        <filter x="-50%" y="-50%" width="200%" height="200%" filterUnits="objectBoundingBox" id="filter-2">
            <feOffset dx="0" dy="1" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="0" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 0.7 0" in="shadowBlurOuter1" type="matrix" result="shadowMatrixOuter1"></feColorMatrix>
            <feOffset dx="0" dy="1" in="SourceAlpha" result="shadowOffsetInner1"></feOffset>
            <feGaussianBlur stdDeviation="0" in="shadowOffsetInner1" result="shadowBlurInner1"></feGaussianBlur>
            <feComposite in="shadowBlurInner1" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner1"></feComposite>
            <feColorMatrix values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0  0 0 0 0.613768116 0" in="shadowInnerInner1" type="matrix" result="shadowMatrixInner1"></feColorMatrix>
            <feMerge>
                <feMergeNode in="shadowMatrixOuter1"></feMergeNode>
                <feMergeNode in="SourceGraphic"></feMergeNode>
                <feMergeNode in="shadowMatrixInner1"></feMergeNode>
            </feMerge>
        </filter>
        <path d="M1.00684547,0 C0.450780073,0 0,0.455760956 0,1.00247329 L0,10.9975267 C0,11.5511774 0.449948758,12 1.00684547,12 L12.9931545,12 C13.5492199,12 14,11.544239 14,10.9975267 L14,1.00247329 C14,0.448822582 13.5500512,0 12.9931545,0 L1.00684547,0 Z M1,1 L1,11 L13,11 L13,1 L1,1 Z M2,2 L2,3 L3,3 L3,2 L2,2 Z M3,4 L3,5 L4,5 L4,4 L3,4 Z M4,5 L4,6 L5,6 L5,5 L4,5 Z M2,7 L2,8 L3,8 L3,7 L2,7 Z M3,9 L3,10 L4,10 L4,9 L3,9 Z M5,8 L5,9 L6,9 L6,8 L5,8 Z M6,9 L6,10 L7,10 L7,9 L6,9 Z M2,5 L2,6 L3,6 L3,5 L2,5 Z M5,6 L5,7 L6,7 L6,6 L5,6 Z M5,3 L5,4 L6,4 L6,3 L5,3 Z M8,8 L8,9 L9,9 L9,8 L8,8 Z M7,5 L7,6 L8,6 L8,5 L7,5 Z M9,3 L9,4 L10,4 L10,3 L9,3 Z M7,2 L7,3 L8,3 L8,2 L7,2 Z M10,5 L10,6 L11,6 L11,5 L10,5 Z M11,3 L11,4 L12,4 L12,3 L11,3 Z M9,6 L9,7 L10,7 L10,6 L9,6 Z M10,7 L10,8 L11,8 L11,7 L10,7 Z M9,9 L9,10 L10,10 L10,9 L9,9 Z M11,9 L11,10 L12,10 L12,9 L11,9 Z M11,6 L11,7 L12,7 L12,6 L11,6 Z" id="path-3"></path>
    </defs>
    <g id="icons-moono" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" sketch:type="MSPage">
        <g id="noise" sketch:type="MSArtboardGroup" filter="url(#filter-2)">
            <g sketch:type="MSLayerGroup" transform="translate(1.000000, 2.000000)" id="Rectangle-45">
                <g>
                    <use fill="url(#linearGradient-1)" fill-rule="evenodd" sketch:type="MSShapeGroup" xlink:href="#path-3"></use>
                    <use fill="none" xlink:href="#path-3"></use>
                    <use fill="none" xlink:href="#path-3"></use>
                </g>
            </g>
        </g>
    </g>
</svg>