<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg width="16px" height="16px" viewBox="0 0 16 16" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:sketch="http://www.bohemiancoding.com/sketch/ns">
    <!-- Generator: Sketch 3.2.2 (9983) - http://www.bohemiancoding.com/sketch -->
    <title>minimize-black</title>
    <desc>Created with Sketch.</desc>
    <defs>
        <linearGradient x1="60.0119231%" y1="-2.28317149%" x2="60.0119231%" y2="97.3736075%" id="linearGradient-1">
            <stop stop-color="#666666" offset="0%"></stop>
            <stop stop-color="#3F3F3F" offset="100%"></stop>
        </linearGradient>
        <filter x="-50%" y="-50%" width="200%" height="200%" filterUnits="objectBoundingBox" id="filter-2">
            <feOffset dx="0" dy="1" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="0" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 0.7 0" in="shadowBlurOuter1" type="matrix" result="shadowMatrixOuter1"></feColorMatrix>
            <feOffset dx="0" dy="1" in="SourceAlpha" result="shadowOffsetInner1"></feOffset>
            <feGaussianBlur stdDeviation="0" in="shadowOffsetInner1" result="shadowBlurInner1"></feGaussianBlur>
            <feComposite in="shadowBlurInner1" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner1"></feComposite>
            <feColorMatrix values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0  0 0 0 0.613768116 0" in="shadowInnerInner1" type="matrix" result="shadowMatrixInner1"></feColorMatrix>
            <feMerge>
                <feMergeNode in="shadowMatrixOuter1"></feMergeNode>
                <feMergeNode in="SourceGraphic"></feMergeNode>
                <feMergeNode in="shadowMatrixInner1"></feMergeNode>
            </feMerge>
        </filter>
        <path d="M2,14 L2,13 L3,13 L3,12 L4,12 L4,11.125 L4.5,11.625 L6,13 L6,8 L1,8 L2.5,9.625 L2.875,10 L2,10 L2,11 L1,11 L1,12 L0,12 L0,14 L2,14 L2,14 Z M2,0 L2,1 L3,1 L3,2 L4,2 L4,2.875 L4.5,2.375 L6,1 L6,6 L1,6 L2.5,4.375 L2.875,4 L2,4 L2,3 L1,3 L1,2 L0,2 L0,0 L2,0 Z M12,0 L12,1 L11,1 L11,2 L10,2 L10,2.875 L9.5,2.375 L8,1 L8,6 L13,6 L11.5,4.375 L11.125,4 L12,4 L12,3 L13,3 L13,2 L14,2 L14,0 L12,0 Z M12,14 L12,13 L11,13 L11,12 L10,12 L10,11.125 L9.5,11.625 L8,13 L8,8 L13,8 L11.5,9.625 L11.125,10 L12,10 L12,11 L13,11 L13,12 L14,12 L14,14 L12,14 Z" id="path-3"></path>
    </defs>
    <g id="icons-moono" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" sketch:type="MSPage">
        <g id="minimize" sketch:type="MSArtboardGroup" filter="url(#filter-2)">
            <g id="Group" sketch:type="MSLayerGroup" transform="translate(1.000000, 1.000000)">
                <g id="path4744">
                    <use fill="url(#linearGradient-1)" fill-rule="evenodd" sketch:type="MSShapeGroup" xlink:href="#path-3"></use>
                    <use fill="none" xlink:href="#path-3"></use>
                    <use fill="none" xlink:href="#path-3"></use>
                </g>
            </g>
        </g>
    </g>
</svg>