<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg width="16px" height="16px" viewBox="0 0 16 16" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:sketch="http://www.bohemiancoding.com/sketch/ns">
    <!-- Generator: Sketch 3.2.2 (9983) - http://www.bohemiancoding.com/sketch -->
    <title>clip-black</title>
    <desc>Created with Sketch.</desc>
    <defs>
        <linearGradient x1="60.0119231%" y1="-2.28317149%" x2="60.0119231%" y2="97.3736075%" id="linearGradient-1">
            <stop stop-color="#666666" offset="0%"></stop>
            <stop stop-color="#3F3F3F" offset="100%"></stop>
        </linearGradient>
        <filter x="-50%" y="-50%" width="200%" height="200%" filterUnits="objectBoundingBox" id="filter-2">
            <feOffset dx="0" dy="1" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="0" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 0.7 0" in="shadowBlurOuter1" type="matrix" result="shadowMatrixOuter1"></feColorMatrix>
            <feOffset dx="0" dy="1" in="SourceAlpha" result="shadowOffsetInner1"></feOffset>
            <feGaussianBlur stdDeviation="0" in="shadowOffsetInner1" result="shadowBlurInner1"></feGaussianBlur>
            <feComposite in="shadowBlurInner1" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner1"></feComposite>
            <feColorMatrix values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0  0 0 0 0.613768116 0" in="shadowInnerInner1" type="matrix" result="shadowMatrixInner1"></feColorMatrix>
            <feMerge>
                <feMergeNode in="shadowMatrixOuter1"></feMergeNode>
                <feMergeNode in="SourceGraphic"></feMergeNode>
                <feMergeNode in="shadowMatrixInner1"></feMergeNode>
            </feMerge>
        </filter>
        <path d="M1.00684547,0 C0.450780073,0 0,0.455760956 0,1.00247329 L0,10.9975267 C0,11.5511774 0.449948758,12 1.00684547,12 L12.9931545,12 C13.5492199,12 14,11.544239 14,10.9975267 L14,1.00247329 C14,0.448822582 13.5500512,0 12.9931545,0 L1.00684547,0 Z M1,1 L1,11 L13,11 L13,1 L1,1 Z" id="path-3"></path>
        <filter x="-50%" y="-50%" width="200%" height="200%" filterUnits="objectBoundingBox" id="filter-4">
            <feOffset dx="0" dy="1" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="0" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 0.7 0" in="shadowBlurOuter1" type="matrix" result="shadowMatrixOuter1"></feColorMatrix>
            <feOffset dx="0" dy="1" in="SourceAlpha" result="shadowOffsetInner1"></feOffset>
            <feGaussianBlur stdDeviation="0" in="shadowOffsetInner1" result="shadowBlurInner1"></feGaussianBlur>
            <feComposite in="shadowBlurInner1" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner1"></feComposite>
            <feColorMatrix values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0  0 0 0 0.613768116 0" in="shadowInnerInner1" type="matrix" result="shadowMatrixInner1"></feColorMatrix>
            <feMerge>
                <feMergeNode in="shadowMatrixOuter1"></feMergeNode>
                <feMergeNode in="SourceGraphic"></feMergeNode>
                <feMergeNode in="shadowMatrixInner1"></feMergeNode>
            </feMerge>
        </filter>
        <path id="path-5" d="M4,10 L12,10 L12,4 L4,10 Z"></path>
        <path id="path-6" d="M4.984375,10 L12.03125,4 L12.03125,1.96875 L10.90625,1.96875 L7.96682738,1.96875 L2.03125,7.03125 L2,10 L4.984375,10 Z"></path>
    </defs>
    <g id="icons-moono" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" sketch:type="MSPage">
        <g id="clip" sketch:type="MSArtboardGroup">
            <g sketch:type="MSLayerGroup" transform="translate(1.000000, 2.000000)">
                <g id="Rectangle-45" filter="url(#filter-2)">
                    <use fill="url(#linearGradient-1)" fill-rule="evenodd" sketch:type="MSShapeGroup" xlink:href="#path-3"></use>
                    <use fill="none" xlink:href="#path-3"></use>
                    <use fill="none" xlink:href="#path-3"></use>
                </g>
                <g id="Rectangle-6" filter="url(#filter-4)">
                    <use fill="url(#linearGradient-1)" fill-rule="evenodd" sketch:type="MSShapeGroup" xlink:href="#path-5"></use>
                    <use fill="none" xlink:href="#path-5"></use>
                    <use fill="none" xlink:href="#path-5"></use>
                </g>
                <g id="Path-30" opacity="0.410389459">
                    <use fill="none" sketch:type="MSShapeGroup" xlink:href="#path-6"></use>
                    <use fill="none" xlink:href="#path-6"></use>
                    <use fill="#000000" fill-rule="evenodd" xlink:href="#path-6"></use>
                </g>
            </g>
        </g>
    </g>
</svg>