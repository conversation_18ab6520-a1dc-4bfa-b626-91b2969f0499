{"appTitle": "CKFinder", "dir": "ltr", "langCode": "en", "common": {"abort": "Abort", "cancel": "Cancel", "choose": "<PERSON><PERSON>", "close": "Close", "copy": "Copy", "delete": "Delete", "download": "Download", "edit": "Edit", "maximize": "Maximize", "messageTitle": "Information", "minimize": "Minimize", "move": "Move", "newNameDialogTitle": "New Name", "ok": "OK", "pleaseWait": "Please wait.", "rememberDecision": "Remember my decision", "rename": "<PERSON><PERSON>", "showMore": "Show more", "skip": "<PERSON><PERSON>", "upload": "Upload", "view": "View"}, "units": {"dateFormat": "m/d/yyyy h:MM aa", "dateAmPm": ["AM", "PM"], "kb": "{size} KB", "mb": "{size} MB", "gb": "{size} GB", "sizePerSecond": "{size}/s", "pixelShort": "px"}, "files": {"autoRename": "Auto-rename", "countMany": "{count} files", "countOne": "1 file", "deleteConfirmation": "Are you sure you want to delete {count} files?", "fileDeleteConfirmation": "Are you sure you want to delete the \"{name}\" file?", "fileRenameExtensionConfirmation": "Are you sure you want to change the file extension? The file may become unusable.", "fileRenameLabel": "Type the new file name: ", "filesPaneTitle": "Files pane", "filesRefresh": "Refreshing files", "filterPlaceholder": "Filter", "gettingFileData": "Getting file data.", "overwrite": "Overwrite", "loadingFilesPane": {"title": "Loading...", "text": "The files are being loaded."}, "emptyFilesPane": {"title": "The folder is empty.", "text": "Use the <strong>Upload</strong> button or <strong>drag and drop</strong> your files here."}, "filterFilesEmpty": {"title": "Nothing found.", "text": "Please try using different search criteria."}}, "folders": {"deleteConfirmation": "Are you sure you want to delete the \"{name}\" folder?", "destinationFolder": "Destination Folder", "newNameLabel": "Type the new folder name: ", "newSubfolder": "New Subfolder", "renameDialogTitle": "<PERSON><PERSON>", "treeTitle": "Folders tree"}, "copy": {"dropMenuItem": "Copy Here", "errorDialogTitle": "Files that cannot be copied:", "manyFilesDialogTitle": "Copy {count} Files To...", "manyFilesWait": "Copying {count} files.", "oneFileDialogTitle": "Copy File To...", "oneFileWait": "Copying file.", "operationLabel": "Copying Files", "operationSummary": "Number of files copied: {count}."}, "move": {"dropMenuItem": "Move Here", "errorDialogTitle": "Files that cannot be moved:", "manyFilesDialogTitle": "Move {count} Files To...", "manyFilesWait": "Moving {count} files.", "oneFileDialogTitle": "Move File To...", "oneFileWait": "Moving file.", "operationLabel": "Moving Files", "operationSummary": "Number of files moved: {count}."}, "upload": {"addFiles": "Add Files", "bytesCountProgress": "({bytesUploaded} of {bytesTotal})", "details": "Details", "filesCountProgress": "{filesUploaded} of {filesTotal}", "progressLabel": "Upload in progress.", "progressMessage": "Uploading...", "selectFileLabel": "Select a file to upload", "selectFiles": "Select files to upload", "success": "Upload finished!", "summary": "Uploaded files: {count}"}, "settings": {"display": "Display", "displayDate": "Date", "displayName": "File Name", "displaySize": "File Size", "sortAscending": "Ascending", "sortBy": "Sort by", "sortByOrder": "Order", "sortDescending": "Descending", "thumbnailSize": "<PERSON><PERSON><PERSON><PERSON>", "title": "Settings", "viewType": "View", "viewTypeCompact": "Compact", "viewTypeList": "List", "viewTypeThumbnails": "Thumbnails"}, "errors": {"codes": {"10": "Invalid command.", "11": "The resource type was not specified in the request.", "12": "The requested resource type is invalid.", "13": "The connector configuration file is invalid.", "14": "Invalid connector plugin: {pluginName}.", "102": "Invalid file or folder name.", "103": "It was not possible to complete the request due to authorization restrictions.", "104": "It was not possible to complete the request due to file system permission restrictions.", "105": "Invalid file extension.", "109": "Invalid request.", "110": "Unknown error.", "111": "It was not possible to complete the request due to resulting file size.", "115": "A file or folder with the same name already exists.", "116": "Folder not found. Please refresh and try again.", "117": "File not found. Please refresh the files list and try again.", "118": "Source and target paths are equal.", "201": "A file with the same name already exists. The uploaded file was renamed to \"{name}\".", "202": "Invalid file.", "203": "Invalid file. The file size is too big.", "204": "The uploaded file is corrupt.", "205": "No temporary folder is available for upload on the server.", "206": "Upload canceled due to security reasons. The file contains HTML-like data.", "207": "The uploaded file was renamed to \"{name}\".", "300": "Moving file(s) failed.", "301": "Copying file(s) failed.", "302": "Deleting file(s) failed.", "500": "The file browser is disabled for security reasons. Please contact your system administrator and check the CKFinder configuration file.", "501": "Thumbnail support is disabled."}, "deleteFilePermissions": "Removing files is not allowed in this folder.", "fileInvalidCharacters": "The file name cannot contain any of the following characters: {disallowedCharacters}", "fileNameNotEmpty": "The file name cannot be empty.", "folderInvalidCharacters": "The folder name cannot contain any of the following characters: {disallowedCharacters}", "incorrectExtension": "File extension is not allowed in this folder.", "missingFile": "The requested file is no longer available.", "missingFolder": "The folder you are trying to reach is no longer available.", "noUploadFolderSelected": "Please select a folder before uploading.", "operationCompleted": "Operation completed with errors.", "renameFilePermissions": "Renaming files is not allowed in this folder.", "unknown": "It was not possible to complete the request. (Error {number})", "unknownUploadError": "Error sending the file.", "uploadErrors": "Upload finished with errors.", "uploadPermissions": "File upload is not allowed."}, "chooseResizedImage": {"title": "Choose Resized", "originalSize": "Original Size", "sizes": {"custom": "Custom", "large": "Large", "max": "Max", "medium": "Medium", "small": "Small"}}, "editImage": {"adjust": "Adjust", "apply": "Apply", "confirmExit": "Are you sure you want to close? You have unsaved changes to the image.", "crop": "Crop", "downloadAction": "Downloading original image.", "keepAspectRatio": "Keep Aspect Ratio", "loading": "Loading image", "presets": "Presets", "reset": "Reset", "resize": "Resize", "rotate": "Rotate", "rotateAntiClockwise": "90&deg; Left", "rotateClockwise": "90&deg; Right", "save": "Save", "saveDialogFileExists": "File with the same name already exists in folder.", "saveDialogOverwrite": "Overwrite File", "saveDialogSaveAs": "Save as:", "saveDialogTitle": "Save Changes", "title": "Edit Image", "transformationAction": "Applying transformations.", "uploadAction": "Uploading edited image.", "filters": {"brightness": "Brightness", "clip": "Clip", "contrast": "Contrast", "exposure": "Exposure", "gamma": "Gamma", "hue": "<PERSON><PERSON>", "noise": "Noise", "saturation": "Saturation", "sepia": "Sepia", "sharpen": "Sharpen", "stackBlur": "Blur", "vibrance": "Vibrance"}, "preset": {"clarity": "Clarity", "concentrate": "Concentrate", "crossProcess": "Cross Process", "glowingSun": "Glowing Sun", "grungy": "Grungy", "hazyDays": "Hazy Days", "hemingway": "<PERSON><PERSON><PERSON>", "herMajesty": "Her Majesty", "jarques": "<PERSON><PERSON><PERSON>", "lomo": "Lomo", "love": "Love", "nostalgia": "Nostalgia", "oldBoot": "Old Boot", "orangePeel": "Orange Peel", "pinhole": "<PERSON><PERSON>ole", "sinCity": "Sin City", "sunrise": "Sunrise", "vintage": "Vintage"}}, "shortcuts": {"title": "Keyboard Shortcuts", "general": {"action": "Execute default action", "firstItem": "Go to first item", "focusFilesPane": "Focus files pane", "focusFoldersPane": "Focus folders pane or breadcrumbs", "focusNext": "Focus next pane", "focusToolbar": "Focus toolbar", "lastItem": "Go to last item", "listShortcuts": "Open this help dialog", "nextItem": "Go to next item", "previousItem": "Go to previous item", "showContextMenu": "Open context menu", "title": "General Interface"}, "files": {"addToSelectionAbove": "Add or remove files above to selection", "addToSelectionBelow": "Add or remove files below to selection", "addToSelectionLeft": "Add or remove file on the left to selection", "addToSelectionRight": "Add or remove file on the right to selection", "delete": "Delete file(s)", "refresh": "Refresh files", "rename": "Rename file", "selectAll": "Select all files", "upload": "Upload file(s)"}, "folders": {"collapseOrParent": "Collapse folder / Go to parent folder", "delete": "Delete folder", "expandOrSubfolder": "Expand folder / Go to first subfolder", "title": "Folders Pane"}, "keys": {"ctrl": "Control", "delete": "Delete", "downArrow": "Down arrow", "escape": "Escape", "leftArrow": "Left arrow", "question": "Question mark", "rightArrow": "Right arrow", "upArrow": "Up arrow"}, "keysAbbreviations": {"alt": "alt", "ctrl": "ctrl", "del": "del", "end": "end", "home": "home", "shift": "shift"}}}