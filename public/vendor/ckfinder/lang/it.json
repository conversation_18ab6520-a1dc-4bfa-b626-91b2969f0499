{"appTitle": "CKFinder", "dir": "ltr", "langCode": "it", "common": {"abort": "Interrompi", "cancel": "<PERSON><PERSON><PERSON>", "choose": "Seleziona", "close": "<PERSON><PERSON>", "copy": "Copia", "delete": "Elimina", "download": "Scarica", "edit": "Modifica", "maximize": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "messageTitle": "Informazione", "minimize": "<PERSON><PERSON><PERSON><PERSON>", "move": "Sposta", "newNameDialogTitle": "Nuovo nome", "ok": "OK", "pleaseWait": "At<PERSON>ere.", "rememberDecision": "Ricorda decisione", "rename": "Rinomina", "showMore": "Mostra altro", "skip": "Salta", "upload": "Carica", "view": "<PERSON><PERSON><PERSON>"}, "units": {"dateFormat": "dd/mm/yyyy HH:MM", "dateAmPm": ["AM", "PM"], "kb": "{size} KB", "mb": "{size} MB", "gb": "{size} GB", "sizePerSecond": "{size}/s", "pixelShort": "px"}, "files": {"autoRename": "Rinomina automaticamente", "countMany": "{count} file", "countOne": "1 file", "deleteConfirmation": "Eliminare {count} file?", "fileDeleteConfirmation": "Eliminare il file \"{name}\"?", "fileRenameExtensionConfirmation": "Cambiare l'estensione del file? Il file può risultare inutilizzabile.", "fileRenameLabel": "Nuovo nome del file: ", "filesPaneTitle": "Pannello file", "filesRefresh": "Aggiornamento dei file", "filterPlaceholder": "Filtro", "gettingFileData": "Recupero dei dati del file.", "overwrite": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "loadingFilesPane": {"title": "Caricamento...", "text": "i file vengono caricati."}, "emptyFilesPane": {"title": "La cartella è vuota.", "text": "Usare il pulsante <strong><PERSON><PERSON></strong> oppure <strong>trascinare qui</strong> i propri file."}, "filterFilesEmpty": {"title": "Non è stato trovato nulla.", "text": "Riprovare con criteri di ricerca differenti."}}, "folders": {"deleteConfirmation": "Eliminare la cartella \"{name}\"?", "destinationFolder": "Cartella di destinazione", "newNameLabel": "Nome della cartella: ", "newSubfolder": "<PERSON>uova sotto-cartella", "renameDialogTitle": "R<PERSON><PERSON> cartella", "treeTitle": "Albero cartelle"}, "copy": {"dropMenuItem": "Copia qui", "errorDialogTitle": "File che non è possibile copiare:", "manyFilesDialogTitle": "Copia {count} file in...", "manyFilesWait": "Copia di {count} file in corso.", "oneFileDialogTitle": "Copia il file in...", "oneFileWait": "Copia del file in corso.", "operationLabel": "Copia dei file", "operationSummary": "Numero di file copiati: {count}."}, "move": {"dropMenuItem": "S<PERSON>a qui", "errorDialogTitle": "File che non è possibile spostare:", "manyFilesDialogTitle": "Sposta {count} file in...", "manyFilesWait": "Spostamento di {count} file in corso.", "oneFileDialogTitle": "Sposta il file in...", "oneFileWait": "Spostamento del file in corso.", "operationLabel": "Spostamento dei file", "operationSummary": "Numero di file spostati: {count}."}, "upload": {"addFiles": "Aggiungi file", "bytesCountProgress": "({bytesUploaded} di {bytesTotal})", "details": "<PERSON><PERSON><PERSON>", "filesCountProgress": "{filesUploaded} di {filesTotal}", "progressLabel": "caricamento in corso.", "progressMessage": "Caricamento in corso...", "selectFileLabel": "Selezionare un file da caricare", "selectFiles": "Selezionare i file da caricare", "success": "Caricamento completato!", "summary": "File caricati: {count}"}, "settings": {"display": "Informazioni:", "displayDate": "Data", "displayName": "Nome del file", "displaySize": "Dimensioni", "sortAscending": "<PERSON><PERSON>", "sortBy": "Ordina per", "sortByOrder": "Ordine", "sortDescending": "Decrescente", "thumbnailSize": "Dimensioni miniatura", "title": "Impostazioni", "viewType": "Vista", "viewTypeCompact": "Compact", "viewTypeList": "Lista", "viewTypeThumbnails": "Miniature"}, "errors": {"codes": {"10": "Comando non valido.", "11": "Il tipo di risorsa non è stato specificato nella richiesta.", "12": "Il tipo di risorsa richiesto non è valido.", "13": "La configurazione del connettore non è valida.", "14": "Estensione del connettore non valida: {pluginName}.", "102": "Il nome del file o della cartella non è valido.", "103": "Non è stato possibile completare la richiesta a causa di restrizioni di autorizzazione.", "104": "Non è stato possibile completare la richiesta a causa di restrizioni nei permessi del file system.", "105": "L'estensione del file non è valida.", "109": "Richiesta non valida.", "110": "<PERSON><PERSON><PERSON> sconos<PERSON>.", "111": "Non è stato possibile completare la richiesta a causa della dimensione finale del file.", "115": "Esiste già un file o una cartella con lo stesso nome.", "116": "Cartella non trovata. Aggiornare e riprovare.", "117": "File non trovato. Aggiornare la lista dei file e riprovare.", "118": "Il percorso di origine e quello di destinazione sono uguali.", "201": "Un file con lo stesso nome esiste già. Il file caricato è stato rinominato in \"{name}\".", "202": "File non valido.", "203": "File non valido. Il file è troppo grande.", "204": "Il file caricato è corrotto.", "205": "Non è disponibile alcuna cartella temporanea per il caricamento dei file sul server.", "206": "Caricamento annullato per motivi di sicurezza. Il file contiene dati in formato HTML.", "207": "Il file caricato è stato rinominato in \"{name}\".", "300": "Non è stato possibile spostare i file.", "301": "Non è stato possibile copiare i file.", "302": "Non è stato possibile eliminare i file.", "500": "Il gestore dei file è disabilitato per motivi di sicurezza. Contattare l'amministratore del sistema e verificare la configurazione di CKFinder.", "501": "Il supporto alle miniature non è attivo."}, "deleteFilePermissions": "Removing files is not allowed in this folder.", "fileInvalidCharacters": "I seguenti caratteri non possono essere usati per comporre il nome del file: {disallowedCharacters}", "fileNameNotEmpty": "Il nome del file non può essere vuoto.", "folderInvalidCharacters": "I seguenti caratteri non possono essere usati per comporre il nome della cartella: {disallowedCharacters}", "incorrectExtension": "In questa cartella non sono permessi file con questa estensione.", "missingFile": "Il file richiesto non è più disponibile.", "missingFolder": "La cartella che si sta cercando di raggiungere non è più disponibile.", "noUploadFolderSelected": "Selezionare la cartella prima di caricare i file.", "operationCompleted": "L'operazione è stata completata con errori.", "renameFilePermissions": "Renaming files is not allowed in this folder.", "unknown": "Non è stato possibile completare la richiesta (errore {number}).", "unknownUploadError": "Errore durante il caricamento del file.", "uploadErrors": "Caricamento completato con errori.", "uploadPermissions": "Il caricamento dei file non è permesso."}, "chooseResizedImage": {"title": "Seleziona miniatura", "originalSize": "Dimensioni originali", "sizes": {"custom": "Personalizzata", "large": "Grande", "max": "Max", "medium": "Media", "small": "<PERSON><PERSON><PERSON>"}}, "editImage": {"adjust": "<PERSON><PERSON><PERSON><PERSON>", "apply": "Applica", "confirmExit": "Si è certi di voler chiudere? Sono state apportate delle modifiche all'immagine che non sono ancora state salvate.", "crop": "Rita<PERSON>", "downloadAction": "Scaricamento dell'immagine originale.", "keepAspectRatio": "Mantieni il rapporto di forma", "loading": "Caricamento dell'immagine", "presets": "Combinazioni", "reset": "Reimposta", "resize": "Ridimensiona", "rotate": "<PERSON><PERSON><PERSON>", "rotateAntiClockwise": "90&deg; anti-orari", "rotateClockwise": "90&deg; orari", "save": "<PERSON><PERSON>", "saveDialogFileExists": "Un file con lo stesso nome esiste già nella cartella.", "saveDialogOverwrite": "Sovrascrivi file", "saveDialogSaveAs": "Salva con nome:", "saveDialogTitle": "Salva modifiche", "title": "Modifica immagine", "transformationAction": "Applicazione delle trasformazioni.", "uploadAction": "Caricamento dell'immagine modificata.", "filters": {"brightness": "Luminosità", "clip": "<PERSON><PERSON><PERSON>", "contrast": "Contrasto", "exposure": "Esposizione", "gamma": "Gamma", "hue": "<PERSON><PERSON>", "noise": "<PERSON><PERSON><PERSON>", "saturation": "Saturazione", "sepia": "Seppia", "sharpen": "<PERSON><PERSON><PERSON> contorni", "stackBlur": "Sfocatura", "vibrance": "Brillantezza"}, "preset": {"clarity": "Limpidezza", "concentrate": "Concentra", "crossProcess": "Interprocesso", "glowingSun": "Sole brillante", "grungy": "Sporco", "hazyDays": "<PERSON><PERSON><PERSON> s<PERSON>", "hemingway": "<PERSON><PERSON><PERSON>", "herMajesty": "<PERSON><PERSON>", "jarques": "<PERSON><PERSON><PERSON>", "lomo": "Lomo", "love": "<PERSON><PERSON>", "nostalgia": "Nostalgia", "oldBoot": "<PERSON><PERSON><PERSON>o stivale", "orangePeel": "Buccia d'arancia", "pinhole": "<PERSON><PERSON><PERSON>", "sinCity": "Sin City", "sunrise": "Alba", "vintage": "Vintage"}}, "shortcuts": {"title": "Tasti rapidi", "general": {"action": "Esegui azione predefinita", "firstItem": "Vai al primo elemento", "focusFilesPane": "Attiva pannello dei file", "focusFoldersPane": "Attiva pannello delle cartelle o breadcrumb", "focusNext": "Attiva pannello successivo", "focusToolbar": "Attiva barra degli strumenti", "lastItem": "Vai all'ultimo elemento", "listShortcuts": "Apri questa finestra d'aiuto", "nextItem": "Vai all'elemento successivo", "previousItem": "Vai all'elemento precedente", "showContextMenu": "<PERSON>i menù contestuale", "title": "Interfaccia generale"}, "files": {"addToSelectionAbove": "Aggiungi o rimuovi file sopra la selezione", "addToSelectionBelow": "Aggiungi o rimuovi file sotto la selezione", "addToSelectionLeft": "Aggiungi o rimuovi file a sinistra della selezione", "addToSelectionRight": "Aggiungi o rimuovi file a destra della selezione", "delete": "Elimina file", "refresh": "Aggiorna file", "rename": "Rinomina file", "selectAll": "Seleziona tutti i file", "upload": "Carica file"}, "folders": {"collapseOrParent": "Comprimi cartella / Vai alla cartella padre", "delete": "<PERSON><PERSON> cartella", "expandOrSubfolder": "Espandi cartella / Vai alla prima sotto-cartella", "title": "Pannello delle cartelle"}, "keys": {"ctrl": "Control", "delete": "Canc", "downArrow": "<PERSON><PERSON><PERSON> giù", "escape": "Esc", "leftArrow": "<PERSON><PERSON><PERSON> sinistra", "question": "Punto di domanda", "rightArrow": "<PERSON><PERSON><PERSON> destra", "upArrow": "Freccia su"}, "keysAbbreviations": {"alt": "alt", "ctrl": "ctrl", "del": "canc", "end": "fine", "home": "inizio", "shift": "shift"}}}