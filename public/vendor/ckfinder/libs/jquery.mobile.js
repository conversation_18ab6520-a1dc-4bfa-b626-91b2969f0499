!function(t,i,s){"function"==typeof define&&define.amd?define(["jquery"],function(e){return s(e,t,i),e.mobile}):s(t.jQuery,t,i)}(this,document,function(e,D,E,t){function s(e,t){var i,s,n=e.nodeName.toLowerCase();return"area"===n?(s=(i=e.parentNode).name,!(!e.href||!s||"map"!==i.nodeName.toLowerCase())&&(!!(s=r("img[usemap=#"+s+"]")[0])&&o(s))):(/input|select|textarea|button|object/.test(n)?!e.disabled:"a"===n&&e.href||t)&&o(e)}function o(e){return r.expr.pseudos.visible(e)&&!r(e).parents().addBack().filter(function(){return"hidden"===r.css(this,"visibility")}).length}var r,l,n,i,a,h,c,d,u,p,m,f,g,b,v,_,C,w,y,x,T,k,P,S,A,I,B,O,F,N,H,q,L,j,M,U,R,W,z,K,V,$,G,X,Z,Y,Q,J,ee,te,ie,se,ne,oe,ae,re,le,he,de,ce,ue,pe,me,fe,ge,be,ve,_e,Ce,we,ye,xe,Te,ke,Pe,De,Ee,Se,Ae,Ie,Be,Oe,Fe,Ne,He,qe,Le,je,Me,Ue,Re,We,ze,Ke,Ve,$e,Ge,Xe,Ze,Ye,Qe,Je,et,tt,it,st,nt,ot,at,rt,lt,ht,dt,ct,ut,pt,mt,ft,gt,bt,vt,_t,Ct,wt,yt,xt,Tt,kt,Pt,Dt,Et,St,At,It,Bt,Ot,Ft,Nt,Ht,qt,Lt,jt,Mt,Ut,Rt,Wt,zt,Kt,Vt,$t,Gt,Xt,Zt,Yt,Qt,Jt,ei,ti,ii,si,ni,oi,ai,ri,li,hi,di,ci,ui,pi,mi,fi,gi,bi,vi,_i,Ci,wi,yi,xi;function Ti(e){return"-"+e.toLowerCase()}function ki(e){return"#"+(e=e||location.href).replace(/^[^#]*#?(.*)$/,"$1")}function Pi(e){return e}function Di(){var e=ki(),t=q(N);e!==N?(H(N=e,t),S(A).trigger(L)):t!==N&&(location.href=location.href.replace(/#.*/,"")+t),B=setTimeout(Di,S.fn[L].delay)}function Ei(e){var t,i=e.charAt(0).toUpperCase()+e.substr(1),s=(e+" "+te.join(i+" ")+i).split(" ");for(t in s)if(void 0!==ee[s[t]])return 1}function Si(){}function Ai(e,t,i,s){var n=i.type;i.type=t,s?Ce.event.trigger(i,ye,e):Ce.event.dispatch.call(e,i),i.type=n}function Ii(){var e=Ae();e!==Ie&&(Ie=e,Fe.trigger(Ne))}function Bi(){Re.resolve(),Re=null}function Oi(e){for(;e&&("string"!=typeof e.nodeName||"a"!==e.nodeName.toLowerCase());)e=e.parentNode;return e}function Fi(e){return"ui-btn-icon-"+(null===e?"left":e)}function Ni(e){for(var t=e.length,i=[],s=0;s<t;s++)e[s].className.match(ot)||i.push(e[s]);return nt(i)}function Hi(e,t,i,s){return e<t?i+(e-t)/2:Math.min(Math.max(i,s-t/2),i+e-t)}function qi(e){return{x:e.scrollLeft(),y:e.scrollTop(),cx:e[0].innerWidth||e.width(),cy:e[0].innerHeight||e.height()}}function Li(e,t,i){(i=e[i+"All"]().not(Vt).first()).length&&(t.trigger("blur").attr("tabindex","-1"),i.find("a").first().trigger("focus"))}function ji(e){return"-"+e.toLowerCase()}function Mi(e,t){return-1===(""+(ci.mobile.getAttribute(this,"filtertext")||ci(this).text())).toLowerCase().indexOf(t)}function Ui(e){return 1<e.hash.length&&decodeURIComponent(e.href.replace(_i,""))===decodeURIComponent(location.href.replace(_i,""))}function Ri(){yi.removeClass("ui-mobile-rendering")}function Wi(e){return"function"==typeof e}e.mobile={},a=0,h=/^ui-id-\d+$/,(r=e).ui=r.ui||{},r.extend(r.ui,{version:"c0ab71056b936627e8a7821f03c044aec6280a40",keyCode:{BACKSPACE:8,COMMA:188,DELETE:46,DOWN:40,END:35,ENTER:13,ESCAPE:27,HOME:36,LEFT:37,PAGE_DOWN:34,PAGE_UP:33,PERIOD:190,RIGHT:39,SPACE:32,TAB:9,UP:38}}),r.fn.extend({focus:(n=r.fn.focus,function(t,i){return"number"==typeof t?this.each(function(){var e=this;setTimeout(function(){r(e).trigger("focus"),i&&i.call(e)},t)}):n.apply(this,arguments)}),scrollParent:function(){var e=(r.ui.ie&&/(static|relative)/.test(this.css("position"))||/absolute/.test(this.css("position"))?this.parents().filter(function(){return/(relative|absolute|fixed)/.test(r.css(this,"position"))&&/(auto|scroll)/.test(r.css(this,"overflow")+r.css(this,"overflow-y")+r.css(this,"overflow-x"))}):this.parents().filter(function(){return/(auto|scroll)/.test(r.css(this,"overflow")+r.css(this,"overflow-y")+r.css(this,"overflow-x"))})).eq(0);return/fixed/.test(this.css("position"))||!e.length?r(this[0].ownerDocument||E):e},uniqueId:function(){return this.each(function(){this.id||(this.id="ui-id-"+ ++a)})},removeUniqueId:function(){return this.each(function(){h.test(this.id)&&r(this).removeAttr("id")})}}),r.extend(r.expr.pseudos,{data:r.expr.createPseudo?r.expr.createPseudo(function(t){return function(e){return!!r.data(e,t)}}):function(e,t,i){return!!r.data(e,i[3])},focusable:function(e){return s(e,!isNaN(r.attr(e,"tabindex")))},tabbable:function(e){var t=r.attr(e,"tabindex"),i=isNaN(t);return(i||0<=t)&&s(e,!i)}}),r("<a>").outerWidth(1).jquery||r.each(["Width","Height"],function(e,i){var n="Width"===i?["Left","Right"]:["Top","Bottom"],s=i.toLowerCase(),o={innerWidth:r.fn.innerWidth,innerHeight:r.fn.innerHeight,outerWidth:r.fn.outerWidth,outerHeight:r.fn.outerHeight};function a(e,t,i,s){return r.each(n,function(){t-=parseFloat(r.css(e,"padding"+this))||0,i&&(t-=parseFloat(r.css(e,"border"+this+"Width"))||0),s&&(t-=parseFloat(r.css(e,"margin"+this))||0)}),t}r.fn["inner"+i]=function(e){return e===l?o["inner"+i].call(this):this.each(function(){r(this).css(s,a(this,e)+"px")})},r.fn["outer"+i]=function(e,t){return"number"!=typeof e?o["outer"+i].call(this,e):this.each(function(){r(this).css(s,a(this,e,!0,t)+"px")})}}),r.fn.addBack||(r.fn.addBack=function(e){return this.add(null==e?this.prevObject:this.prevObject.filter(e))}),r("<a>").data("a-b","a").removeData("a-b").data("a-b")&&(r.fn.removeData=(i=r.fn.removeData,function(e){return arguments.length?i.call(this,r.camelCase(e)):i.call(this)})),r.ui.ie=!!/msie [\w.]+/.exec(navigator.userAgent.toLowerCase()),r.support.selectstart="onselectstart"in E.createElement("div"),r.fn.extend({disableSelection:function(){return this.on((r.support.selectstart?"selectstart":"mousedown")+".ui-disableSelection",function(e){e.preventDefault()})},enableSelection:function(){return this.off(".ui-disableSelection")},zIndex:function(e){if(e!==l)return this.css("zIndex",e);if(this.length)for(var t,i,s=r(this[0]);s.length&&s[0]!==E;){if(("absolute"===(t=s.css("position"))||"relative"===t||"fixed"===t)&&(i=parseInt(s.css("zIndex"),10),!isNaN(i)&&0!==i))return i;s=s.parent()}return 0}}),r.ui.plugin={add:function(e,t,i){var s,n=r.ui[e].prototype;for(s in i)n.plugins[s]=n.plugins[s]||[],n.plugins[s].push([t,i[s]])},call:function(e,t,i,s){var n,o=e.plugins[t];if(o&&(s||e.element[0].parentNode&&11!==e.element[0].parentNode.nodeType))for(n=0;n<o.length;n++)e.options[o[n][0]]&&o[n][1].apply(e.element,i)}},d=this,(c=e).extend(c.mobile,{window:c(d),document:c(E),keyCode:c.ui.keyCode,behaviors:{},silentScroll:function(e){"number"!=typeof e&&(e=c.mobile.defaultHomeScroll),c.event.special.scrollstart.enabled=!1,setTimeout(function(){d.scrollTo(0,e),c.mobile.document.trigger("silentscroll",{x:0,y:e})},20),setTimeout(function(){c.event.special.scrollstart.enabled=!0},150)},getClosestBaseUrl:function(e){var t=c(e).closest(".ui-page").jqmData("url"),e=c.mobile.path.documentBase.hrefNoHash;return c.mobile.dynamicBaseEnabled&&t&&c.mobile.path.isPath(t)||(t=e),c.mobile.path.makeUrlAbsolute(t,e)},removeActiveLinkClass:function(e){!c.mobile.activeClickedLink||c.mobile.activeClickedLink.closest("."+c.mobile.activePageClass).length&&!e||c.mobile.activeClickedLink.removeClass(c.mobile.activeBtnClass),c.mobile.activeClickedLink=null},getInheritedTheme:function(e,t){for(var i,s,n=e[0],o="",a=/ui-(bar|body|overlay)-([a-z])\b/;n&&!((i=n.className||"")&&(s=a.exec(i))&&(o=s[2]));)n=n.parentNode;return o||t||"a"},enhanceable:function(e){return this.haveParents(e,"enhance")},hijackable:function(e){return this.haveParents(e,"ajax")},haveParents:function(e,t){if(!c.mobile.ignoreContentEnabled)return e;for(var i,s,n,o=e.length,a=c(),r=0;r<o;r++){for(s=e.eq(r),n=!1,i=e[r];i;){if("false"===(i.getAttribute?i.getAttribute("data-"+c.mobile.ns+t):"")){n=!0;break}i=i.parentNode}n||(a=a.add(s))}return a},getScreenHeight:function(){return d.innerHeight||c.mobile.window.height()},resetActivePageHeight:function(e){var t,i,s,n,o,a,r,l=c("."+c.mobile.activePageClass),h=l.height(),d=l.outerHeight(!0);t=l,i="number"==typeof e?e:c.mobile.getScreenHeight(),s=t.parent(),n=[],o=function(){var e=c(this),e=c.mobile.toolbar&&e.data("mobile-toolbar")?e.toolbar("option"):{position:e.attr("data-"+c.mobile.ns+"position"),updatePagePadding:!1!==e.attr("data-"+c.mobile.ns+"update-page-padding")};return!("fixed"===e.position&&!0===e.updatePagePadding)},a=s.children(":jqmData(role='header')").filter(o),r=t.children(":jqmData(role='header')"),o=s.children(":jqmData(role='footer')").filter(o),t=t.children(":jqmData(role='footer')"),0===r.length&&0<a.length&&(n=n.concat(a.toArray())),0===t.length&&0<o.length&&(n=n.concat(o.toArray())),c.each(n,function(e,t){i-=c(t).outerHeight()}),e=Math.max(0,i),l.css("min-height",""),l.height()<e&&l.css("min-height",e-(d-h))},loading:function(){var e=this.loading._widget||c(c.mobile.loader.prototype.defaultHtml).loader(),t=e.loader.apply(e,arguments);return this.loading._widget=e,t}}),c.addDependents=function(e,t){var i=c(e),e=i.jqmData("dependents")||c();i.jqmData("dependents",c(e).add(t))},c.fn.extend({removeWithDependents:function(){c.removeWithDependents(this)},enhanceWithin:function(){var e,s={},n=c.mobile.page.prototype.keepNativeSelector(),o=this;for(e in c.mobile.nojs&&c.mobile.nojs(this),c.mobile.links&&c.mobile.links(this),c.mobile.degradeInputsWithin&&c.mobile.degradeInputsWithin(this),c.fn.buttonMarkup&&this.find(c.fn.buttonMarkup.initSelector).not(n).jqmEnhanceable().buttonMarkup(),c.fn.fieldcontain&&this.find(":jqmData(role='fieldcontain')").not(n).jqmEnhanceable().fieldcontain(),c.each(c.mobile.widgets,function(e,t){var i;!t.initSelector||0<(i=0<(i=c.mobile.enhanceable(o.find(t.initSelector))).length?i.not(n):i).length&&(s[t.prototype.widgetName]=i)}),s)s[e][e]();return this},addDependents:function(e){c.addDependents(this,e)},getEncodedText:function(){return c("<a>").text(this.text()).html()},jqmEnhanceable:function(){return c.mobile.enhanceable(this)},jqmHijackable:function(){return c.mobile.hijackable(this)}}),c.removeWithDependents=function(e){e=c(e);(e.jqmData("dependents")||c()).remove(),e.remove()},c.addDependents=function(e,t){var i=c(e),e=i.jqmData("dependents")||c();i.jqmData("dependents",c(e).add(t))},c.find.matches=function(e,t){return c.find(e,null,null,t)},c.find.matchesSelector=function(e,t){return 0<c.find(t,null,null,[e]).length},e.extend(e.mobile,{version:"1.4.5",subPageUrlKey:"ui-page",hideUrlBar:!0,keepNative:":jqmData(role='none'), :jqmData(role='nojs')",activePageClass:"ui-page-active",activeBtnClass:"ui-btn-active",focusClass:"ui-focus",ajaxEnabled:!0,hashListeningEnabled:!0,linkBindingEnabled:!0,defaultPageTransition:"fade",maxTransitionWidth:!1,minScrollBack:0,defaultDialogTransition:"pop",pageLoadErrorMessage:"Error Loading Page",pageLoadErrorMessageTheme:"a",phonegapNavigationEnabled:!1,autoInitializePage:!0,pushStateEnabled:!0,ignoreContentEnabled:!1,buttonMarkup:{hoverDelay:200},dynamicBaseEnabled:!0,pageContainer:e(),allowCrossDomainPages:!1,dialogHashKey:"&ui-state=dialog"}),u=e,m=0,f=Array.prototype.slice,g=u.cleanData,u.cleanData=function(e){for(var t,i=0;null!=(t=e[i]);i++)try{u(t).triggerHandler("remove")}catch(e){}g(e)},u.widget=function(e,i,t){var s,n,o,a,r={},l=e.split(".")[0];return e=e.split(".")[1],s=l+"-"+e,t||(t=i,i=u.Widget),u.expr.pseudos[s.toLowerCase()]=function(e){return!!u.data(e,s)},u[l]=u[l]||{},n=u[l][e],o=u[l][e]=function(e,t){if(!this._createWidget)return new o(e,t);arguments.length&&this._createWidget(e,t)},u.extend(o,n,{version:t.version,_proto:u.extend({},t),_childConstructors:[]}),(a=new i).options=u.widget.extend({},a.options),u.each(t,function(t,s){function n(){return i.prototype[t].apply(this,arguments)}function o(e){return i.prototype[t].apply(this,e)}Wi(s)?r[t]=function(){var e,t=this._super,i=this._superApply;return this._super=n,this._superApply=o,e=s.apply(this,arguments),this._super=t,this._superApply=i,e}:r[t]=s}),o.prototype=u.widget.extend(a,{widgetEventPrefix:n&&a.widgetEventPrefix||e},r,{constructor:o,namespace:l,widgetName:e,widgetFullName:s}),n?(u.each(n._childConstructors,function(e,t){var i=t.prototype;u.widget(i.namespace+"."+i.widgetName,o,t._proto)}),delete n._childConstructors):i._childConstructors.push(o),u.widget.bridge(e,o),o},u.widget.extend=function(e){for(var t,i,s=f.call(arguments,1),n=0,o=s.length;n<o;n++)for(t in s[n])i=s[n][t],s[n].hasOwnProperty(t)&&i!==p&&(u.isPlainObject(i)?e[t]=u.isPlainObject(e[t])?u.widget.extend({},e[t],i):u.widget.extend({},i):e[t]=i);return e},u.widget.bridge=function(o,t){var a=t.prototype.widgetFullName||o;u.fn[o]=function(i){var e="string"==typeof i,s=f.call(arguments,1),n=this;return i=!e&&s.length?u.widget.extend.apply(null,[i].concat(s)):i,e?this.each(function(){var e,t=u.data(this,a);return"instance"===i?(n=t,!1):t?Wi(t[i])&&"_"!==i.charAt(0)?(e=t[i].apply(t,s))!==t&&e!==p?(n=e&&e.jquery?n.pushStack(e.get()):e,!1):void 0:u.error("no such method '"+i+"' for "+o+" widget instance"):u.error("cannot call methods on "+o+" prior to initialization; attempted to call method '"+i+"'")}):this.each(function(){var e=u.data(this,a);e?e.option(i||{})._init():u.data(this,a,new t(i,this))}),n}},u.Widget=function(){},u.Widget._childConstructors=[],u.Widget.prototype={widgetName:"widget",widgetEventPrefix:"",defaultElement:"<div>",options:{disabled:!1,create:null},_createWidget:function(e,t){t=u(t||this.defaultElement||this)[0],this.element=u(t),this.uuid=m++,this.eventNamespace="."+this.widgetName+this.uuid,this.options=u.widget.extend({},this.options,this._getCreateOptions(),e),this.bindings=u(),this.hoverable=u(),this.focusable=u(),t!==this&&(u.data(t,this.widgetFullName,this),this._on(!0,this.element,{remove:function(e){e.target===t&&this.destroy()}}),this.document=u(t.style?t.ownerDocument:t.document||t),this.window=u(this.document[0].defaultView||this.document[0].parentWindow)),this._create(),this._trigger("create",null,this._getCreateEventData()),this._init()},_getCreateOptions:u.noop,_getCreateEventData:u.noop,_create:u.noop,_init:u.noop,destroy:function(){this._destroy(),this.element.off(this.eventNamespace).removeData(this.widgetFullName).removeData(u.camelCase(this.widgetFullName)),this.widget().off(this.eventNamespace).removeAttr("aria-disabled").removeClass(this.widgetFullName+"-disabled ui-state-disabled"),this.bindings.off(this.eventNamespace),this.hoverable.removeClass("ui-state-hover"),this.focusable.removeClass("ui-state-focus")},_destroy:u.noop,widget:function(){return this.element},option:function(e,t){var i,s,n,o=e;if(0===arguments.length)return u.widget.extend({},this.options);if("string"==typeof e)if(o={},e=(i=e.split(".")).shift(),i.length){for(s=o[e]=u.widget.extend({},this.options[e]),n=0;n<i.length-1;n++)s[i[n]]=s[i[n]]||{},s=s[i[n]];if(e=i.pop(),t===p)return s[e]===p?null:s[e];s[e]=t}else{if(t===p)return this.options[e]===p?null:this.options[e];o[e]=t}return this._setOptions(o),this},_setOptions:function(e){for(var t in e)this._setOption(t,e[t]);return this},_setOption:function(e,t){return this.options[e]=t,"disabled"===e&&(this.widget().toggleClass(this.widgetFullName+"-disabled",!!t),this.hoverable.removeClass("ui-state-hover"),this.focusable.removeClass("ui-state-focus")),this},enable:function(){return this._setOptions({disabled:!1})},disable:function(){return this._setOptions({disabled:!0})},_on:function(n,o,e){var a,r=this;"boolean"!=typeof n&&(e=o,o=n,n=!1),e?(o=a=u(o),this.bindings=this.bindings.add(o)):(e=o,o=this.element,a=this.widget()),u.each(e,function(e,t){function i(){if(n||!0!==r.options.disabled&&!u(this).hasClass("ui-state-disabled"))return("string"==typeof t?r[t]:t).apply(r,arguments)}"string"!=typeof t&&(i.guid=t.guid=t.guid||i.guid||u.guid++);var s=e.match(/^(\w+)\s*(.*)$/),e=s[1]+r.eventNamespace,s=s[2];s?a.on(s,e,i):o.on(e,i)})},_off:function(e,t){t=(t||"").split(" ").join(this.eventNamespace+" ")+this.eventNamespace,e.off(t).off(t)},_delay:function(e,t){var i=this;return setTimeout(function(){return("string"==typeof e?i[e]:e).apply(i,arguments)},t||0)},_hoverable:function(e){this.hoverable=this.hoverable.add(e),this._on(e,{mouseenter:function(e){u(e.currentTarget).addClass("ui-state-hover")},mouseleave:function(e){u(e.currentTarget).removeClass("ui-state-hover")}})},_focusable:function(e){this.focusable=this.focusable.add(e),this._on(e,{focusin:function(e){u(e.currentTarget).addClass("ui-state-focus")},focusout:function(e){u(e.currentTarget).removeClass("ui-state-focus")}})},_trigger:function(e,t,i){var s,n,o=this.options[e];if(i=i||{},(t=u.Event(t)).type=(e===this.widgetEventPrefix?e:this.widgetEventPrefix+e).toLowerCase(),t.target=this.element[0],n=t.originalEvent)for(s in n)s in t||(t[s]=n[s]);return this.element.trigger(t,i),!(Wi(o)&&!1===o.apply(this.element[0],[t].concat(i))||t.isDefaultPrevented())}},u.each({show:"fadeIn",hide:"fadeOut"},function(o,a){u.Widget.prototype["_"+o]=function(t,e,i){var s=(e="string"==typeof e?{effect:e}:e)?!0!==e&&"number"!=typeof e&&e.effect||a:o,n=!u.isEmptyObject(e="number"==typeof(e=e||{})?{duration:e}:e);e.complete=i,e.delay&&t.delay(e.delay),n&&u.effects&&u.effects.effect[s]?t[o](e):s!==o&&t[s]?t[s](e.duration,e.easing,i):t.queue(function(e){u(this)[o](),i&&i.call(t[0]),e()})}}),v={},_=(b=e).find,C=/(?:\{[\s\S]*\}|\[[\s\S]*\])$/,w=/:jqmData\(([^)]*)\)/g,b.extend(b.mobile,{ns:"",getAttribute:function(e,t){var i;(e=e.jquery?e[0]:e)&&e.getAttribute&&(i=e.getAttribute("data-"+b.mobile.ns+t));try{i="true"===i||"false"!==i&&("null"===i?null:+i+""===i?+i:C.test(i)?JSON.parse(i):i)}catch(e){}return i},nsNormalizeDict:v,nsNormalize:function(e){return v[e]||(v[e]=b.camelCase(b.mobile.ns+e))},closestPageData:function(e){return e.closest(":jqmData(role='page'), :jqmData(role='dialog')").data("mobile-page")}}),b.fn.jqmData=function(e,t){var i;return void 0!==e&&(e=e&&b.mobile.nsNormalize(e),i=arguments.length<2||void 0===t?this.data(e):this.data(e,t)),i},b.jqmData=function(e,t,i){var s;return s=void 0!==t?b.data(e,t&&b.mobile.nsNormalize(t),i):s},b.fn.jqmRemoveData=function(e){return this.removeData(b.mobile.nsNormalize(e))},b.jqmRemoveData=function(e,t){return b.removeData(e,b.mobile.nsNormalize(t))},b.find=function(e,t,i,s){return-1<e.indexOf(":jqmData")&&(e=e.replace(w,"[data-"+(b.mobile.ns||"")+"$1]")),_.call(this,e,t,i,s)},b.extend(b.find,_),x=/[A-Z]/g,(y=e).extend(y.Widget.prototype,{_getCreateOptions:function(){var e,t,i=this.element[0],s={};if(!y.mobile.getAttribute(i,"defaults"))for(e in this.options)null!=(t=y.mobile.getAttribute(i,e.replace(x,Ti)))&&(s[e]=t);return s}}),y.mobile.widget=y.Widget,k="ui-loader",P=(T=e)("html"),T.widget("mobile.loader",{options:{theme:"a",textVisible:!1,html:"",text:"loading"},defaultHtml:"<div class='"+k+"'><span class='ui-icon-loading'></span><h1></h1></div>",fakeFixLoader:function(){var e=T("."+T.mobile.activeBtnClass).first();this.element.css({top:T.support.scrollTop&&this.window.scrollTop()+this.window.height()/2||e.length&&e.offset().top||100})},checkLoaderPosition:function(){var e=this.element.offset(),t=this.window.scrollTop(),i=T.mobile.getScreenHeight();(e.top<t||e.top-t>i)&&(this.element.addClass("ui-loader-fakefix"),this.fakeFixLoader(),this.window.off("scroll",this.checkLoaderPosition).on("scroll",T.proxy(this.fakeFixLoader,this)))},resetHtml:function(){this.element.html(T(this.defaultHtml).html())},show:function(e,t,i){var s,n,o;this.resetHtml(),e="object"==typeof e?(o=T.extend({},this.options,e)).theme:(o=this.options,e||o.theme),n=t||(!1===o.text?"":o.text),P.addClass("ui-loading"),s=o.textVisible,this.element.attr("class",k+" ui-corner-all ui-body-"+e+" ui-loader-"+(s||t||e.text?"verbose":"default")+(o.textonly||i?" ui-loader-textonly":"")),o.html?this.element.html(o.html):this.element.find("h1").text(n),this.element.appendTo(T.mobile.pagecontainer?T(":mobile-pagecontainer"):T("body")),this.checkLoaderPosition(),this.window.on("scroll",T.proxy(this.checkLoaderPosition,this))},hide:function(){P.removeClass("ui-loading"),this.options.text&&this.element.removeClass("ui-loader-fakefix"),this.window.off("scroll",this.fakeFixLoader),this.window.off("scroll",this.checkLoaderPosition)}}),A=this,L="hashchange",j=E,G=(S=e).event.special,$=j.documentMode,M="on"+L in A&&(void 0===$||7<$),S.fn[L]=function(e){return e?this.on(L,e):this.trigger(L)},S.fn[L].delay=50,G[L]=S.extend(G[L],{setup:function(){if(M)return!1;S(I.start)},teardown:function(){if(M)return!1;S(I.stop)}}),G={},N=ki(),q=H=Pi,G.start=function(){B||Di()},G.stop=function(){B&&clearTimeout(B),B=void 0},!A.attachEvent||A.addEventListener||M||(G.start=function(){O||(F=(F=S.fn[L].src)&&F+ki(),O=S('<iframe tabindex="-1" title="empty"/>').hide().one("load",function(){F||H(ki()),Di()}).attr("src",F||"javascript:0").insertAfter("body")[0].contentWindow,j.onpropertychange=function(){try{"title"===event.propertyName&&(O.document.title=j.title)}catch(e){}})},G.stop=Pi,q=function(){return ki(O.location.href)},H=function(e,t){var i=O.document,s=S.fn[L].domain;e!==t&&(i.title=j.title,i.open(),s&&i.write('<script>document.domain="'+s+'"<\/script>'),i.close(),O.location.hash=e)}),I=G,Z=e,D.matchMedia=D.matchMedia||(R=(X=E).documentElement,W=R.firstElementChild||R.firstChild,z=X.createElement("body"),(K=X.createElement("div")).id="mq-test-1",K.style.cssText="position:absolute;top:-100em",z.style.background="none",z.appendChild(K),function(e){return K.innerHTML='&shy;<style media="'+e+'"> #mq-test-1 { width: 42px; }</style>',R.insertBefore(z,W),U=42===K.offsetWidth,R.removeChild(z),{matches:U,media:e}}),Z.mobile.media=function(e){return D.matchMedia(e).matches},Y=e,Q={touch:"ontouchend"in E},Y.mobile.support=Y.mobile.support||{},Y.extend(Y.support,Q),Y.extend(Y.mobile.support,Q),e.extend(e.support,{orientation:"orientation"in D&&"onorientationchange"in D}),J=(V=e)("<body>").prependTo("html"),ee=J[0].style,te=["Webkit","Moz","O"],Oe="palmGetResource"in D,Be=D.operamini&&"[object OperaMini]"==={}.toString.call(D.operamini),ie=D.blackberry&&!Ei("-webkit-transform"),V.extend(V.mobile,{browser:{}}),V.mobile.browser.oldIE=function(){for(var e=3,t=E.createElement("div"),i=t.all||[];t.innerHTML="\x3c!--[if gt IE "+ ++e+"]><br><![endif]--\x3e",i[0];);return 4<e?e:!e}(),V.extend(V.support,{pushState:"pushState"in history&&"replaceState"in history&&!(0<=D.navigator.userAgent.indexOf("Firefox")&&D.top!==D)&&-1===D.navigator.userAgent.search(/CriOS/),mediaquery:V.mobile.media("only all"),cssPseudoElement:!!Ei("content"),touchOverflow:!!Ei("overflowScrolling"),cssTransform3d:function(){var e,t,i,s="transform-3d",n=V.mobile.media("(-"+te.join("-"+s+"),(-")+"-"+s+"),("+s+")");if(n)return!!n;for(i in e=E.createElement("div"),t={MozTransform:"-moz-transform",transform:"transform"},J.append(e),t)void 0!==e.style[i]&&(e.style[i]="translate3d( 100px, 1px, 1px )",n=D.getComputedStyle(e).getPropertyValue(t[i]));return!!n&&"none"!==n}(),boxShadow:!!Ei("boxShadow")&&!ie,fixedPosition:($=D,G=navigator.userAgent,X=navigator.platform,Z=G.match(/AppleWebKit\/([0-9]+)/),Y=!!Z&&Z[1],ie=!!(Q=G.match(/Fennec\/([0-9]+)/))&&Q[1],Q=!!(Z=G.match(/Opera Mobi\/([0-9]+)/))&&Z[1],!((-1<X.indexOf("iPhone")||-1<X.indexOf("iPad")||-1<X.indexOf("iPod"))&&Y&&Y<534||$.operamini&&"[object OperaMini]"==={}.toString.call($.operamini)||Z&&Q<7458||-1<G.indexOf("Android")&&Y&&Y<533||ie&&ie<6||"palmGetResource"in D&&Y&&Y<534||-1<G.indexOf("MeeGo")&&-1<G.indexOf("NokiaBrowser/8.5.0"))),scrollTop:("pageXOffset"in D||"scrollTop"in E.documentElement||"scrollTop"in J[0])&&!Oe&&!Be,dynamicBaseTag:(Z=location.protocol+"//"+location.host+location.pathname+"ui-dir/",Q=V("head base"),ie=null,Y="",Q.length?Y=Q.attr("href"):Q=ie=V("<base>",{href:Z}).appendTo("head"),G=V("<a href='testurl' />").prependTo(J)[0].href,Q[0].href=Y||location.pathname,ie&&ie.remove(),0===G.indexOf(Z)),cssPointerEvents:(ie=E.createElement("x"),G=E.documentElement,Z=D.getComputedStyle,"pointerEvents"in ie.style&&(ie.style.pointerEvents="auto",ie.style.pointerEvents="x",G.appendChild(ie),Z=Z&&"auto"===Z(ie,"").pointerEvents,G.removeChild(ie),!!Z)),boundingRect:void 0!==E.createElement("div").getBoundingClientRect,inlineSVG:function(){function e(e){e&&i||V("html").addClass("ui-nosvg")}var t=D,i=!(!t.document.createElementNS||!t.document.createElementNS("http://www.w3.org/2000/svg","svg").createSVGRect||t.opera&&-1===navigator.userAgent.indexOf("Chrome")),s=new t.Image;s.onerror=function(){e(!1)},s.onload=function(){e(1===s.width&&1===s.height)},s.src="data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///ywAAAAAAQABAAACAUwAOw=="}}),J.remove(),Oe=-1<(Oe=D.navigator.userAgent).indexOf("Nokia")&&(-1<Oe.indexOf("Symbian/3")||-1<Oe.indexOf("Series60/5"))&&-1<Oe.indexOf("AppleWebKit")&&Oe.match(/(BrowserNG|NokiaBrowser)\/7\.[0-3]/),V.mobile.gradeA=function(){return(V.support.mediaquery&&V.support.cssPseudoElement||V.mobile.browser.oldIE&&8<=V.mobile.browser.oldIE)&&(V.support.boundingRect||null!==V.fn.jquery.match(/1\.[0-7+]\.[0-9+]?/))},V.mobile.ajaxBlacklist=D.blackberry&&!D.WebKitPoint||Be||Oe,Oe&&V(function(){V("head link[rel='stylesheet']").attr("rel","alternate stylesheet").attr("rel","stylesheet")}),V.support.boxShadow||V("html").addClass("ui-noboxshadow"),oe=(se=e).mobile.window,se.event.special.beforenavigate={setup:function(){oe.on("navigate",Si)},teardown:function(){oe.off("navigate",Si)}},se.event.special.navigate=ne={bound:!1,pushStateEnabled:!0,originalEventName:void 0,isPushStateEnabled:function(){return se.support.pushState&&!0===se.mobile.pushStateEnabled&&this.isHashChangeEnabled()},isHashChangeEnabled:function(){return!0===se.mobile.hashListeningEnabled},popstate:function(e){var t=new se.Event("navigate"),i=new se.Event("beforenavigate"),s=e.originalEvent.state||{};i.originalEvent=e,oe.trigger(i),i.isDefaultPrevented()||(e.historyState&&se.extend(s,e.historyState),t.originalEvent=e,setTimeout(function(){oe.trigger(t,{state:s})},0))},hashchange:function(e){var t=new se.Event("navigate"),i=new se.Event("beforenavigate");i.originalEvent=e,oe.trigger(i),i.isDefaultPrevented()||(t.originalEvent=e,oe.trigger(t,{state:e.hashchangeState||{}}))},setup:function(){ne.bound||(ne.bound=!0,ne.isPushStateEnabled()?(ne.originalEventName="popstate",oe.on("popstate.navigate",ne.popstate)):ne.isHashChangeEnabled()&&(ne.originalEventName="hashchange",oe.on("hashchange.navigate",ne.hashchange)))}},he="&ui-state=dialog",(ae=e).mobile.path=le={uiStateKey:"&ui-state",urlParseRE:/^\s*(((([^:\/\\#\?]+:)?(?:([\/\\]{2})((?:(([^:@\/\\#\?]*)(?:\:([^@\/\\#\?]*))?)@)?(([^:\/\\#\?\]\[]+|\[[^\/\\\]@#?]+\])(?:\:([0-9]+))?))?)?)?(([\/\\]?(?:[^\/\\\?#]+[\/\\]+)*)([^\?#]*)))?(\?[^#]+)?)(#.*)?/,getLocation:function(e){var t=this.parseUrl(e||location.href),i=e?t:location,e="#"===(e=t.hash)?"":e;return i.protocol+t.doubleSlash+i.host+(""!==i.protocol&&"/"!==i.pathname.substring(0,1)?"/":"")+i.pathname+i.search+e},getDocumentUrl:function(e){return e?ae.extend({},le.documentUrl):le.documentUrl.href},parseLocation:function(){return this.parseUrl(this.getLocation())},parseUrl:function(e){if("object"==typeof e)return e;e=le.urlParseRE.exec(e||"")||[];return{href:e[0]||"",hrefNoHash:e[1]||"",hrefNoSearch:e[2]||"",domain:e[3]||"",protocol:e[4]||"",doubleSlash:e[5]||"",authority:e[6]||"",username:e[8]||"",password:e[9]||"",host:e[10]||"",hostname:e[11]||"",port:e[12]||"",pathname:e[13]||"",directory:e[14]||"",filename:e[15]||"",search:e[16]||"",hash:e[17]||""}},makePathAbsolute:function(e,t){var i,s,n,o;if(e&&"/"===e.charAt(0))return e;for(e=e||"",i=(t=t?t.replace(/^\/|(\/[^\/]*|[^\/]+)$/g,""):"")?t.split("/"):[],s=e.split("/"),n=0;n<s.length;n++)switch(o=s[n]){case".":break;case"..":i.length&&i.pop();break;default:i.push(o)}return"/"+i.join("/")},isSameDomain:function(e,t){return le.parseUrl(e).domain.toLowerCase()===le.parseUrl(t).domain.toLowerCase()},isRelativeUrl:function(e){return""===le.parseUrl(e).protocol},isAbsoluteUrl:function(e){return""!==le.parseUrl(e).protocol},makeUrlAbsolute:function(e,t){if(!le.isRelativeUrl(e))return e;t===re&&(t=this.documentBase);var i=le.parseUrl(e),s=le.parseUrl(t),n=i.protocol||s.protocol,o=i.protocol?i.doubleSlash:i.doubleSlash||s.doubleSlash,e=i.authority||s.authority,t=""!==i.pathname;return n+o+e+le.makePathAbsolute(i.pathname||s.filename,s.pathname)+(i.search||!t&&s.search||"")+i.hash},addSearchParams:function(e,t){var i=le.parseUrl(e),e="object"==typeof t?ae.param(t):t,t=i.search||"?";return i.hrefNoSearch+t+("?"!==t.charAt(t.length-1)?"&":"")+e+(i.hash||"")},convertUrlToDataUrl:function(e){var t=e,e=le.parseUrl(e);return le.isEmbeddedPage(e)?t=e.hash.split(he)[0].replace(/^#/,"").replace(/\?.*$/,""):le.isSameDomain(e,this.documentBase)&&(t=e.hrefNoHash.replace(this.documentBase.domain,"").split(he)[0]),D.decodeURIComponent(t)},get:function(e){return e===re&&(e=le.parseLocation().hash),le.stripHash(e).replace(/[^\/]*\.[^\/*]+$/,"")},set:function(e){location.hash=e},isPath:function(e){return/\//.test(e)},clean:function(e){return e.replace(this.documentBase.domain,"")},stripHash:function(e){return e.replace(/^#/,"")},stripQueryParams:function(e){return e.replace(/\?.*$/,"")},cleanHash:function(e){return le.stripHash(e.replace(/\?.*$/,"").replace(he,""))},isHashValid:function(e){return/^#[^#]+$/.test(e)},isExternal:function(e){e=le.parseUrl(e);return!(!e.protocol||e.domain.toLowerCase()===this.documentUrl.domain.toLowerCase())},hasProtocol:function(e){return/^(:?\w+:)/.test(e)},isEmbeddedPage:function(e){e=le.parseUrl(e);return""!==e.protocol?!this.isPath(e.hash)&&e.hash&&(e.hrefNoHash===this.documentUrl.hrefNoHash||this.documentBaseDiffers&&e.hrefNoHash===this.documentBase.hrefNoHash):/^#/.test(e.href)},squash:function(e,t){var i,s=this.isPath(e),n=this.parseUrl(e),o=n.hash,a="";return t=t||(s?le.getLocation():(i=le.getDocumentUrl(!0),le.isPath(i.hash)?le.squash(i.href):i.href)),e=s?le.stripHash(e):e,-1<(n=(e=le.isPath(n.hash)?le.stripHash(n.hash):e).indexOf(this.uiStateKey))&&(a=e.slice(n),e=e.slice(0,n)),e=le.makeUrlAbsolute(e,t),t=this.parseUrl(e).search,s?(!le.isPath(o)&&0!==o.replace("#","").indexOf(this.uiStateKey)||(o=""),a&&-1===o.indexOf(this.uiStateKey)&&(o+=a),-1===o.indexOf("#")&&""!==o&&(o="#"+o),e=(e=le.parseUrl(e)).protocol+e.doubleSlash+e.host+e.pathname+t+o):e+=-1<e.indexOf("#")?a:"#"+a,e},isPreservableHash:function(e){return 0===e.replace("#","").indexOf(this.uiStateKey)},hashToSelector:function(e){var t="#"===e.substring(0,1);return(t?"#":"")+(e=t?e.substring(1):e).replace(/([!"#$%&'()*+,./:;<=>?@[\]^`{|}~])/g,"\\$1")},getFilePath:function(e){return e&&e.split(he)[0]},isFirstPageUrl:function(e){var t=le.parseUrl(le.makeUrlAbsolute(e,this.documentBase)),i=t.hrefNoHash===this.documentUrl.hrefNoHash||this.documentBaseDiffers&&t.hrefNoHash===this.documentBase.hrefNoHash,e=ae.mobile.firstPage,e=e&&e[0]?e[0].id:re;return i&&(!t.hash||"#"===t.hash||e&&t.hash.replace(/^#/,"")===e)},isPermittedCrossDomainRequest:function(e,t){return ae.mobile.allowCrossDomainPages&&("file:"===e.protocol||"content:"===e.protocol)&&-1!==t.search(/^https?:/)}},le.documentUrl=le.parseLocation(),Be=ae("head").find("base"),le.documentBase=Be.length?le.parseUrl(le.makeUrlAbsolute(Be.attr("href"),le.documentUrl.href)):le.documentUrl,le.documentBaseDiffers=le.documentUrl.hrefNoHash!==le.documentBase.hrefNoHash,le.getDocumentBase=function(e){return e?ae.extend({},le.documentBase):le.documentBase.href},ae.extend(ae.mobile,{getDocumentUrl:le.getDocumentUrl,getDocumentBase:le.getDocumentBase}),(de=e).mobile.History=function(e,t){this.stack=e||[],this.activeIndex=t||0},de.extend(de.mobile.History.prototype,{getActive:function(){return this.stack[this.activeIndex]},getLast:function(){return this.stack[this.previousIndex]},getNext:function(){return this.stack[this.activeIndex+1]},getPrev:function(){return this.stack[this.activeIndex-1]},add:function(e,t){t=t||{},this.getNext()&&this.clearForward(),t.hash&&-1===t.hash.indexOf("#")&&(t.hash="#"+t.hash),t.url=e,this.stack.push(t),this.activeIndex=this.stack.length-1},clearForward:function(){this.stack=this.stack.slice(0,this.activeIndex+1)},find:function(e,t,i){for(var s,n,o=(t=t||this.stack).length,a=0;a<o;a++)if(s=t[a],(decodeURIComponent(e)===decodeURIComponent(s.url)||decodeURIComponent(e)===decodeURIComponent(s.hash))&&(n=a,i))return n;return n},closest:function(e){var t=this.activeIndex,i=this.find(e,this.stack.slice(0,t));return i=i===ce?(i=this.find(e,this.stack.slice(t),!0))===ce?i:i+t:i},direct:function(e){var t=this.closest(e.url),i=this.activeIndex;t!==ce&&(this.activeIndex=t,this.previousIndex=i),t<i?(e.present||e.back||de.noop)(this.getActive(),"back"):i<t?(e.present||e.forward||de.noop)(this.getActive(),"forward"):t===ce&&e.missing&&e.missing(this.getActive())}}),pe=(ue=e).mobile.path,me=location.href,ue.mobile.Navigator=function(e){this.history=e,this.ignoreInitialHashChange=!0,ue.mobile.window.on({"popstate.history":ue.proxy(this.popstate,this),"hashchange.history":ue.proxy(this.hashchange,this)})},ue.extend(ue.mobile.Navigator.prototype,{squash:function(e,t){var i=pe.isPath(e)?pe.stripHash(e):e,e=pe.squash(e),t=ue.extend({hash:i,url:e},t);return D.history.replaceState(t,t.title||E.title,e),t},hash:function(e,t){var i=pe.parseUrl(e),s=pe.parseLocation(),e=s.pathname+s.search===i.pathname+i.search?i.hash||i.pathname+i.search:pe.isPath(e)?(t=pe.parseUrl(t)).pathname+t.search+(pe.isPreservableHash(t.hash)?t.hash.replace("#",""):""):e;return e},go:function(e,t,i){var s=ue.event.special.navigate.isPushStateEnabled(),n=pe.squash(e),o=this.hash(e,n);i&&o!==pe.stripHash(pe.parseLocation().hash)&&(this.preventNextHashChange=i),this.preventHashAssignPopState=!0,D.location.hash=o,this.preventHashAssignPopState=!1,t=ue.extend({url:n,hash:o,title:E.title},t),s&&((s=new ue.Event("popstate")).originalEvent={type:"popstate",state:null},this.squash(e,t),i||(this.ignorePopState=!0,ue.mobile.window.trigger(s))),this.history.add(t.url,t)},popstate:function(i){var e,t;if(ue.event.special.navigate.isPushStateEnabled()){if(this.preventHashAssignPopState)return this.preventHashAssignPopState=!1,void i.stopImmediatePropagation();if(this.ignorePopState)this.ignorePopState=!1;else if(i.originalEvent.state||1!==this.history.stack.length||!this.ignoreInitialHashChange||(this.ignoreInitialHashChange=!1,location.href!==me)){if(e=pe.parseLocation().hash,!i.originalEvent.state&&e)return t=this.squash(e),this.history.add(t.url,t),void(i.historyState=t);this.history.direct({url:(i.originalEvent.state||{}).url||e,present:function(e,t){i.historyState=ue.extend({},e),i.historyState.direction=t}})}else i.preventDefault()}},hashchange:function(i){var e,t;if(ue.event.special.navigate.isHashChangeEnabled()&&!ue.event.special.navigate.isPushStateEnabled()){if(this.preventNextHashChange)return this.preventNextHashChange=!1,void i.stopImmediatePropagation();e=this.history,t=pe.parseLocation().hash,this.history.direct({url:t,present:function(e,t){i.hashchangeState=ue.extend({},e),i.hashchangeState.direction=t},missing:function(){e.add(t,{hash:t,title:E.title})}})}}}),function(s){s.mobile.navigate=function(e,t,i){s.mobile.navigate.navigator.go(e,t,i)},s.mobile.navigate.history=new s.mobile.History,s.mobile.navigate.navigator=new s.mobile.Navigator(s.mobile.navigate.history);var e=s.mobile.path.parseLocation();s.mobile.navigate.history.add(e.href,{hash:e.hash})}(e),fe=e,be={animation:{},transition:{}},ve=E.createElement("a"),_e=["","webkit-","moz-","o-"],fe.each(["animation","transition"],function(e,i){var s=0===e?i+"-name":i;fe.each(_e,function(e,t){if(ve.style[fe.camelCase(t+s)]!==ge)return be[i].prefix=t,!1}),be[i].duration=fe.camelCase(be[i].prefix+i+"-duration"),be[i].event=fe.camelCase(be[i].prefix+i+"-end"),""===be[i].prefix&&(be[i].event=be[i].event.toLowerCase())}),fe.support.cssTransitions=be.transition.prefix!==ge,fe.support.cssAnimations=be.animation.prefix!==ge,fe(ve).remove(),fe.fn.animationComplete=function(e,t,i){function s(){clearTimeout(n),e.apply(this,arguments)}var n,o,a=this,r=t&&"animation"!==t?"transition":"animation";return fe.support.cssTransitions&&"transition"==r||fe.support.cssAnimations&&"animation"==r?(i===ge&&(0!==(o=fe(this).context!==E?3e3*parseFloat(fe(this).css(be[r].duration)):o)&&o!==ge&&!isNaN(o)||(o=fe.fn.animationComplete.defaultDuration)),n=setTimeout(function(){fe(a).off(be[r].event,s),e.apply(a)},o),fe(this).one(be[r].event,s)):(setTimeout(fe.proxy(e,this),0),fe(this))},fe.fn.animationComplete.defaultDuration=1e3,function(d,e,c){var l,t,n="virtualMouseBindings",h="virtualTouchID",i="vmouseover vmousedown vmousemove vmouseup vclick vmouseout vmousecancel".split(" "),u="altKey bubbles cancelable ctrlKey currentTarget detail eventPhase metaKey relatedTarget shiftKey target timeStamp view which".split(" "),p="clientX clientY pageX pageY screenX screenY".split(" "),s=d.event.mouseHooks?d.event.mouseHooks.props:[],m=u.concat(s),o={},a=0,r=0,f=0,g=!1,b=[],v=!1,_=!1,C="addEventListener"in e,w=d(e),y=1,x=0;function T(e){for(;e&&void 0!==e.originalEvent;)e=e.originalEvent;return e}function k(e){for(var t,i,s={};e;){for(i in t=d.data(e,n))t[i]&&(s[i]=s.hasVirtualBinding=!0);e=e.parentNode}return s}function P(){_=!0}function D(){_=!1}function E(){S(),a=setTimeout(function(){x=a=0,b.length=0,v=!1,P()},d.vmouse.resetTimerDuration)}function S(){a&&(clearTimeout(a),a=0)}function A(e,t,i){var s;return(i&&i[e]||!i&&function(e,t){for(var i;e;){if((i=d.data(e,n))&&(!t||i[t]))return e;e=e.parentNode}}(t.target,e))&&(s=function(e,t){var i,s,n,o,a,r,l,h=e.type;if((e=d.Event(e)).type=t,i=e.originalEvent,s=u,-1<h.search(/^(mouse|click)/)&&(s=m),i)for(a=s.length;a;)e[n=s[--a]]=i[n];if(-1<h.search(/mouse(down|up)|click/)&&!e.which&&(e.which=1),-1!==h.search(/^touch/)&&(h=(t=T(i)).touches,t=t.changedTouches,o=h&&h.length?h[0]:t&&t.length?t[0]:c))for(r=0,l=p.length;r<l;r++)e[n=p[r]]=o[n];return e}(t,e),d(t.target).trigger(s)),s}function I(e){var t=d.data(e.target,h);v||x&&x===t||(t=A("v"+e.type,e))&&(t.isDefaultPrevented()&&e.preventDefault(),t.isPropagationStopped()&&e.stopPropagation(),t.isImmediatePropagationStopped()&&e.stopImmediatePropagation())}function B(e){var t,i,s=T(e).touches;s&&1===s.length&&(t=k(i=e.target)).hasVirtualBinding&&(x=y++,d.data(i,h,x),S(),D(),g=!1,i=T(e).touches[0],r=i.pageX,f=i.pageY,A("vmouseover",e,t),A("vmousedown",e,t))}function O(e){_||(g||A("vmousecancel",e,k(e.target)),g=!0,E())}function F(e){var t,i,s,n;_||(t=T(e).touches[0],i=g,s=d.vmouse.moveDistanceThreshold,n=k(e.target),(g=g||Math.abs(t.pageX-r)>s||Math.abs(t.pageY-f)>s)&&!i&&A("vmousecancel",e,n),A("vmousemove",e,n),E())}function N(e){var t,i;_||(P(),A("vmouseup",e,t=k(e.target)),g||(i=A("vclick",e,t))&&i.isDefaultPrevented()&&(i=T(e).changedTouches[0],b.push({touchID:x,x:i.clientX,y:i.clientY}),v=!0),A("vmouseout",e,t),g=!1,E())}function H(e){var t,i=d.data(e,n);if(i)for(t in i)if(i[t])return 1}function q(){}for(d.vmouse={moveDistanceThreshold:10,clickDistanceThreshold:10,resetTimerDuration:1500},t=0;t<i.length;t++)d.event.special[i[t]]=function(i){var s=i.substr(1);return{setup:function(){H(this)||d.data(this,n,{}),d.data(this,n)[i]=!0,o[i]=(o[i]||0)+1,1===o[i]&&w.on(s,I),d(this).on(s,q),C&&(o.touchstart=(o.touchstart||0)+1,1===o.touchstart&&w.on("touchstart",B).on("touchend",N).on("touchmove",F).on("scroll",O))},teardown:function(){--o[i],o[i]||w.off(s,I),C&&(--o.touchstart,o.touchstart||w.off("touchstart",B).off("touchmove",F).off("touchend",N).off("scroll",O));var e=d(this),t=d.data(this,n);t&&(t[i]=!1),e.off(s,q),H(this)||e.removeData(n)}}}(i[t]);C&&e.addEventListener("click",function(e){var t,i,s,n,o,a=b.length,r=e.target;if(a)for(t=e.clientX,i=e.clientY,l=d.vmouse.clickDistanceThreshold,s=r;s;){for(n=0;n<a;n++)if(o=b[n],s===r&&Math.abs(o.x-t)<l&&Math.abs(o.y-i)<l||d.data(s,h)===o.touchID)return e.preventDefault(),void e.stopPropagation();s=s.parentNode}},!0)}(e,E),we=this,xe=(Ce=e)(E),Oe=Ce.mobile.support.touch,Te="touchmove scroll",ke=Oe?"touchstart":"mousedown",Pe=Oe?"touchend":"mouseup",De=Oe?"touchmove":"mousemove",Ce.each("touchstart touchmove touchend tap taphold swipe swipeleft swiperight scrollstart scrollstop".split(" "),function(e,t){Ce.fn[t]=function(e){return e?this.on(t,e):this.trigger(t)},Ce.attrFn&&(Ce.attrFn[t]=!0)}),Ce.event.special.scrollstart={enabled:!0,setup:function(){var i,t,s=this;function n(e,t){Ai(s,(i=t)?"scrollstart":"scrollstop",e)}Ce(s).on(Te,function(e){Ce.event.special.scrollstart.enabled&&(i||n(e,!0),clearTimeout(t),t=setTimeout(function(){n(e,!1)},50))})},teardown:function(){Ce(this).off(Te)}},Ce.event.special.tap={tapholdThreshold:750,emitTapOnTaphold:!0,setup:function(){var a=this,r=Ce(a),l=!1;r.on("vmousedown",function(e){if(l=!1,e.which&&1!==e.which)return!1;var t,i=e.target;function s(){clearTimeout(t)}function n(){s(),r.off("vclick",o).off("vmouseup",s),xe.off("vmousecancel",n)}function o(e){n(),l||i!==e.target?l&&e.preventDefault():Ai(a,"tap",e)}r.on("vmouseup",s).on("vclick",o),xe.on("vmousecancel",n),t=setTimeout(function(){Ce.event.special.tap.emitTapOnTaphold||(l=!0),Ai(a,"taphold",Ce.Event("taphold",{target:i}))},Ce.event.special.tap.tapholdThreshold)})},teardown:function(){Ce(this).off("vmousedown").off("vclick").off("vmouseup"),xe.off("vmousecancel")}},Ce.event.special.swipe={scrollSupressionThreshold:30,durationThreshold:1e3,horizontalDistanceThreshold:30,verticalDistanceThreshold:30,getLocation:function(e){var t=we.pageXOffset,i=we.pageYOffset,s=e.clientX,n=e.clientY;return 0===e.pageY&&Math.floor(n)>Math.floor(e.pageY)||0===e.pageX&&Math.floor(s)>Math.floor(e.pageX)?(s-=t,n-=i):(n<e.pageY-i||s<e.pageX-t)&&(s=e.pageX-t,n=e.pageY-i),{x:s,y:n}},start:function(e){var t=e.originalEvent.touches?e.originalEvent.touches[0]:e,t=Ce.event.special.swipe.getLocation(t);return{time:(new Date).getTime(),coords:[t.x,t.y],origin:Ce(e.target)}},stop:function(e){e=e.originalEvent.touches?e.originalEvent.touches[0]:e,e=Ce.event.special.swipe.getLocation(e);return{time:(new Date).getTime(),coords:[e.x,e.y]}},handleSwipe:function(e,t,i,s){if(t.time-e.time<Ce.event.special.swipe.durationThreshold&&Math.abs(e.coords[0]-t.coords[0])>Ce.event.special.swipe.horizontalDistanceThreshold&&Math.abs(e.coords[1]-t.coords[1])<Ce.event.special.swipe.verticalDistanceThreshold){var n=e.coords[0]>t.coords[0]?"swipeleft":"swiperight";return Ai(i,"swipe",Ce.Event("swipe",{target:s,swipestart:e,swipestop:t}),!0),Ai(i,n,Ce.Event(n,{target:s,swipestart:e,swipestop:t}),!0),!0}return!1},eventInProgress:!1,setup:function(){var o=this,e=Ce(o),a={},t=Ce.data(this,"mobile-events");t||Ce.data(this,"mobile-events",t={length:0}),t.length++,(t.swipe=a).start=function(e){var t,i,s,n;Ce.event.special.swipe.eventInProgress||(Ce.event.special.swipe.eventInProgress=!0,i=Ce.event.special.swipe.start(e),s=e.target,n=!1,a.move=function(e){i&&!e.isDefaultPrevented()&&(t=Ce.event.special.swipe.stop(e),n||(n=Ce.event.special.swipe.handleSwipe(i,t,o,s))&&(Ce.event.special.swipe.eventInProgress=!1),Math.abs(i.coords[0]-t.coords[0])>Ce.event.special.swipe.scrollSupressionThreshold&&e.preventDefault())},a.stop=function(){n=!0,Ce.event.special.swipe.eventInProgress=!1,xe.off(De,a.move),a.move=null},xe.on(De,a.move).one(Pe,a.stop))},e.on(ke,a.start)},teardown:function(){var e,t=Ce.data(this,"mobile-events");t&&(e=t.swipe,delete t.swipe,t.length--,0===t.length&&Ce.removeData(this,"mobile-events")),e&&(e.start&&Ce(this).off(ke,e.start),e.move&&xe.off(De,e.move),e.stop&&xe.off(Pe,e.stop))}},Ce.each({scrollstop:"scrollstart",taphold:"tap",swipeleft:"swipe.left",swiperight:"swipe.right"},function(e,t){Ce.event.special[e]={setup:function(){Ce(this).on(t,Ce.noop)},teardown:function(){Ce(this).off(t)}}}),function(e){e.event.special.throttledresize={setup:function(){e(this).on("resize",n)},teardown:function(){e(this).off("resize",n)}};var t,i,s,n=function(){i=(new Date).getTime(),250<=(s=i-o)?(o=i,e(this).trigger("throttledresize")):(t&&clearTimeout(t),t=setTimeout(n,250-s))},o=0}(e),Fe=(Ee=e)(Se=this),Ne="orientationchange",He={0:!0,180:!0},Ee.support.orientation&&(Be=Se.innerWidth||Fe.width(),Oe=Se.innerHeight||Fe.height(),Xe=He[Se.orientation],((Oe=Oe<Be&&50<Be-Oe)&&Xe||!Oe&&!Xe)&&(He={"-90":!0,90:!0})),Ee.event.special.orientationchange=Ee.extend({},Ee.event.special.orientationchange,{setup:function(){if(Ee.support.orientation&&!Ee.event.special.orientationchange.disabled)return!1;Ie=Ae(),Fe.on("throttledresize",Ii)},teardown:function(){if(Ee.support.orientation&&!Ee.event.special.orientationchange.disabled)return!1;Fe.off("throttledresize",Ii)},add:function(e){var t=e.handler;e.handler=function(e){return e.orientation=Ae(),t.apply(this,arguments)}}}),Ee.event.special.orientationchange.orientation=Ae=function(){var e=E.documentElement;return(Ee.support.orientation?He[Se.orientation]:e&&e.clientWidth/e.clientHeight<1.1)?"portrait":"landscape"},Ee.fn[Ne]=function(e){return e?this.on(Ne,e):this.trigger(Ne)},Ee.attrFn&&(Ee.attrFn[Ne]=!0),Xe=(qe=e)("head").children("base"),Le={element:Xe.length?Xe:qe("<base>",{href:qe.mobile.path.documentBase.hrefNoHash}).prependTo(qe("head")),linkSelector:"[src], link[href], a[rel='external'], :jqmData(ajax='false'), a[target]",set:function(e){qe.mobile.dynamicBaseEnabled&&qe.support.dynamicBaseTag&&Le.element.attr("href",qe.mobile.path.makeUrlAbsolute(e,qe.mobile.path.documentBase))},rewrite:function(e,t){var o=qe.mobile.path.get(e);t.find(Le.linkSelector).each(function(e,t){var i=qe(t).is("[href]")?"href":qe(t).is("[src]")?"src":"action",s=qe.mobile.path.parseLocation(),n=(n=qe(t).attr(i)).replace(s.protocol+s.doubleSlash+s.host+s.pathname,"");/^(\w+:|#|\/)/.test(n)||qe(t).attr(i,o+n)})},reset:function(){Le.element.attr("href",qe.mobile.path.documentBase.hrefNoSearch)}},qe.mobile.base=Le,function(n,o){n.mobile.widgets={};var i,e=n.widget,s=n.mobile.keepNative;n.widget=(i=n.widget,function(){var e=i.apply(this,arguments),t=e.prototype.widgetName;return e.initSelector=e.prototype.initSelector!==o?e.prototype.initSelector:":jqmData(role='"+t+"')",n.mobile.widgets[t]=e}),n.extend(n.widget,e),n.mobile.document.on("create",function(e){n(e.target).enhanceWithin()}),n.widget("mobile.page",{options:{theme:"a",domCache:!1,keepNativeDefault:n.mobile.keepNative,contentTheme:null,enhanced:!1},_createWidget:function(){n.Widget.prototype._createWidget.apply(this,arguments),this._trigger("init")},_create:function(){if(!1===this._trigger("beforecreate"))return!1;this.options.enhanced||this._enhance(),this._on(this.element,{pagebeforehide:"removeContainerBackground",pagebeforeshow:"_handlePageBeforeShow"}),this.element.enhanceWithin(),"dialog"===n.mobile.getAttribute(this.element[0],"role")&&n.mobile.dialog&&this.element.dialog()},_enhance:function(){var i="data-"+n.mobile.ns,s=this;this.options.role&&this.element.attr("data-"+n.mobile.ns+"role",this.options.role),this.element.attr("tabindex","0").addClass("ui-page ui-page-theme-"+this.options.theme),this.element.find("["+i+"role='content']").each(function(){var e=n(this),t=this.getAttribute(i+"theme")||o;s.options.contentTheme=t||s.options.contentTheme||s.options.dialog&&s.options.theme||"dialog"===s.element.jqmData("role")&&s.options.theme,e.addClass("ui-content"),s.options.contentTheme&&e.addClass("ui-body-"+s.options.contentTheme),e.attr("role","main").addClass("ui-content")})},bindRemove:function(e){var t=this.element;!t.data("mobile-page").options.domCache&&t.is(":jqmData(external-page='true')")&&t.on("pagehide.remove",e||function(e,t){var i;t.samePage||(i=n(this),t=new n.Event("pageremove"),i.trigger(t),t.isDefaultPrevented()||i.removeWithDependents())})},_setOptions:function(e){e.theme!==o&&this.element.removeClass("ui-page-theme-"+this.options.theme).addClass("ui-page-theme-"+e.theme),e.contentTheme!==o&&this.element.find("[data-"+n.mobile.ns+"='content']").removeClass("ui-body-"+this.options.contentTheme).addClass("ui-body-"+e.contentTheme)},_handlePageBeforeShow:function(){this.setContainerBackground()},removeContainerBackground:function(){this.element.closest(":mobile-pagecontainer").pagecontainer({theme:"none"})},setContainerBackground:function(e){this.element.parent().pagecontainer({theme:e||this.options.theme})},keepNativeSelector:function(){var e=this.options,t=String.prototype.trim(e.keepNative||""),i=String.prototype.trim(n.mobile.keepNative),e=String.prototype.trim(e.keepNativeDefault),i=s===i?"":i,e=""===i?e:"";return(t?[t]:[]).concat(i?[i]:[]).concat(e?[e]:[]).join(", ")}})}(e),function(p,m){p.widget("mobile.pagecontainer",{options:{theme:"a"},initSelector:!1,_create:function(){this._trigger("beforecreate"),this.setLastScrollEnabled=!0,this._on(this.window,{navigate:"_disableRecordScroll",scrollstop:"_delayedRecordScroll"}),this._on(this.window,{navigate:"_filterNavigateEvents"}),this._on({pagechange:"_afterContentChange"}),this.window.one("navigate",p.proxy(function(){this.setLastScrollEnabled=!0},this))},_setOptions:function(e){e.theme!==m&&"none"!==e.theme?this.element.removeClass("ui-overlay-"+this.options.theme).addClass("ui-overlay-"+e.theme):e.theme!==m&&this.element.removeClass("ui-overlay-"+this.options.theme),this._super(e)},_disableRecordScroll:function(){this.setLastScrollEnabled=!1},_enableRecordScroll:function(){this.setLastScrollEnabled=!0},_afterContentChange:function(){this.setLastScrollEnabled=!0,this._off(this.window,"scrollstop"),this._on(this.window,{scrollstop:"_delayedRecordScroll"})},_recordScroll:function(){var e,t,i,s;!this.setLastScrollEnabled||(e=this._getActiveHistory())&&(t=this._getScroll(),i=this._getMinScroll(),s=this._getDefaultScroll(),e.lastScroll=t<i?s:t)},_delayedRecordScroll:function(){setTimeout(p.proxy(this,"_recordScroll"),100)},_getScroll:function(){return this.window.scrollTop()},_getMinScroll:function(){return p.mobile.minScrollBack},_getDefaultScroll:function(){return p.mobile.defaultHomeScroll},_filterNavigateEvents:function(e,t){e.originalEvent&&e.originalEvent.isDefaultPrevented()||((e=(e=-1<e.originalEvent.type.indexOf("hashchange")?t.state.hash:t.state.url)||this._getHash())&&"#"!==e&&0!==e.indexOf("#"+p.mobile.path.uiStateKey)||(e=location.href),this._handleNavigate(e,t.state))},_getHash:function(){return p.mobile.path.parseLocation().hash},getActivePage:function(){return this.activePage},_getInitialContent:function(){return p.mobile.firstPage},_getHistory:function(){return p.mobile.navigate.history},_getActiveHistory:function(){return this._getHistory().getActive()},_getDocumentBase:function(){return p.mobile.path.documentBase},back:function(){this.go(-1)},forward:function(){this.go(1)},go:function(e){var t,i,s;p.mobile.hashListeningEnabled?D.history.go(e):(i=(t=p.mobile.navigate.history.activeIndex)+parseInt(e,10),s=p.mobile.navigate.history.stack[i].url,e=1<=e?"forward":"back",p.mobile.navigate.history.activeIndex=i,p.mobile.navigate.history.previousIndex=t,this.change(s,{direction:e,changeHash:!1,fromHashChange:!0}))},_handleDestination:function(e){return(e="string"==typeof e?p.mobile.path.stripHash(e):e)&&(this._getHistory(),e=p.mobile.path.isPath(e)?e:p.mobile.path.makeUrlAbsolute("#"+e,this._getDocumentBase())),e||this._getInitialContent()},_transitionFromHistory:function(e,t){var i=this._getHistory(),i="back"===e?i.getLast():i.getActive();return i&&i.transition||t},_handleDialog:function(e,t){var i,s=this.getActivePage();return s&&!s.data("mobile-dialog")?("back"===t.direction?this.back():this.forward(),!1):(i=t.pageUrl,s=this._getActiveHistory(),p.extend(e,{role:s.role,transition:this._transitionFromHistory(t.direction,e.transition),reverse:"back"===t.direction}),i)},_handleNavigate:function(e,t){var i=p.mobile.path.stripHash(e),s=this._getHistory(),n=0===s.stack.length?"none":this._transitionFromHistory(t.direction),e={changeHash:!1,fromHashChange:!0,reverse:"back"===t.direction};p.extend(e,t,{transition:n}),0<s.activeIndex&&-1<i.indexOf(p.mobile.dialogHashKey)&&!1===(i=this._handleDialog(e,t))||this._changeContent(this._handleDestination(i),e)},_changeContent:function(e,t){p.mobile.changePage(e,t)},_getBase:function(){return p.mobile.base},_getNs:function(){return p.mobile.ns},_enhance:function(e,t){return e.page({role:t})},_include:function(e,t){e.appendTo(this.element),this._enhance(e,t.role),e.page("bindRemove")},_find:function(e){var t=this._createFileUrl(e),i=this._createDataUrl(e),s=this._getInitialContent(),e=this.element.children("[data-"+this._getNs()+"url='"+p.mobile.path.hashToSelector(i)+"']");return e=0===(e=0===e.length&&i&&!p.mobile.path.isPath(i)?this.element.children(p.mobile.path.hashToSelector("#"+i)).attr("data-"+this._getNs()+"url",i).jqmData("url",i):e).length&&p.mobile.path.isFirstPageUrl(t)&&s&&s.parent().length?p(s):e},_getLoader:function(){return p.mobile.loading()},_showLoading:function(e,t,i,s){this._loadMsg||(this._loadMsg=setTimeout(p.proxy(function(){this._getLoader().loader("show",t,i,s),this._loadMsg=0},this),e))},_hideLoading:function(){clearTimeout(this._loadMsg),this._loadMsg=0,this._getLoader().loader("hide")},_showError:function(){this._hideLoading(),this._showLoading(0,p.mobile.pageLoadErrorMessageTheme,p.mobile.pageLoadErrorMessage,!0),setTimeout(p.proxy(this,"_hideLoading"),1500)},_parse:function(e,t){var i=p("<div></div>");return i.get(0).innerHTML=e,(i=!(i=i.find(":jqmData(role='page'), :jqmData(role='dialog')").first()).length?p("<div data-"+this._getNs()+"role='page'>"+(e.split(/<\/?body[^>]*>/gim)[1]||"")+"</div>"):i).attr("data-"+this._getNs()+"url",this._createDataUrl(t)).attr("data-"+this._getNs()+"external-page",!0),i},_setLoadedTitle:function(e,t){t=t.match(/<title[^>]*>([^<]*)/)&&RegExp.$1;t&&!e.jqmData("title")&&(t=p("<div>"+t+"</div>").text(),e.jqmData("title",t))},_isRewritableBaseTag:function(){return p.mobile.dynamicBaseEnabled&&!p.support.dynamicBaseTag},_createDataUrl:function(e){return p.mobile.path.convertUrlToDataUrl(e)},_createFileUrl:function(e){return p.mobile.path.getFilePath(e)},_triggerWithDeprecated:function(e,t,i){var s=p.Event("page"+e),n=p.Event(this.widgetName+e);return(i||this.element).trigger(s,t),this._trigger(e,n,t),{deprecatedEvent:s,event:n}},_loadSuccess:function(o,a,r,l){var h=this._createFileUrl(o);return p.proxy(function(e,t,i){var s,n;/^text\/html\b/.test(i.getResponseHeader("Content-Type"))?(s=new RegExp("(<[^>]+\\bdata-"+this._getNs()+"role=[\"']?page[\"']?[^>]*>)"),n=new RegExp("\\bdata-"+this._getNs()+"url=[\"']?([^\"'>]*)[\"']?"),s.test(e)&&RegExp.$1&&n.test(RegExp.$1)&&RegExp.$1&&(h=p.mobile.path.getFilePath(p("<div>"+RegExp.$1+"</div>").text()),h=this.window[0].encodeURIComponent(h)),r.prefetch===m&&this._getBase().set(h),n=this._parse(e,h),this._setLoadedTitle(n,e),a.xhr=i,a.textStatus=t,a.page=n,a.content=n,a.toPage=n,this._triggerWithDeprecated("load",a).event.isDefaultPrevented()||(this._isRewritableBaseTag()&&n&&this._getBase().rewrite(h,n),this._include(n,r),r.showLoadMsg&&this._hideLoading(),l.resolve(o,r,n))):r.showLoadMsg&&this._showError()},this)},_loadDefaults:{type:"get",data:m,reloadPage:!1,reload:!1,role:m,showLoadMsg:!1,loadMsgDelay:50},load:function(e,t){var i,s,n=t&&t.deferred||p.Deferred(),o=t&&t.reload===m&&t.reloadPage!==m?{reload:t.reloadPage}:{},a=p.extend({},this._loadDefaults,t,o),r=p.mobile.path.makeUrlAbsolute(e,this._findBaseWithDefault());return a.data&&"get"===a.type&&(r=p.mobile.path.addSearchParams(r,a.data),a.data=m),a.data&&"post"===a.type&&(a.reload=!0),s=this._createFileUrl(r),o=this._createDataUrl(r),0===(i=this._find(r)).length&&p.mobile.path.isEmbeddedPage(s)&&!p.mobile.path.isFirstPageUrl(s)?(n.reject(r,a),n.promise()):(this._getBase().reset(),i.length&&!a.reload?(this._enhance(i,a.role),n.resolve(r,a,i),a.prefetch||this._getBase().set(e),n.promise()):(t={url:e,absUrl:r,toPage:e,prevPage:t?t.fromPage:m,dataUrl:o,deferred:n,options:a},(o=this._triggerWithDeprecated("beforeload",t)).deprecatedEvent.isDefaultPrevented()||o.event.isDefaultPrevented()?n.promise():(a.showLoadMsg&&this._showLoading(a.loadMsgDelay),a.prefetch===m&&this._getBase().reset(),p.mobile.allowCrossDomainPages||p.mobile.path.isSameDomain(p.mobile.path.documentUrl,r)?p.ajax({url:s,type:a.type,data:a.data,contentType:a.contentType,dataType:"html",success:this._loadSuccess(r,t,a,n),error:this._loadError(r,t,a,n)}):n.reject(r,a),n.promise())))},_loadError:function(s,n,o,a){return p.proxy(function(e,t,i){this._getBase().set(p.mobile.path.get()),n.xhr=e,n.textStatus=t,n.errorThrown=i;i=this._triggerWithDeprecated("loadfailed",n);i.deprecatedEvent.isDefaultPrevented()||i.event.isDefaultPrevented()||(o.showLoadMsg&&this._showError(),a.reject(s,o))},this)},_getTransitionHandler:function(e){return e=p.mobile._maybeDegradeTransition(e),p.mobile.transitionHandlers[e]||p.mobile.defaultTransitionHandler},_triggerCssTransitionEvents:function(e,t,i){var s=!1;i=i||"",t&&(e[0]===t[0]&&(s=!0),this._triggerWithDeprecated(i+"hide",{nextPage:e,toPage:e,prevPage:t,samePage:s},t)),this._triggerWithDeprecated(i+"show",{prevPage:t||p(""),toPage:e},e)},_cssTransition:function(e,t,i){var s=i.transition,n=i.reverse,o=i.deferred;this._triggerCssTransitionEvents(e,t,"before"),this._hideLoading(),(n=new(this._getTransitionHandler(s))(s,n,e,t).transition()).done(p.proxy(function(){this._triggerCssTransitionEvents(e,t)},this)),n.done(function(){o.resolve.apply(o,arguments)})},_releaseTransitionLock:function(){g=!1,0<f.length&&p.mobile.changePage.apply(null,f.pop())},_removeActiveLinkClass:function(e){p.mobile.removeActiveLinkClass(e)},_loadUrl:function(e,s,t){t.target=e,t.deferred=p.Deferred(),this.load(e,t),t.deferred.done(p.proxy(function(e,t,i){g=!1,t.absUrl=s.absUrl,this.transition(i,s,t)},this)),t.deferred.fail(p.proxy(function(){this._removeActiveLinkClass(!0),this._releaseTransitionLock(),this._triggerWithDeprecated("changefailed",s)},this))},_triggerPageBeforeChange:function(e,t,i){return t.prevPage=this.activePage,p.extend(t,{toPage:e,options:i}),t.absUrl="string"==typeof e?p.mobile.path.makeUrlAbsolute(e,this._findBaseWithDefault()):i.absUrl,!(t=this._triggerWithDeprecated("beforechange",t)).event.isDefaultPrevented()&&!t.deprecatedEvent.isDefaultPrevented()},change:function(e,t){var i,s;g?f.unshift(arguments):(s={},(i=p.extend({},p.mobile.changePage.defaults,t)).fromPage=i.fromPage||this.activePage,this._triggerPageBeforeChange(e,s,i)&&("string"==typeof(e=s.toPage)?(g=!0,this._loadUrl(e,s,i)):this.transition(e,s,i)))},transition:function(o,a,r){var e,t,i,s,n,l,h,d,c,u;if(g)f.unshift([o,r]);else if(this._triggerPageBeforeChange(o,a,r)&&(a.prevPage=r.fromPage,!(u=this._triggerWithDeprecated("beforetransition",a)).deprecatedEvent.isDefaultPrevented()&&!u.event.isDefaultPrevented())){if(g=!0,o[0]!==p.mobile.firstPage[0]||r.dataUrl||(r.dataUrl=p.mobile.path.documentUrl.hrefNoHash),e=r.fromPage,d=t=r.dataUrl&&p.mobile.path.convertUrlToDataUrl(r.dataUrl)||o.jqmData("url"),p.mobile.path.getFilePath(t),i=p.mobile.navigate.history.getActive(),s=0===p.mobile.navigate.history.activeIndex,c=0,n=E.title,l=("dialog"===r.role||"dialog"===o.jqmData("role"))&&!0!==o.jqmData("dialog"),e&&e[0]===o[0]&&!r.allowSamePageTransition)return g=!1,this._triggerWithDeprecated("transition",a),this._triggerWithDeprecated("change",a),void(r.fromHashChange&&p.mobile.navigate.history.direct({url:t}));o.page({role:r.role}),r.fromHashChange&&(c="back"===r.direction?-1:1);try{(E.activeElement&&"body"!==E.activeElement.nodeName.toLowerCase()?p(E.activeElement):p("input:focus, textarea:focus, select:focus")).trigger("blur")}catch(e){}h=!1,l&&i&&(i.url&&-1<i.url.indexOf(p.mobile.dialogHashKey)&&this.activePage&&!this.activePage.hasClass("ui-dialog")&&0<p.mobile.navigate.history.activeIndex&&(h=!(r.changeHash=!1)),t=i.url||"",!h&&-1<t.indexOf("#")?t+=p.mobile.dialogHashKey:t+="#"+p.mobile.dialogHashKey),(u=i?o.jqmData("title")||o.children(":jqmData(role='header')").find(".ui-title").text():n)&&n===E.title&&(n=u),o.jqmData("title")||o.jqmData("title",n),r.transition=r.transition||(c&&!s?i.transition:m)||(l?p.mobile.defaultDialogTransition:p.mobile.defaultPageTransition),!c&&h&&(p.mobile.navigate.history.getActive().pageUrl=d),t&&!r.fromHashChange&&(!p.mobile.path.isPath(t)&&t.indexOf("#")<0&&(t="#"+t),!(d={transition:r.transition,title:n,pageUrl:d,role:r.role})!==r.changeHash&&p.mobile.hashListeningEnabled?p.mobile.navigate(this.window[0].encodeURI(t),d,!0):o[0]!==p.mobile.firstPage[0]&&p.mobile.navigate.history.add(t,d)),E.title=n,p.mobile.activePage=o,this.activePage=o,r.reverse=r.reverse||c<0,c=p.Deferred(),this._cssTransition(o,e,{transition:r.transition,reverse:r.reverse,deferred:c}),c.done(p.proxy(function(e,t,i,s,n){p.mobile.removeActiveLinkClass(),r.duplicateCachedPage&&r.duplicateCachedPage.remove(),n||p.mobile.focusPage(o),this._releaseTransitionLock(),this._triggerWithDeprecated("transition",a),this._triggerWithDeprecated("change",a)},this))}},_findBaseWithDefault:function(){return this.activePage&&p.mobile.getClosestBaseUrl(this.activePage)||p.mobile.path.documentBase.hrefNoHash}}),p.mobile.navreadyDeferred=p.Deferred();var f=[],g=!1}(e),Ue=(je=e).Deferred(),Re=je.Deferred(),We=je.mobile.path.documentUrl,ze=null,je.mobile.loadPage=function(e,t){var i=(t=t||{}).pageContainer||je.mobile.pageContainer;return t.deferred=je.Deferred(),i.pagecontainer("load",e,t),t.deferred.promise()},je.mobile.back=function(){var e=D.navigator;this.phonegapNavigationEnabled&&e&&e.app&&e.app.backHistory?e.app.backHistory():je.mobile.pageContainer.pagecontainer("back")},je.mobile.focusPage=function(e){var t=e.find("[autofocus]"),i=e.find(".ui-title:eq(0)");(t.length?t:i.length?i:e).trigger("focus")},je.mobile._maybeDegradeTransition=je.mobile._maybeDegradeTransition||function(e){return e},je.mobile.changePage=function(e,t){je.mobile.pageContainer.pagecontainer("change",e,t)},je.mobile.changePage.defaults={transition:Me,reverse:!1,changeHash:!0,fromHashChange:!1,role:Me,duplicateCachedPage:Me,pageContainer:Me,showLoadMsg:!0,dataUrl:Me,fromPage:Me,allowSamePageTransition:!1},je.mobile._registerInternalEvents=function(){function n(e,t){var i,s,n,o=!0;return!(!je.mobile.ajaxEnabled||e.is(":jqmData(ajax='false')")||!e.jqmHijackable().length||e.attr("target"))&&(i=ze&&ze.attr("formaction")||e.attr("action"),n=(e.attr("method")||"get").toLowerCase(),i||(i=je.mobile.getClosestBaseUrl(e),(i="get"===n?je.mobile.path.parseUrl(i).hrefNoSearch:i)===je.mobile.path.documentBase.hrefNoHash&&(i=We.hrefNoSearch)),i=je.mobile.path.makeUrlAbsolute(i,je.mobile.getClosestBaseUrl(e)),!(je.mobile.path.isExternal(i)&&!je.mobile.path.isPermittedCrossDomainRequest(We,i))&&(t||(t=e.serializeArray(),ze&&ze[0].form===e[0]&&(s=ze.attr("name"))&&(je.each(t,function(e,t){if(t.name===s)return s="",!1}),s&&t.push({name:s,value:ze.attr("value")})),o={url:i,options:{type:n,data:je.param(t),transition:e.jqmData("transition"),reverse:"reverse"===e.jqmData("direction"),reloadPage:!0}}),o))}je.mobile.document.on("form","submit",function(e){var t;e.isDefaultPrevented()||(t=n(je(this)))&&(je.mobile.changePage(t.url,t.options),e.preventDefault())}),je.mobile.document.on("vclick",function(e){var t,i=e.target,s=!1;if(!(1<e.which)&&je.mobile.linkBindingEnabled){if(ze=je(i),je.data(i,"mobile-button")){if(!n(je(i).closest("form"),!0))return;i.parentNode&&(i=i.parentNode)}else{if(!(i=Oi(i))||"#"===je.mobile.path.parseUrl(i.getAttribute("href")||"#").hash)return;if(!je(i).jqmHijackable().length)return}~i.className.indexOf("ui-link-inherit")?i.parentNode&&(t=je.data(i.parentNode,"buttonElements")):t=je.data(i,"buttonElements"),t?i=t.outer:s=!0,i=je(i),0<(i=s?i.closest(".ui-btn"):i).length&&!i.hasClass("ui-state-disabled")&&(je.mobile.removeActiveLinkClass(!0),je.mobile.activeClickedLink=i,je.mobile.activeClickedLink.addClass(je.mobile.activeBtnClass))}}),je.mobile.document.on("click",function(e){if(je.mobile.linkBindingEnabled&&!e.isDefaultPrevented()){function t(){D.setTimeout(function(){je.mobile.removeActiveLinkClass(!0)},200)}var i,s,n,o=Oi(e.target),a=je(o);if(je.mobile.activeClickedLink&&je.mobile.activeClickedLink[0]===e.target.parentNode&&t(),o&&!(1<e.which)&&a.jqmHijackable().length){if(a.is(":jqmData(rel='back')"))return je.mobile.back(),!1;if(n=je.mobile.getClosestBaseUrl(a),i=je.mobile.path.makeUrlAbsolute(a.attr("href")||"#",n),je.mobile.ajaxEnabled||je.mobile.path.isEmbeddedPage(i)){if(!(-1===i.search("#")||je.mobile.path.isExternal(i)&&je.mobile.path.isAbsoluteUrl(i))){if(!(i=i.replace(/[^#]*#/,"")))return void e.preventDefault();i=je.mobile.path.isPath(i)?je.mobile.path.makeUrlAbsolute(i,n):je.mobile.path.makeUrlAbsolute("#"+i,We.hrefNoHash)}a.is("[rel='external']")||a.is(":jqmData(ajax='false')")||a.is("[target]")||je.mobile.path.isExternal(i)&&!je.mobile.path.isPermittedCrossDomainRequest(We,i)?t():(s=a.jqmData("transition"),o="reverse"===a.jqmData("direction")||a.jqmData("back"),n=a.attr("data-"+je.mobile.ns+"rel")||Me,je.mobile.changePage(i,{transition:s,reverse:o,role:n,link:a}),e.preventDefault())}else t()}}}),je.mobile.document.on(".ui-page","pageshow.prefetch",function(){var i=[];je(this).find("a:jqmData(prefetch)").each(function(){var e=je(this),t=e.attr("href");t&&-1===je.inArray(t,i)&&(i.push(t),je.mobile.loadPage(t,{role:e.attr("data-"+je.mobile.ns+"rel"),prefetch:!0}))})}),je.mobile.pageContainer.pagecontainer(),je.mobile.document.on("pageshow",function(){Re?Re.done(je.mobile.resetActivePageHeight):je.mobile.resetActivePageHeight()}),je.mobile.window.on("throttledresize",je.mobile.resetActivePageHeight)},je(function(){Ue.resolve()}),"complete"===E.readyState?Bi():je.mobile.window.on("load",Bi),je.when(Ue,je.mobile.navreadyDeferred).done(function(){je.mobile._registerInternalEvents()}),Ve=this,(Ke=e).mobile.Transition=function(){this.init.apply(this,arguments)},Ke.extend(Ke.mobile.Transition.prototype,{toPreClass:" ui-page-pre-in",init:function(e,t,i,s){Ke.extend(this,{name:e,reverse:t,$to:i,$from:s,deferred:new Ke.Deferred})},cleanFrom:function(){this.$from.removeClass(Ke.mobile.activePageClass+" out in reverse "+this.name).height("")},beforeDoneIn:function(){},beforeDoneOut:function(){},beforeStartOut:function(){},doneIn:function(){this.beforeDoneIn(),this.$to.removeClass("out in reverse "+this.name).height(""),this.toggleViewportClass(),Ke.mobile.window.scrollTop()!==this.toScroll&&this.scrollPage(),this.sequential||this.$to.addClass(Ke.mobile.activePageClass),this.deferred.resolve(this.name,this.reverse,this.$to,this.$from,!0)},doneOut:function(e,t,i,s){this.beforeDoneOut(),this.startIn(e,t,i,s)},hideIn:function(e){this.$to.css("z-index",-10),e.call(this),this.$to.css("z-index","")},scrollPage:function(){Ke.event.special.scrollstart.enabled=!1,!Ke.mobile.hideUrlBar&&this.toScroll===Ke.mobile.defaultHomeScroll||Ve.scrollTo(0,this.toScroll),setTimeout(function(){Ke.event.special.scrollstart.enabled=!0},150)},startIn:function(e,t,i,s){this.hideIn(function(){this.$to.addClass(Ke.mobile.activePageClass+this.toPreClass),s||Ke.mobile.focusPage(this.$to),this.$to.height(e+this.toScroll),i||this.scrollPage()}),this.$to.removeClass(this.toPreClass).addClass(this.name+" in "+t),i?this.doneIn():this.$to.animationComplete(Ke.proxy(function(){this.doneIn()},this))},startOut:function(e,t,i){this.beforeStartOut(e,t,i),this.$from.height(e+Ke.mobile.window.scrollTop()).addClass(this.name+" out"+t)},toggleViewportClass:function(){Ke.mobile.pageContainer.toggleClass("ui-mobile-viewport-transitioning viewport-"+this.name)},transition:function(){var e=this.reverse?" reverse":"",t=Ke.mobile.getScreenHeight(),i=!1!==Ke.mobile.maxTransitionWidth&&Ke.mobile.window.width()>Ke.mobile.maxTransitionWidth;return this.toScroll=Ke.mobile.navigate.history.getActive().lastScroll||Ke.mobile.defaultHomeScroll,i=!Ke.support.cssTransitions||!Ke.support.cssAnimations||i||!this.name||"none"===this.name||Math.max(Ke.mobile.window.scrollTop(),this.toScroll)>Ke.mobile.getMaxScrollForTransition(),this.toggleViewportClass(),this.$from&&!i?this.startOut(t,e,i):this.doneOut(t,e,i,!0),this.deferred.promise()}}),($e=e).mobile.SerialTransition=function(){this.init.apply(this,arguments)},$e.extend($e.mobile.SerialTransition.prototype,$e.mobile.Transition.prototype,{sequential:!0,beforeDoneOut:function(){this.$from&&this.cleanFrom()},beforeStartOut:function(e,t,i){this.$from.animationComplete($e.proxy(function(){this.doneOut(e,t,i)},this))}}),(Xe=e).mobile.ConcurrentTransition=function(){this.init.apply(this,arguments)},Xe.extend(Xe.mobile.ConcurrentTransition.prototype,Xe.mobile.Transition.prototype,{sequential:!1,beforeDoneIn:function(){this.$from&&this.cleanFrom()},beforeStartOut:function(e,t,i){this.doneOut(e,t,i)}}),(Ge=e).mobile.transitionHandlers={sequential:Ge.mobile.SerialTransition,simultaneous:Ge.mobile.ConcurrentTransition},Ge.mobile.defaultTransitionHandler=Ge.mobile.transitionHandlers.sequential,Ge.mobile.transitionFallbacks={},Ge.mobile._maybeDegradeTransition=function(e){return e=e&&!Ge.support.cssTransform3d&&Ge.mobile.transitionFallbacks[e]?Ge.mobile.transitionFallbacks[e]:e},Ge.mobile.getMaxScrollForTransition=Ge.mobile.getMaxScrollForTransition||function(){return 3*Ge.mobile.getScreenHeight()},e.mobile.transitionFallbacks.flip="fade",e.mobile.transitionFallbacks.flow="fade",e.mobile.transitionFallbacks.pop="fade",(Xe=e).mobile.transitionHandlers.slide=Xe.mobile.transitionHandlers.simultaneous,Xe.mobile.transitionFallbacks.slide="fade",e.mobile.transitionFallbacks.slidedown="fade",e.mobile.transitionFallbacks.slidefade="fade",e.mobile.transitionFallbacks.slideup="fade",e.mobile.transitionFallbacks.turn="fade",(Ze=e).mobile.degradeInputs={color:!1,date:!1,datetime:!1,"datetime-local":!1,email:!1,month:!1,number:!1,range:"number",search:"text",tel:!1,time:!1,url:!1,week:!1},Ze.mobile.page.prototype.options.degradeInputs=Ze.mobile.degradeInputs,Ze.mobile.degradeInputsWithin=function(e){(e=Ze(e)).find("input").not(Ze.mobile.page.prototype.keepNativeSelector()).each(function(){var e,t,i=Ze(this),s=this.getAttribute("type"),n=Ze.mobile.degradeInputs[s]||"text";Ze.mobile.degradeInputs[s]&&(t=-1<(e=Ze("<div>").html(i.clone()).html()).indexOf(" type="),s=' type="'+n+'" data-'+Ze.mobile.ns+'type="'+s+'"'+(t?"":">"),i.replaceWith(e.replace(t?/\s+type=["']?\w+['"]?/:/\/?>/,s)))})},(Ye=e).widget("mobile.page",Ye.mobile.page,{options:{closeBtn:"left",closeBtnText:"Close",overlayTheme:"a",corners:!0,dialog:!1},_create:function(){this._super(),this.options.dialog&&(Ye.extend(this,{_inner:this.element.children(),_headerCloseButton:null}),this.options.enhanced||this._setCloseBtn(this.options.closeBtn))},_enhance:function(){this._super(),this.options.dialog&&this.element.addClass("ui-dialog").wrapInner(Ye("<div/>",{role:"dialog",class:"ui-dialog-contain ui-overlay-shadow"+(this.options.corners?" ui-corner-all":"")}))},_setOptions:function(e){var t,i,s=this.options;e.corners!==Qe&&this._inner.toggleClass("ui-corner-all",!!e.corners),e.overlayTheme!==Qe&&Ye.mobile.activePage[0]===this.element[0]&&(s.overlayTheme=e.overlayTheme,this._handlePageBeforeShow()),e.closeBtnText!==Qe&&(t=s.closeBtn,i=e.closeBtnText),(t=e.closeBtn!==Qe?e.closeBtn:t)&&this._setCloseBtn(t,i),this._super(e)},_handlePageBeforeShow:function(){this.options.overlayTheme&&this.options.dialog?(this.removeContainerBackground(),this.setContainerBackground(this.options.overlayTheme)):this._super()},_setCloseBtn:function(e,t){var i,s=this._headerCloseButton;"none"===(e="left"===e?"left":"right"===e?"right":"none")?s&&(s.remove(),s=null):s?(s.removeClass("ui-btn-left ui-btn-right").addClass("ui-btn-"+e),t&&s.text(t)):(i=this._inner.find(":jqmData(role='header')").first(),s=Ye("<a></a>",{href:"#",class:"ui-btn ui-corner-all ui-icon-delete ui-btn-icon-notext ui-btn-"+e}).attr("data-"+Ye.mobile.ns+"rel","back").text(t||this.options.closeBtnText||"").prependTo(i)),this._headerCloseButton=s}}),(Je=e).widget("mobile.dialog",{options:{closeBtn:"left",closeBtnText:"Close",overlayTheme:"a",corners:!0},_handlePageBeforeShow:function(){this._isCloseable=!0,this.options.overlayTheme&&this.element.page("removeContainerBackground").page("setContainerBackground",this.options.overlayTheme)},_handlePageBeforeHide:function(){this._isCloseable=!1},_handleVClickSubmit:function(e){var t=Je(e.target).closest("vclick"===e.type?"a":"form");t.length&&!t.jqmData("transition")&&((e={})["data-"+Je.mobile.ns+"transition"]=(Je.mobile.navigate.history.getActive()||{}).transition||Je.mobile.defaultDialogTransition,e["data-"+Je.mobile.ns+"direction"]="reverse",t.attr(e))},_create:function(){var e=this.element,t=this.options;e.addClass("ui-dialog").wrapInner(Je("<div/>",{role:"dialog",class:"ui-dialog-contain ui-overlay-shadow"+(t.corners?" ui-corner-all":"")})),Je.extend(this,{_isCloseable:!1,_inner:e.children(),_headerCloseButton:null}),this._on(e,{vclick:"_handleVClickSubmit",submit:"_handleVClickSubmit",pagebeforeshow:"_handlePageBeforeShow",pagebeforehide:"_handlePageBeforeHide"}),this._setCloseBtn(t.closeBtn)},_setOptions:function(e){var t,i,s=this.options;e.corners!==et&&this._inner.toggleClass("ui-corner-all",!!e.corners),e.overlayTheme!==et&&Je.mobile.activePage[0]===this.element[0]&&(s.overlayTheme=e.overlayTheme,this._handlePageBeforeShow()),e.closeBtnText!==et&&(t=s.closeBtn,i=e.closeBtnText),(t=e.closeBtn!==et?e.closeBtn:t)&&this._setCloseBtn(t,i),this._super(e)},_setCloseBtn:function(e,t){var i,s=this._headerCloseButton;"none"===(e="left"===e?"left":"right"===e?"right":"none")?s&&(s.remove(),s=null):s?(s.removeClass("ui-btn-left ui-btn-right").addClass("ui-btn-"+e),t&&s.text(t)):(i=this._inner.find(":jqmData(role='header')").first(),s=Je("<a></a>",{role:"button",href:"#",class:"ui-btn ui-corner-all ui-icon-delete ui-btn-icon-notext ui-btn-"+e}).text(t||this.options.closeBtnText||"").prependTo(i),this._on(s,{click:"close"})),this._headerCloseButton=s},close:function(){var e=Je.mobile.navigate.history;this._isCloseable&&(this._isCloseable=!1,Je.mobile.hashListeningEnabled&&0<e.activeIndex?Je.mobile.back():Je.mobile.pageContainer.pagecontainer("back"))}}),st=/([A-Z])/g,(tt=e).widget("mobile.collapsible",{options:{enhanced:!1,expandCueText:null,collapseCueText:null,collapsed:!0,heading:"h1,h2,h3,h4,h5,h6,legend",collapsedIcon:null,expandedIcon:null,iconpos:null,theme:null,contentTheme:null,inset:null,corners:null,mini:null},_create:function(){var e=this.element,t={accordion:e.closest(":jqmData(role='collapsible-set'),:jqmData(role='collapsibleset')"+(tt.mobile.collapsibleset?", :mobile-collapsibleset":"")).addClass("ui-collapsible-set")};this._ui=t,this._renderedOptions=this._getOptions(this.options),this.options.enhanced?(t.heading=this.element.children(".ui-collapsible-heading"),t.content=t.heading.next(),t.anchor=t.heading.children(),t.status=t.anchor.children(".ui-collapsible-heading-status")):this._enhance(e,t),this._on(t.heading,{tap:function(){t.heading.find("a").first().addClass(tt.mobile.activeBtnClass)},click:function(e){this._handleExpandCollapse(!t.heading.hasClass("ui-collapsible-heading-collapsed")),e.preventDefault(),e.stopPropagation()}})},_getOptions:function(e){var t,i=this._ui.accordion,s=this._ui.accordionWidget;for(t in e=tt.extend({},e),i.length&&!s&&(this._ui.accordionWidget=s=i.data("mobile-collapsibleset")),e)e[t]=null!=e[t]?e[t]:s?s.options[t]:i.length?tt.mobile.getAttribute(i[0],t.replace(st,"-$1").toLowerCase()):null,null==e[t]&&(e[t]=tt.mobile.collapsible.defaults[t]);return e},_themeClassFromOption:function(e,t){return!t||"none"===t?"":e+t},_enhance:function(e,t){var i=this._renderedOptions,s=this._themeClassFromOption("ui-body-",i.contentTheme);return e.addClass("ui-collapsible "+(i.inset?"ui-collapsible-inset ":"")+(i.inset&&i.corners?"ui-corner-all ":"")+(s?"ui-collapsible-themed-content ":"")),t.originalHeading=e.children(this.options.heading).first(),t.content=e.wrapInner("<div class='ui-collapsible-content "+s+"'></div>").children(".ui-collapsible-content"),t.heading=t.originalHeading,t.heading.is("legend")&&(t.heading=tt("<div role='heading'>"+t.heading.html()+"</div>"),t.placeholder=tt("<div>\x3c!-- placeholder for legend --\x3e</div>").insertBefore(t.originalHeading),t.originalHeading.remove()),s=i.collapsed?i.collapsedIcon?"ui-icon-"+i.collapsedIcon:"":i.expandedIcon?"ui-icon-"+i.expandedIcon:"",t.status=tt("<span class='ui-collapsible-heading-status'></span>"),t.anchor=t.heading.detach().addClass("ui-collapsible-heading").append(t.status).wrapInner("<a href='#' class='ui-collapsible-heading-toggle'></a>").find("a").first().addClass("ui-btn "+(s?s+" ":"")+(s?Fi(i.iconpos)+" ":"")+this._themeClassFromOption("ui-btn-",i.theme)+" "+(i.mini?"ui-mini ":"")),t.heading.insertBefore(t.content),this._handleExpandCollapse(this.options.collapsed),t},refresh:function(){this._applyOptions(this.options),this._renderedOptions=this._getOptions(this.options)},_applyOptions:function(e){var t,i,s,n=this.element,o=this._renderedOptions,a=this._ui,r=a.anchor,l=a.status,h=this._getOptions(e);e.collapsed!==it&&this._handleExpandCollapse(e.collapsed),(e=n.hasClass("ui-collapsible-collapsed"))?h.expandCueText!==it&&l.text(h.expandCueText):h.collapseCueText!==it&&l.text(h.collapseCueText),l=h.collapsedIcon!==it?!1!==h.collapsedIcon:!1!==o.collapsedIcon,h.iconpos===it&&h.collapsedIcon===it&&h.expandedIcon===it||(r.removeClass([Fi(o.iconpos)].concat(o.expandedIcon?["ui-icon-"+o.expandedIcon]:[]).concat(o.collapsedIcon?["ui-icon-"+o.collapsedIcon]:[]).join(" ")),l&&r.addClass([Fi((h.iconpos!==it?h:o).iconpos)].concat(e?["ui-icon-"+(h.collapsedIcon!==it?h:o).collapsedIcon]:["ui-icon-"+(h.expandedIcon!==it?h:o).expandedIcon]).join(" "))),h.theme!==it&&(i=this._themeClassFromOption("ui-btn-",o.theme),t=this._themeClassFromOption("ui-btn-",h.theme),r.removeClass(i).addClass(t)),h.contentTheme!==it&&(i=this._themeClassFromOption("ui-body-",o.contentTheme),t=this._themeClassFromOption("ui-body-",h.contentTheme),a.content.removeClass(i).addClass(t)),h.inset!==it&&(n.toggleClass("ui-collapsible-inset",h.inset),s=!(!h.inset||!h.corners&&!o.corners)),(s=h.corners!==it?!(!h.corners||!h.inset&&!o.inset):s)!==it&&n.toggleClass("ui-corner-all",s),h.mini!==it&&r.toggleClass("ui-mini",h.mini)},_setOptions:function(e){this._applyOptions(e),this._super(e),this._renderedOptions=this._getOptions(this.options)},_handleExpandCollapse:function(e){var t=this._renderedOptions,i=this._ui;i.status.text(e?t.expandCueText:t.collapseCueText),i.heading.toggleClass("ui-collapsible-heading-collapsed",e).find("a").first().toggleClass("ui-icon-"+t.expandedIcon,!e).toggleClass("ui-icon-"+t.collapsedIcon,e||t.expandedIcon===t.collapsedIcon).removeClass(tt.mobile.activeBtnClass),this.element.toggleClass("ui-collapsible-collapsed",e),i.content.toggleClass("ui-collapsible-content-collapsed",e).attr("aria-hidden",e).trigger("updatelayout"),this.options.collapsed=e,this._trigger(e?"collapse":"expand")},expand:function(){this._handleExpandCollapse(!1)},collapse:function(){this._handleExpandCollapse(!0)},_destroy:function(){var e=this._ui;this.options.enhanced||(e.placeholder?(e.originalHeading.insertBefore(e.placeholder),e.placeholder.remove(),e.heading.remove()):(e.status.remove(),e.heading.removeClass("ui-collapsible-heading ui-collapsible-heading-collapsed").children().contents().unwrap()),e.anchor.contents().unwrap(),e.content.contents().unwrap(),this.element.removeClass("ui-collapsible ui-collapsible-collapsed ui-collapsible-themed-content ui-collapsible-inset ui-corner-all"))}}),tt.mobile.collapsible.defaults={expandCueText:" click to expand contents",collapseCueText:" click to collapse contents",collapsedIcon:"plus",contentTheme:"inherit",expandedIcon:"minus",iconpos:"left",inset:!0,corners:!0,theme:"inherit",mini:!1},ot=/\bui-screen-hidden\b/,(nt=e).mobile.behaviors.addFirstLastClasses={_getVisibles:function(e,t){var i;return i=t||0===(i=e.filter(":visible")).length?Ni(e):i},_addFirstLastClasses:function(e,t,i){e.removeClass("ui-first-child ui-last-child"),t.eq(0).addClass("ui-first-child").end().last().addClass("ui-last-child"),i||this.element.trigger("updatelayout")},_removeFirstLastClasses:function(e){e.removeClass("ui-first-child ui-last-child")}},lt=":mobile-collapsible, "+(at=e).mobile.collapsible.initSelector,at.widget("mobile.collapsibleset",at.extend({initSelector:":jqmData(role='collapsible-set'),:jqmData(role='collapsibleset')",options:at.extend({enhanced:!1},at.mobile.collapsible.defaults),_handleCollapsibleExpand:function(e){e=at(e.target).closest(".ui-collapsible");e.parent().is(":mobile-collapsibleset, :jqmData(role='collapsible-set')")&&e.siblings(".ui-collapsible:not(.ui-collapsible-collapsed)").collapsible("collapse")},_create:function(){var e=this.element,t=this.options;at.extend(this,{_classes:""}),t.enhanced||(e.addClass("ui-collapsible-set "+this._themeClassFromOption("ui-group-theme-",t.theme)+" "+(t.corners&&t.inset?"ui-corner-all ":"")),this.element.find(at.mobile.collapsible.initSelector).collapsible()),this._on(e,{collapsibleexpand:"_handleCollapsibleExpand"})},_themeClassFromOption:function(e,t){return!t||"none"===t?"":e+t},_init:function(){this._refresh(!0),this.element.children(lt).filter(":jqmData(collapsed='false')").collapsible("expand")},_setOptions:function(e){var t,i=this.element,s=this._themeClassFromOption("ui-group-theme-",e.theme);return s&&i.removeClass(this._themeClassFromOption("ui-group-theme-",this.options.theme)).addClass(s),e.inset!==rt&&(t=!(!e.inset||!e.corners&&!this.options.corners)),(t=e.corners!==rt?!(!e.corners||!e.inset&&!this.options.inset):t)!==rt&&i.toggleClass("ui-corner-all",t),e=this._super(e),this.element.children(":mobile-collapsible").collapsible("refresh"),e},_destroy:function(){var e=this.element;this._removeFirstLastClasses(e.children(lt)),e.removeClass("ui-collapsible-set ui-corner-all "+this._themeClassFromOption("ui-group-theme-",this.options.theme)).children(":mobile-collapsible").collapsible("destroy")},_refresh:function(e){var t=this.element.children(lt);this.element.find(at.mobile.collapsible.initSelector).not(".ui-collapsible").collapsible(),this._addFirstLastClasses(t,this._getVisibles(t,e),e)},refresh:function(){this._refresh(!1)}},at.mobile.behaviors.addFirstLastClasses)),e.fn.fieldcontain=function(){return this.addClass("ui-field-contain")},(ht=e).fn.grid=function(a){return this.each(function(){var e,t=ht(this),i=ht.extend({grid:null},a),s=t.children(),n={solo:1,a:2,b:3,c:4,d:5},o=i.grid;if(!o)if(s.length<=5)for(e in n)n[e]===s.length&&(o=e);else o="a",t.addClass("ui-grid-duo");i=n[o],t.addClass("ui-grid-"+o),s.filter(":nth-child("+i+"n+1)").addClass("ui-block-a"),1<i&&s.filter(":nth-child("+i+"n+2)").addClass("ui-block-b"),2<i&&s.filter(":nth-child("+i+"n+3)").addClass("ui-block-c"),3<i&&s.filter(":nth-child("+i+"n+4)").addClass("ui-block-d"),4<i&&s.filter(":nth-child("+i+"n+5)").addClass("ui-block-e")})},(dt=e).widget("mobile.navbar",{options:{iconpos:"top",grid:null},_create:function(){var e=this.element,t=e.find("a, button"),s=t.filter(":jqmData(icon)").length?this.options.iconpos:void 0;e.addClass("ui-navbar").attr("role","navigation").find("ul").jqmEnhanceable().grid({grid:this.options.grid}),t.each(function(){var e=dt.mobile.getAttribute(this,"icon"),t=dt.mobile.getAttribute(this,"theme"),i="ui-btn";t&&(i+=" ui-btn-"+t),e&&(i+=" ui-icon-"+e+" ui-btn-icon-"+s),dt(this).addClass(i)}),e.on("a","vclick",function(){var e=dt(this);e.hasClass("ui-state-disabled")||e.hasClass("ui-disabled")||e.hasClass(dt.mobile.activeBtnClass)||(t.removeClass(dt.mobile.activeBtnClass),e.addClass(dt.mobile.activeBtnClass),dt(E).one("pagehide",function(){e.removeClass(dt.mobile.activeBtnClass)}))}),e.closest(".ui-page").on("pagebeforeshow",function(){t.filter(".ui-state-persist").addClass(dt.mobile.activeBtnClass)})}}),ut=(ct=e).mobile.getAttribute,ct.widget("mobile.listview",ct.extend({options:{theme:null,countTheme:null,dividerTheme:null,icon:"carat-r",splitIcon:"carat-r",splitTheme:null,corners:!0,shadow:!0,inset:!1},_create:function(){var e=this,t="";t+=e.options.inset?" ui-listview-inset":"",e.options.inset&&(t+=e.options.corners?" ui-corner-all":"",t+=e.options.shadow?" ui-shadow":""),e.element.addClass(" ui-listview"+t),e.refresh(!0)},_findFirstElementByTagName:function(e,t,i,s){var n={};for(n[i]=n[s]=!0;e;){if(n[e.nodeName])return e;e=e[t]}return null},_addThumbClasses:function(e){for(var t,i=e.length,s=0;s<i;s++)(t=ct(this._findFirstElementByTagName(e[s].firstChild,"nextSibling","img","IMG"))).length&&ct(this._findFirstElementByTagName(t[0].parentNode,"parentNode","li","LI")).addClass(t.hasClass("ui-li-icon")?"ui-li-has-icon":"ui-li-has-thumb")},_getChildrenByTagName:function(e,t,i){var s=[],n={};for(n[t]=n[i]=!0,e=e.firstChild;e;)n[e.nodeName]&&s.push(e),e=e.nextSibling;return ct(s)},_beforeListviewRefresh:ct.noop,_afterListviewRefresh:ct.noop,refresh:function(e){var t,i,s,n,o,a,r,l,h,d,c,u,p,m=this.options,f=this.element,g="ol"===!!f[0].nodeName.toLowerCase(),b=f.attr("start"),v={},_=f.find(".ui-li-count"),C=ut(f[0],"counttheme")||this.options.countTheme,C=C?"ui-body-"+C:"ui-body-inherit";for(m.theme&&f.addClass("ui-group-theme-"+m.theme),g&&(b||0===b)&&(b=parseInt(b,10)-1,f.css("counter-reset","listnumbering "+b)),this._beforeListviewRefresh(),s=(p=this._getChildrenByTagName(f[i=0],"li","LI")).length;i<s;i++)n=p.eq(i),o="",(e||n[0].className.search(/\bui-li-static\b|\bui-li-divider\b/)<0)&&(l=this._getChildrenByTagName(n[0],"a","A"),h="list-divider"===ut(n[0],"role"),d=n.attr("value"),a=ut(n[0],"theme"),l.length&&l[0].className.search(/\bui-btn\b/)<0&&!h?(r=!1!==(u=ut(n[0],"icon"))&&(u||m.icon),l.removeClass("ui-link"),t="ui-btn",a&&(t+=" ui-btn-"+a),1<l.length?(o="ui-li-has-alt",c=l.last(),u=(u=ut(c[0],"theme")||m.splitTheme||ut(n[0],"theme",!0))?" ui-btn-"+u:"",u="ui-btn ui-btn-icon-notext ui-icon-"+(ut(c[0],"icon")||ut(n[0],"icon")||m.splitIcon)+u,c.attr("title",String.prototype.trim(c.getEncodedText())).addClass(u).empty(),l=l.first()):r&&(t+=" ui-btn-icon-right ui-icon-"+r),l.addClass(t)):h?(o="ui-li-divider ui-bar-"+(ut(n[0],"theme")||m.dividerTheme||m.theme||"inherit"),n.attr("role","heading")):l.length<=0&&(o="ui-li-static ui-body-"+(a||"inherit")),g&&d&&(d=parseInt(d,10)-1,n.css("counter-reset","listnumbering "+d))),v[o]||(v[o]=[]),v[o].push(n[0]);for(o in v)ct(v[o]).addClass(o);_.each(function(){ct(this).closest("li").addClass("ui-li-has-count")}),C&&_.not("[class*='ui-body-']").addClass(C),this._addThumbClasses(p),this._addThumbClasses(p.find(".ui-btn")),this._afterListviewRefresh(),this._addFirstLastClasses(p,this._getVisibles(p,e),e)}},ct.mobile.behaviors.addFirstLastClasses)),(pt=e).widget("mobile.listview",pt.mobile.listview,{options:{autodividers:!1,autodividersSelector:function(e){return(e=String.prototype.trim(e.text())||null)?e=e.slice(0,1).toUpperCase():null}},_beforeListviewRefresh:function(){this.options.autodividers&&(this._replaceDividers(),this._superApply(arguments))},_replaceDividers:function(){var e,t,i,s,n,o=null,a=this.element;for(a.children("li:jqmData(role='list-divider')").remove(),t=a.children("li"),e=0;e<t.length;e++)i=t[e],(s=this.options.autodividersSelector(pt(i)))&&o!==s&&((n=E.createElement("li")).appendChild(E.createTextNode(s)),n.setAttribute("data-"+pt.mobile.ns+"role","list-divider"),i.parentNode.insertBefore(n,i)),o=s}}),mt=/(^|\s)ui-li-divider($|\s)/,ft=/(^|\s)ui-screen-hidden($|\s)/,e.widget("mobile.listview",e.mobile.listview,{options:{hideDividers:!1},_afterListviewRefresh:function(){var e,t,i,s=!0;if(this._superApply(arguments),this.options.hideDividers)for(t=(e=this._getChildrenByTagName(this.element[0],"li","LI")).length-1;-1<t;t--)(i=e[t]).className.match(mt)?(s&&(i.className=i.className+" ui-screen-hidden"),s=!0):i.className.match(ft)||(s=!1)}}),(gt=e).mobile.nojs=function(e){gt(":jqmData(role='nojs')",e).addClass("ui-nojs")},e.mobile.behaviors.formReset={_handleFormReset:function(){this._on(this.element.closest("form"),{reset:function(){this._delay("_reset")}})}},_t=(bt=e).mobile.path.hashToSelector,bt.widget("mobile.checkboxradio",bt.extend({initSelector:"input:not( :jqmData(role='flipswitch' ) )[type='checkbox'],input[type='radio']:not( :jqmData(role='flipswitch' ))",options:{theme:"inherit",mini:!1,wrapperClass:null,enhanced:!1,iconpos:"left"},_create:function(){function e(e,t){return e.jqmData(t)||e.closest("form, fieldset").jqmData(t)}var t=this.element,i=this.options,s=this.options.enhanced?{element:this.element.siblings("label"),isParent:!1}:this._findLabel(),n=t[0].type,o="ui-"+n+"-on",a="ui-"+n+"-off";"checkbox"!==n&&"radio"!==n||(this.element[0].disabled&&(this.options.disabled=!0),i.iconpos=e(t,"iconpos")||s.element.attr("data-"+bt.mobile.ns+"iconpos")||i.iconpos,i.mini=e(t,"mini")||i.mini,bt.extend(this,{input:t,label:s.element,labelIsParent:s.isParent,inputtype:n,checkedClass:o,uncheckedClass:a}),this.options.enhanced||this._enhance(),this._on(s.element,{vmouseover:"_handleLabelVMouseOver",vclick:"_handleLabelVClick"}),this._on(t,{vmousedown:"_cacheVals",vclick:"_handleInputVClick",focus:"_handleInputFocus",blur:"_handleInputBlur"}),this._handleFormReset(),this.refresh())},_findLabel:function(){var e,t,i=this.element,s=i[0].labels;return s&&0<s.length?(e=bt(s[0]),t=bt.contains(e[0],i[0])):e=(t=0<(s=i.closest("label")).length)?s:bt(this.document[0].getElementsByTagName("label")).filter("[for='"+_t(i[0].id)+"']").first(),{element:e,isParent:t}},_enhance:function(){this.label.addClass("ui-btn ui-corner-all"),this.labelIsParent?this.input.add(this.label).wrapAll(this._wrapper()):(this.element.wrap(this._wrapper()),this.element.parent().prepend(this.label)),this._setOptions({theme:this.options.theme,iconpos:this.options.iconpos,mini:this.options.mini})},_wrapper:function(){return bt("<div class='"+(this.options.wrapperClass||"")+" ui-"+this.inputtype+(this.options.disabled?" ui-state-disabled":"")+"' ></div>")},_handleInputFocus:function(){this.label.addClass(bt.mobile.focusClass)},_handleInputBlur:function(){this.label.removeClass(bt.mobile.focusClass)},_handleInputVClick:function(){this.element.prop("checked",this.element.is(":checked")),this._getInputSet().not(this.element).prop("checked",!1),this._updateAll(!0)},_handleLabelVMouseOver:function(e){this.label.parent().hasClass("ui-state-disabled")&&e.stopPropagation()},_handleLabelVClick:function(e){var t=this.element;if(!t.is(":disabled"))return this._cacheVals(),t.prop("checked","radio"===this.inputtype||!t.prop("checked")),t.triggerHandler("click"),this._getInputSet().not(t).prop("checked",!1),this._updateAll(),!1;e.preventDefault()},_cacheVals:function(){this._getInputSet().each(function(){bt(this).attr("data-"+bt.mobile.ns+"cacheVal",this.checked)})},_getInputSet:function(){var e=this.element[0],t=e.name,i=e.form,s=this.element.parents().last().get(0),n=this.element;return t&&"radio"===this.inputtype&&s&&(e="input[type='radio'][name='"+_t(t)+"']",n=i?((t=i.getAttribute("id"))&&(n=bt(e+"[form='"+_t(t)+"']",s)),bt(i).find(e).filter(function(){return this.form===i}).add(n)):bt(e,s).filter(function(){return!this.form})),n},_updateAll:function(t){var i=this;this._getInputSet().each(function(){var e=bt(this);!this.checked&&"checkbox"!==i.inputtype||t||e.trigger("change")}).checkboxradio("refresh")},_reset:function(){this.refresh()},_hasIcon:function(){var e,t=bt.mobile.controlgroup;return!(t&&0<(e=this.element.closest(":mobile-controlgroup,"+t.prototype.initSelector)).length)||"horizontal"!==((t=bt.data(e[0],"mobile-controlgroup"))?t.options.type:e.attr("data-"+bt.mobile.ns+"type"))},refresh:function(){var e=this.element[0].checked,t=bt.mobile.activeBtnClass,i="ui-btn-icon-"+this.options.iconpos,s=[],n=[];this._hasIcon()?(n.push(t),s.push(i)):(n.push(i),(e?s:n).push(t)),e?(s.push(this.checkedClass),n.push(this.uncheckedClass)):(s.push(this.uncheckedClass),n.push(this.checkedClass)),this.widget().toggleClass("ui-state-disabled",this.element.prop("disabled")),this.label.addClass(s.join(" ")).removeClass(n.join(" "))},widget:function(){return this.label.parent()},_setOptions:function(e){var t=this.label,i=this.options,s=this.widget(),n=this._hasIcon();e.disabled!==vt&&(this.input.prop("disabled",!!e.disabled),s.toggleClass("ui-state-disabled",!!e.disabled)),e.mini!==vt&&s.toggleClass("ui-mini",!!e.mini),e.theme!==vt&&t.removeClass("ui-btn-"+i.theme).addClass("ui-btn-"+e.theme),e.wrapperClass!==vt&&s.removeClass(i.wrapperClass).addClass(e.wrapperClass),e.iconpos!==vt&&n?t.removeClass("ui-btn-icon-"+i.iconpos).addClass("ui-btn-icon-"+e.iconpos):n||t.removeClass("ui-btn-icon-"+i.iconpos),this._super(e)}},bt.mobile.behaviors.formReset)),(Ct=e).widget("mobile.button",{initSelector:"input[type='button'], input[type='submit'], input[type='reset']",options:{theme:null,icon:null,iconpos:"left",iconshadow:!1,corners:!0,shadow:!0,inline:null,mini:null,wrapperClass:null,enhanced:!1},_create:function(){this.element.is(":disabled")&&(this.options.disabled=!0),this.options.enhanced||this._enhance(),Ct.extend(this,{wrapper:this.element.parent()}),this._on({focus:function(){this.widget().addClass(Ct.mobile.focusClass)},blur:function(){this.widget().removeClass(Ct.mobile.focusClass)}}),this.refresh(!0)},_enhance:function(){this.element.wrap(this._button())},_button:function(){var e=this.options,t=this._getIconClasses(this.options);return Ct("<div class='ui-btn ui-input-btn"+(e.wrapperClass?" "+e.wrapperClass:"")+(e.theme?" ui-btn-"+e.theme:"")+(e.corners?" ui-corner-all":"")+(e.shadow?" ui-shadow":"")+(e.inline?" ui-btn-inline":"")+(e.mini?" ui-mini":"")+(e.disabled?" ui-state-disabled":"")+(t?" "+t:"")+"' >"+this.element.val()+"</div>")},widget:function(){return this.wrapper},_destroy:function(){this.element.insertBefore(this.wrapper),this.wrapper.remove()},_getIconClasses:function(e){return e.icon?"ui-icon-"+e.icon+(e.iconshadow?" ui-shadow-icon":"")+" ui-btn-icon-"+e.iconpos:""},_setOptions:function(e){var t=this.widget();e.theme!==wt&&t.removeClass(this.options.theme).addClass("ui-btn-"+e.theme),e.corners!==wt&&t.toggleClass("ui-corner-all",e.corners),e.shadow!==wt&&t.toggleClass("ui-shadow",e.shadow),e.inline!==wt&&t.toggleClass("ui-btn-inline",e.inline),e.mini!==wt&&t.toggleClass("ui-mini",e.mini),e.disabled!==wt&&(this.element.prop("disabled",e.disabled),t.toggleClass("ui-state-disabled",e.disabled)),e.icon===wt&&e.iconshadow===wt&&e.iconpos===wt||t.removeClass(this._getIconClasses(this.options)).addClass(this._getIconClasses(Ct.extend({},this.options,e))),this._super(e)},refresh:function(e){var t=this.element.prop("disabled");this.options.icon&&"notext"===this.options.iconpos&&this.element.attr("title")&&this.element.attr("title",this.element.val()),e||(e=this.element.detach(),Ct(this.wrapper).text(this.element.val()).append(e)),this.options.disabled!==t&&this._setOptions({disabled:t})}}),xt=(yt=e)("meta[name=viewport]"),Tt=xt.attr("content"),kt=Tt+",maximum-scale=1, user-scalable=no",Pt=Tt+",maximum-scale=10, user-scalable=yes",Dt=/(user-scalable[\s]*=[\s]*no)|(maximum-scale[\s]*=[\s]*1)[$,\s]/.test(Tt),yt.mobile.zoom=yt.extend({},{enabled:!Dt,locked:!1,disable:function(e){Dt||yt.mobile.zoom.locked||(xt.attr("content",kt),yt.mobile.zoom.enabled=!1,yt.mobile.zoom.locked=e||!1)},enable:function(e){Dt||yt.mobile.zoom.locked&&!0!==e||(xt.attr("content",Pt),yt.mobile.zoom.enabled=!0,yt.mobile.zoom.locked=!1)},restore:function(){Dt||(xt.attr("content",Tt),yt.mobile.zoom.enabled=!0)}}),(Et=e).widget("mobile.textinput",{initSelector:"input[type='text'],input[type='search'],:jqmData(type='search'),input[type='number'],:jqmData(type='number'),input[type='password'],input[type='email'],input[type='url'],input[type='tel'],textarea,input[type='time'],input[type='date'],input[type='month'],input[type='week'],input[type='datetime'],input[type='datetime-local'],input[type='color'],input:not([type]),input[type='file']",options:{theme:null,corners:!0,mini:!1,preventFocusZoom:/iPhone|iPad|iPod/.test(navigator.platform)&&-1<navigator.userAgent.indexOf("AppleWebKit"),wrapperClass:"",enhanced:!1},_create:function(){var e=this.options,t=this.element.is("[type='search'], :jqmData(type='search')"),i="TEXTAREA"===this.element[0].tagName,s=this.element.is("[data-"+(Et.mobile.ns||"")+"type='range']"),n=(this.element.is("input")||this.element.is("[data-"+(Et.mobile.ns||"")+"type='search']"))&&!s;this.element.prop("disabled")&&(e.disabled=!0),Et.extend(this,{classes:this._classesFromOptions(),isSearch:t,isTextarea:i,isRange:s,inputNeedsWrap:n}),this._autoCorrect(),e.enhanced||this._enhance(),this._on({focus:"_handleFocus",blur:"_handleBlur"})},refresh:function(){this.setOptions({disabled:this.element.is(":disabled")})},_enhance:function(){var e=[];this.isTextarea&&e.push("ui-input-text"),(this.isTextarea||this.isRange)&&e.push("ui-shadow-inset"),this.inputNeedsWrap?this.element.wrap(this._wrap()):e=e.concat(this.classes),this.element.addClass(e.join(" "))},widget:function(){return this.inputNeedsWrap?this.element.parent():this.element},_classesFromOptions:function(){var e=this.options,t=[];return t.push("ui-body-"+(null===e.theme?"inherit":e.theme)),e.corners&&t.push("ui-corner-all"),e.mini&&t.push("ui-mini"),e.disabled&&t.push("ui-state-disabled"),e.wrapperClass&&t.push(e.wrapperClass),t},_wrap:function(){return Et("<div class='"+(this.isSearch?"ui-input-search ":"ui-input-text ")+this.classes.join(" ")+" ui-shadow-inset'></div>")},_autoCorrect:function(){void 0===this.element[0].autocorrect||Et.support.touchOverflow||(this.element[0].setAttribute("autocorrect","off"),this.element[0].setAttribute("autocomplete","off"))},_handleBlur:function(){this.widget().removeClass(Et.mobile.focusClass),this.options.preventFocusZoom&&Et.mobile.zoom.enable(!0)},_handleFocus:function(){this.options.preventFocusZoom&&Et.mobile.zoom.disable(!0),this.widget().addClass(Et.mobile.focusClass)},_setOptions:function(e){var t=this.widget();this._super(e),e.disabled===St&&e.mini===St&&e.corners===St&&e.theme===St&&e.wrapperClass===St||(t.removeClass(this.classes.join(" ")),this.classes=this._classesFromOptions(),t.addClass(this.classes.join(" "))),e.disabled!==St&&this.element.prop("disabled",!!e.disabled)},_destroy:function(){this.options.enhanced||(this.inputNeedsWrap&&this.element.unwrap(),this.element.removeClass("ui-input-text "+this.classes.join(" ")))}}),(At=e).widget("mobile.slider",At.extend({initSelector:"input[type='range'], :jqmData(type='range'), :jqmData(role='slider')",widgetEventPrefix:"slide",options:{theme:null,trackTheme:null,corners:!0,mini:!1,highlight:!1},_create:function(){var e,t,i,s,n,o,a,r,l,h,d=this.element,c=this.options.trackTheme||At.mobile.getAttribute(d[0],"theme"),u=c?" ui-bar-"+c:" ui-bar-inherit",p=this.options.corners||d.jqmData("corners")?" ui-corner-all":"",m=this.options.mini||d.jqmData("mini")?" ui-mini":"",f=d[0].nodeName.toLowerCase(),g="select"===f,b=d.parent().is(":jqmData(role='rangeslider')"),v=g?"ui-slider-switch":"",_=d.attr("id"),C=At("[for='"+_+"']"),w=C.attr("id")||_+"-label",y=g?0:parseFloat(d.attr("min")),x=g?d.find("option").length-1:parseFloat(d.attr("max")),c=D.parseFloat(d.attr("step")||1),_=E.createElement("a"),T=At(_),k=E.createElement("div"),P=At(k),h=!(!this.options.highlight||g)&&((h=E.createElement("div")).className="ui-slider-bg "+At.mobile.activeBtnClass,At(h).prependTo(P));if(C.attr("id",w),this.isToggleSwitch=g,_.setAttribute("href","#"),k.setAttribute("role","application"),k.className=[this.isToggleSwitch?"ui-slider ui-slider-track ui-shadow-inset ":"ui-slider-track ui-shadow-inset ",v,u,p,m].join(""),_.className="ui-slider-handle",k.appendChild(_),T.attr({role:"slider","aria-valuemin":y,"aria-valuemax":x,"aria-valuenow":this._value(),"aria-valuetext":this._value(),title:this._value(),"aria-labelledby":w}),At.extend(this,{slider:P,handle:T,control:d,type:f,step:c,max:x,min:y,valuebg:h,isRangeslider:b,dragging:!1,beforeStart:null,userModified:!1,mouseMoved:!1}),g){for((h=d.attr("tabindex"))&&T.attr("tabindex",h),d.attr("tabindex","-1").focus(function(){At(this).trigger("blur"),T.trigger("focus")}),(t=E.createElement("div")).className="ui-slider-inneroffset",i=0,s=k.childNodes.length;i<s;i++)t.appendChild(k.childNodes[i]);for(k.appendChild(t),T.addClass("ui-slider-handle-snapping"),n=0,o=(e=d.find("option")).length;n<o;n++)a=n?"a":"b",r=n?" "+At.mobile.activeBtnClass:"",(l=E.createElement("span")).className=["ui-slider-label ui-slider-label-",a,r].join(""),l.setAttribute("role","img"),l.appendChild(E.createTextNode(e[n].innerHTML)),At(l).prependTo(P);this._labels=At(".ui-slider-label",P)}d.addClass(g?"ui-slider-switch":"ui-slider-input"),this._on(d,{change:"_controlChange",keyup:"_controlKeyup",blur:"_controlBlur",vmouseup:"_controlVMouseUp"}),P.on("vmousedown",At.proxy(this._sliderVMouseDown,this)).on("vclick",!1),this._on(E,{vmousemove:"_preventDocumentDrag"}),this._on(P.add(E),{vmouseup:"_sliderVMouseUp"}),P.insertAfter(d),g||b||(t=this.options.mini?"<div class='ui-slider ui-mini'>":"<div class='ui-slider'>",d.add(P).wrapAll(t)),this._on(this.handle,{vmousedown:"_handleVMouseDown",keydown:"_handleKeydown",keyup:"_handleKeyup"}),this.handle.on("vclick",!1),this._handleFormReset(),this.refresh(It,It,!0)},_setOptions:function(e){e.theme!==It&&this._setTheme(e.theme),e.trackTheme!==It&&this._setTrackTheme(e.trackTheme),e.corners!==It&&this._setCorners(e.corners),e.mini!==It&&this._setMini(e.mini),e.highlight!==It&&this._setHighlight(e.highlight),e.disabled!==It&&this._setDisabled(e.disabled),this._super(e)},_controlChange:function(e){if(!1===this._trigger("controlchange",e))return!1;this.mouseMoved||this.refresh(this._value(),!0)},_controlKeyup:function(){this.refresh(this._value(),!0,!0)},_controlBlur:function(){this.refresh(this._value(),!0)},_controlVMouseUp:function(){this._checkedRefresh()},_handleVMouseDown:function(){this.handle.trigger("focus")},_handleKeydown:function(e){var t=this._value();if(!this.options.disabled){switch(e.keyCode){case At.mobile.keyCode.HOME:case At.mobile.keyCode.END:case At.mobile.keyCode.PAGE_UP:case At.mobile.keyCode.PAGE_DOWN:case At.mobile.keyCode.UP:case At.mobile.keyCode.RIGHT:case At.mobile.keyCode.DOWN:case At.mobile.keyCode.LEFT:e.preventDefault(),this._keySliding||(this._keySliding=!0,this.handle.addClass("ui-state-active"))}switch(e.keyCode){case At.mobile.keyCode.HOME:this.refresh(this.min);break;case At.mobile.keyCode.END:this.refresh(this.max);break;case At.mobile.keyCode.PAGE_UP:case At.mobile.keyCode.UP:case At.mobile.keyCode.RIGHT:this.refresh(t+this.step);break;case At.mobile.keyCode.PAGE_DOWN:case At.mobile.keyCode.DOWN:case At.mobile.keyCode.LEFT:this.refresh(t-this.step)}}},_handleKeyup:function(){this._keySliding&&(this._keySliding=!1,this.handle.removeClass("ui-state-active"))},_sliderVMouseDown:function(e){return this.options.disabled||1!==e.which&&0!==e.which&&e.which!==It||!1===this._trigger("beforestart",e)||(this.dragging=!0,this.userModified=!1,this.mouseMoved=!1,this.isToggleSwitch&&(this.beforeStart=this.element[0].selectedIndex),this.refresh(e),this._trigger("start")),!1},_sliderVMouseUp:function(){if(this.dragging)return this.dragging=!1,this.isToggleSwitch&&(this.handle.addClass("ui-slider-handle-snapping"),!this.mouseMoved||this.userModified?this.refresh(0===this.beforeStart?1:0):this.refresh(this.beforeStart)),this.mouseMoved=!1,this._trigger("stop"),!1},_preventDocumentDrag:function(e){return!1!==this._trigger("drag",e)&&(this.dragging&&!this.options.disabled?(this.mouseMoved=!0,this.isToggleSwitch&&this.handle.removeClass("ui-slider-handle-snapping"),this.refresh(e),this.userModified=this.beforeStart!==this.element[0].selectedIndex,!1):void 0)},_checkedRefresh:function(){this.value!==this._value()&&this.refresh(this._value())},_value:function(){return this.isToggleSwitch?this.element[0].selectedIndex:parseFloat(this.element.val())},_reset:function(){this.refresh(It,!1,!0)},refresh:function(e,t,i){var s,n,o,a,r,l,h,d,c,u=this,p=At.mobile.getAttribute(this.element[0],"theme"),m=this.options.theme||p,f=m?" ui-btn-"+m:"",g=this.options.trackTheme||p,m=g?" ui-bar-"+g:" ui-bar-inherit",p=this.options.corners?" ui-corner-all":"",g=this.options.mini?" ui-mini":"";if(u.slider[0].className=[this.isToggleSwitch?"ui-slider ui-slider-switch ui-slider-track ui-shadow-inset":"ui-slider-track ui-shadow-inset",m,p,g].join(""),(this.options.disabled||this.element.prop("disabled"))&&this.disable(),this.value=this._value(),this.options.highlight&&!this.isToggleSwitch&&0===this.slider.find(".ui-slider-bg").length&&(this.valuebg=((c=E.createElement("div")).className="ui-slider-bg "+At.mobile.activeBtnClass,At(c).prependTo(u.slider))),this.handle.addClass("ui-btn"+f+" ui-shadow"),m=this.element,g=(p=!this.isToggleSwitch)?[]:m.find("option"),c=p?parseFloat(m.attr("min")):0,u=p?parseFloat(m.attr("max")):g.length-1,f=p&&0<parseFloat(m.attr("step"))?parseFloat(m.attr("step")):1,"object"==typeof e){if(l=e,r=this.slider.offset().left,s=this.slider.width(),!this.dragging||l.pageX<r-8||l.pageX>r+s+8)return;o=1<(n=s/((u-c)/f))?(l.pageX-r)/s*100:Math.round((l.pageX-r)/s*100)}else null==e&&(e=p?parseFloat(m.val()||0):m[0].selectedIndex),o=(parseFloat(e)-c)/(u-c)*100;if(!isNaN(o)&&(r=(a=o/100*(u-c)+c)-(l=(a-c)%f),2*Math.abs(l)>=f&&(r+=0<l?f:-f),l=100/((u-c)/f),a=parseFloat(r.toFixed(5)),1<(n=void 0===n?s/((u-c)/f):n)&&p&&(o=(a-c)*l*(1/f)),u<(a=a<c?c:a)&&(a=u),this.handle.css("left",(o=100<(o=o<0?0:o)?100:o)+"%"),this.handle[0].setAttribute("aria-valuenow",p?a:g.eq(a).attr("value")),this.handle[0].setAttribute("aria-valuetext",p?a:g.eq(a).getEncodedText()),this.handle[0].setAttribute("title",p?a:g.eq(a).getEncodedText()),this.valuebg&&this.valuebg.css("width",o+"%"),this._labels&&(g=this.handle.width()/this.slider.width()*100,h=o&&g+(100-g)*o/100,d=100===o?0:Math.min(100+g-h,100),this._labels.each(function(){var e=At(this).hasClass("ui-slider-label-a");At(this).width((e?h:d)+"%")})),!i)){if(i=!1,p?(i=parseFloat(m.val())!==a,m.val(a)):(i=m[0].selectedIndex!==a,m[0].selectedIndex=a),!1===this._trigger("beforechange",e))return!1;!t&&i&&m.trigger("change")}},_setHighlight:function(e){(e=!!e)?(this.options.highlight=!!e,this.refresh()):this.valuebg&&(this.valuebg.remove(),this.valuebg=!1)},_setTheme:function(e){this.handle.removeClass("ui-btn-"+this.options.theme).addClass("ui-btn-"+e);var t=this.options.theme||"inherit",e=e||"inherit";this.control.removeClass("ui-body-"+t).addClass("ui-body-"+e)},_setTrackTheme:function(e){var t=this.options.trackTheme||"inherit",e=e||"inherit";this.slider.removeClass("ui-body-"+t).addClass("ui-body-"+e)},_setMini:function(e){e=!!e,this.isToggleSwitch||this.isRangeslider||(this.slider.parent().toggleClass("ui-mini",e),this.element.toggleClass("ui-mini",e)),this.slider.toggleClass("ui-mini",e)},_setCorners:function(e){this.slider.toggleClass("ui-corner-all",e),this.isToggleSwitch||this.control.toggleClass("ui-corner-all",e)},_setDisabled:function(e){this.element.prop("disabled",e=!!e),this.slider.toggleClass("ui-state-disabled",e).attr("aria-disabled",e),this.element.toggleClass("ui-state-disabled",e)}},At.mobile.behaviors.formReset)),(Bt=e).widget("mobile.slider",Bt.mobile.slider,{options:{popupEnabled:!1,showValue:!1},_create:function(){this._super(),Bt.extend(this,{_currentValue:null,_popup:null,_popupVisible:!1}),this._setOption("popupEnabled",this.options.popupEnabled),this._on(this.handle,{vmousedown:"_showPopup"}),this._on(this.slider.add(this.document),{vmouseup:"_hidePopup"}),this._refresh()},_positionPopup:function(){var e=this.handle.offset();this._popup.offset({left:e.left+(this.handle.width()-this._popup.width())/2,top:e.top-this._popup.outerHeight()-5})},_setOption:function(e,t){this._super(e,t),"showValue"===e?this.handle.html(t&&!this.options.mini?this._value():""):"popupEnabled"===e&&t&&!this._popup&&(this._popup=(Ot=Ot||Bt("<div></div>",{class:"ui-slider-popup ui-shadow ui-corner-all"})).clone().addClass("ui-body-"+(this.options.theme||"a")).hide().insertBefore(this.element))},refresh:function(){this._super.apply(this,arguments),this._refresh()},_refresh:function(){var e,t=this.options;t.popupEnabled&&this.handle.removeAttr("title"),(e=this._value())!==this._currentValue&&(this._currentValue=e,t.popupEnabled&&this._popup&&(this._positionPopup(),this._popup.html(e)),t.showValue&&!this.options.mini&&this.handle.html(e))},_showPopup:function(){this.options.popupEnabled&&!this._popupVisible&&(this.handle.html(""),this._popup.show(),this._positionPopup(),this._popupVisible=!0)},_hidePopup:function(){var e=this.options;e.popupEnabled&&this._popupVisible&&(e.showValue&&!e.mini&&this.handle.html(this._value()),this._popup.hide(),this._popupVisible=!1)}}),(Ft=e).widget("mobile.flipswitch",Ft.extend({options:{onText:"On",offText:"Off",theme:null,enhanced:!1,wrapperClass:null,corners:!0,mini:!1},_create:function(){this.options.enhanced?Ft.extend(this,{flipswitch:this.element.parent(),on:this.element.find(".ui-flipswitch-on").eq(0),off:this.element.find(".ui-flipswitch-off").eq(0),type:this.element.get(0).tagName}):this._enhance(),this._handleFormReset(),this._originalTabIndex=this.element.attr("tabindex"),null!=this._originalTabIndex&&this.on.attr("tabindex",this._originalTabIndex),this.element.attr("tabindex","-1"),this._on({focus:"_handleInputFocus"}),this.element.is(":disabled")&&this._setOptions({disabled:!0}),this._on(this.flipswitch,{click:"_toggle",swipeleft:"_left",swiperight:"_right"}),this._on(this.on,{keydown:"_keydown"}),this._on({change:"refresh"})},_handleInputFocus:function(){this.on.trigger("focus")},widget:function(){return this.flipswitch},_left:function(){this.flipswitch.removeClass("ui-flipswitch-active"),"SELECT"===this.type?this.element.get(0).selectedIndex=0:this.element.prop("checked",!1),this.element.trigger("change")},_right:function(){this.flipswitch.addClass("ui-flipswitch-active"),"SELECT"===this.type?this.element.get(0).selectedIndex=1:this.element.prop("checked",!0),this.element.trigger("change")},_enhance:function(){var e=Ft("<div>"),t=this.options,i=this.element,s=t.theme||"inherit",n=Ft("<a></a>",{href:"#"}),o=Ft("<span></span>"),a=i.get(0).tagName,r="INPUT"===a?t.onText:i.find("option").eq(1).text(),l="INPUT"===a?t.offText:i.find("option").eq(0).text();n.addClass("ui-flipswitch-on ui-btn ui-shadow ui-btn-inherit").text(r),o.addClass("ui-flipswitch-off").text(l),e.addClass("ui-flipswitch ui-shadow-inset ui-bar-"+s+" "+(t.wrapperClass||"")+" "+(i.is(":checked")||i.find("option").eq(1).is(":selected")?"ui-flipswitch-active":"")+(i.is(":disabled")?" ui-state-disabled":"")+(t.corners?" ui-corner-all":"")+(t.mini?" ui-mini":"")).append(n,o),i.addClass("ui-flipswitch-input").after(e).appendTo(e),Ft.extend(this,{flipswitch:e,on:n,off:o,type:a})},_reset:function(){this.refresh()},refresh:function(){var e=this.flipswitch.hasClass("ui-flipswitch-active")?"_right":"_left",t="SELECT"===this.type?0<this.element.get(0).selectedIndex?"_right":"_left":this.element.prop("checked")?"_right":"_left";t!==e&&this[t]()},_toggle:function(){var e=this.flipswitch.hasClass("ui-flipswitch-active")?"_left":"_right";this[e]()},_keydown:function(e){e.which===Ft.mobile.keyCode.LEFT?this._left():e.which===Ft.mobile.keyCode.RIGHT?this._right():e.which===Ft.mobile.keyCode.SPACE&&(this._toggle(),e.preventDefault())},_setOptions:function(e){var t,i;e.theme!==Nt&&(t=e.theme||"inherit",i=e.theme||"inherit",this.widget().removeClass("ui-bar-"+t).addClass("ui-bar-"+i)),e.onText!==Nt&&this.on.text(e.onText),e.offText!==Nt&&this.off.text(e.offText),e.disabled!==Nt&&this.widget().toggleClass("ui-state-disabled",e.disabled),e.mini!==Nt&&this.widget().toggleClass("ui-mini",e.mini),e.corners!==Nt&&this.widget().toggleClass("ui-corner-all",e.corners),this._super(e)},_destroy:function(){this.options.enhanced||(null!=this._originalTabIndex?this.element.attr("tabindex",this._originalTabIndex):this.element.removeAttr("tabindex"),this.on.remove(),this.off.remove(),this.element.unwrap(),this.flipswitch.remove(),this.removeClass("ui-flipswitch-input"))}},Ft.mobile.behaviors.formReset)),(Ht=e).widget("mobile.rangeslider",Ht.extend({options:{theme:null,trackTheme:null,corners:!0,mini:!1,highlight:!0},_create:function(){var e=this.element,t=this.options.mini?"ui-rangeslider ui-mini":"ui-rangeslider",i=e.find("input").first(),s=e.find("input").last(),n=e.find("label").first(),o=Ht.data(i.get(0),"mobile-slider")||Ht.data(i.slider().get(0),"mobile-slider"),a=Ht.data(s.get(0),"mobile-slider")||Ht.data(s.slider().get(0),"mobile-slider"),r=o.slider,l=a.slider,a=o.handle,o=Ht("<div class='ui-rangeslider-sliders' />").appendTo(e);i.addClass("ui-rangeslider-first"),s.addClass("ui-rangeslider-last"),e.addClass(t),r.appendTo(o),l.appendTo(o),n.insertBefore(e),a.prependTo(l),Ht.extend(this,{_inputFirst:i,_inputLast:s,_sliderFirst:r,_sliderLast:l,_label:n,_targetVal:null,_sliderTarget:!1,_sliders:o,_proxy:!1}),this.refresh(),this._on(this.element.find("input.ui-slider-input"),{slidebeforestart:"_slidebeforestart",slidestop:"_slidestop",slidedrag:"_slidedrag",slidebeforechange:"_change",blur:"_change",keyup:"_change"}),this._on({mousedown:"_change"}),this._on(this.element.closest("form"),{reset:"_handleReset"}),this._on(a,{vmousedown:"_dragFirstHandle"})},_handleReset:function(){var e=this;setTimeout(function(){e._updateHighlight()},0)},_dragFirstHandle:function(e){return Ht.data(this._inputFirst.get(0),"mobile-slider").dragging=!0,Ht.data(this._inputFirst.get(0),"mobile-slider").refresh(e),Ht.data(this._inputFirst.get(0),"mobile-slider")._trigger("start"),!1},_slidedrag:function(e){var t=Ht(e.target).is(this._inputFirst),i=t?this._inputLast:this._inputFirst;if(this._sliderTarget=!1,"first"===this._proxy&&t||"last"===this._proxy&&!t)return Ht.data(i.get(0),"mobile-slider").dragging=!0,Ht.data(i.get(0),"mobile-slider").refresh(e),!1},_slidestop:function(e){e=Ht(e.target).is(this._inputFirst);this._proxy=!1,this.element.find("input").trigger("vmouseup"),this._sliderFirst.css("z-index",e?1:"")},_slidebeforestart:function(e){this._sliderTarget=!1,Ht(e.originalEvent.target).hasClass("ui-slider-track")&&(this._sliderTarget=!0,this._targetVal=Ht(e.target).val())},_setOptions:function(e){e.theme!==qt&&this._setTheme(e.theme),e.trackTheme!==qt&&this._setTrackTheme(e.trackTheme),e.mini!==qt&&this._setMini(e.mini),e.highlight!==qt&&this._setHighlight(e.highlight),e.disabled!==qt&&this._setDisabled(e.disabled),this._super(e),this.refresh()},refresh:function(){var e=this.element,t=this.options;(this._inputFirst.is(":disabled")||this._inputLast.is(":disabled"))&&(this.options.disabled=!0),e.find("input").slider({theme:t.theme,trackTheme:t.trackTheme,disabled:t.disabled,corners:t.corners,mini:t.mini,highlight:t.highlight}).slider("refresh"),this._updateHighlight()},_change:function(e){if("keyup"===e.type)return this._updateHighlight(),!1;var t=this,i=parseFloat(this._inputFirst.val(),10),s=parseFloat(this._inputLast.val(),10),n=Ht(e.target).hasClass("ui-rangeslider-first"),o=n?this._inputFirst:this._inputLast,a=n?this._inputLast:this._inputFirst;if(this._inputFirst.val()>this._inputLast.val()&&"mousedown"===e.type&&!Ht(e.target).hasClass("ui-slider-handle"))o.trigger("blur");else if("mousedown"===e.type)return;return s<i&&!this._sliderTarget?(o.val(n?s:i).slider("refresh"),this._trigger("normalize")):s<i&&(o.val(this._targetVal).slider("refresh"),setTimeout(function(){a.val(n?i:s).slider("refresh"),Ht.data(a.get(0),"mobile-slider").handle.trigger("focus"),t._sliderFirst.css("z-index",n?"":1),t._trigger("normalize")},0),this._proxy=n?"first":"last"),i===s?(Ht.data(o.get(0),"mobile-slider").handle.css("z-index",1),Ht.data(a.get(0),"mobile-slider").handle.css("z-index",0)):(Ht.data(a.get(0),"mobile-slider").handle.css("z-index",""),Ht.data(o.get(0),"mobile-slider").handle.css("z-index","")),this._updateHighlight(),!(s<=i)&&void 0},_updateHighlight:function(){var e=parseInt(Ht.data(this._inputFirst.get(0),"mobile-slider").handle.get(0).style.left,10),t=parseInt(Ht.data(this._inputLast.get(0),"mobile-slider").handle.get(0).style.left,10)-e;this.element.find(".ui-slider-bg").css({"margin-left":e+"%",width:t+"%"})},_setTheme:function(e){this._inputFirst.slider("option","theme",e),this._inputLast.slider("option","theme",e)},_setTrackTheme:function(e){this._inputFirst.slider("option","trackTheme",e),this._inputLast.slider("option","trackTheme",e)},_setMini:function(e){this._inputFirst.slider("option","mini",e),this._inputLast.slider("option","mini",e),this.element.toggleClass("ui-mini",!!e)},_setHighlight:function(e){this._inputFirst.slider("option","highlight",e),this._inputLast.slider("option","highlight",e)},_setDisabled:function(e){this._inputFirst.prop("disabled",e),this._inputLast.prop("disabled",e)},_destroy:function(){this._label.prependTo(this.element),this.element.removeClass("ui-rangeslider ui-mini"),this._inputFirst.after(this._sliderFirst),this._inputLast.after(this._sliderLast),this._sliders.remove(),this.element.find("input").removeClass("ui-rangeslider-first ui-rangeslider-last").slider("destroy")}},Ht.mobile.behaviors.formReset)),(Lt=e).widget("mobile.textinput",Lt.mobile.textinput,{options:{clearBtn:!1,clearBtnText:"Clear text"},_create:function(){this._super(),this.isSearch&&(this.options.clearBtn=!0),this.options.clearBtn&&this.inputNeedsWrap&&this._addClearBtn()},clearButton:function(){return Lt("<a href='#' tabindex='-1' aria-hidden='true' class='ui-input-clear ui-btn ui-icon-delete ui-btn-icon-notext ui-corner-all'></a>").attr("title",this.options.clearBtnText).text(this.options.clearBtnText)},_clearBtnClick:function(e){this.element.val("").trigger("focus").trigger("change"),this._clearBtn.addClass("ui-input-clear-hidden"),e.preventDefault()},_addClearBtn:function(){this.options.enhanced||this._enhanceClear(),Lt.extend(this,{_clearBtn:this.widget().find("a.ui-input-clear")}),this._bindClearEvents(),this._toggleClear()},_enhanceClear:function(){this.clearButton().appendTo(this.widget()),this.widget().addClass("ui-input-has-clear")},_bindClearEvents:function(){this._on(this._clearBtn,{click:"_clearBtnClick"}),this._on({keyup:"_toggleClear",change:"_toggleClear",input:"_toggleClear",focus:"_toggleClear",blur:"_toggleClear",cut:"_toggleClear",paste:"_toggleClear"})},_unbindClear:function(){this._off(this._clearBtn,"click"),this._off(this.element,"keyup change input focus blur cut paste")},_setOptions:function(e){this._super(e),e.clearBtn===jt||this.element.is("textarea, :jqmData(type='range')")||(e.clearBtn?this._addClearBtn():this._destroyClear()),e.clearBtnText!==jt&&this._clearBtn!==jt&&this._clearBtn.text(e.clearBtnText).attr("title",e.clearBtnText)},_toggleClear:function(){this._delay("_toggleClearClass",0)},_toggleClearClass:function(){this._clearBtn.toggleClass("ui-input-clear-hidden",!this.element.val())},_destroyClear:function(){this.widget().removeClass("ui-input-has-clear"),this._unbindClear(),this._clearBtn.remove()},_destroy:function(){this._super(),this.options.clearBtn&&this._destroyClear()}}),(Mt=e).widget("mobile.textinput",Mt.mobile.textinput,{options:{autogrow:!0,keyupTimeoutBuffer:100},_create:function(){this._super(),this.options.autogrow&&this.isTextarea&&this._autogrow()},_autogrow:function(){this.element.addClass("ui-textinput-autogrow"),this._on({keyup:"_timeout",change:"_timeout",input:"_timeout",paste:"_timeout"}),this._on(!0,this.document,{pageshow:"_handleShow",popupbeforeposition:"_handleShow",updatelayout:"_handleShow",panelopen:"_handleShow"})},_handleShow:function(e){Mt.contains(e.target,this.element[0])&&this.element.is(":visible")&&("popupbeforeposition"!==e.type&&this.element.addClass("ui-textinput-autogrow-resize").animationComplete(Mt.proxy(function(){this.element.removeClass("ui-textinput-autogrow-resize")},this),"transition"),this._prepareHeightUpdate())},_unbindAutogrow:function(){this.element.removeClass("ui-textinput-autogrow"),this._off(this.element,"keyup change input paste"),this._off(this.document,"pageshow popupbeforeposition updatelayout panelopen")},keyupTimeout:null,_prepareHeightUpdate:function(e){this.keyupTimeout&&clearTimeout(this.keyupTimeout),void 0===e?this._updateHeight():this.keyupTimeout=this._delay("_updateHeight",e)},_timeout:function(){this._prepareHeightUpdate(this.options.keyupTimeoutBuffer)},_updateHeight:function(){var e,t,i=this.window.scrollTop();this.keyupTimeout=0,"onpage"in this.element[0]||this.element.css({height:0,"min-height":0,"max-height":0}),t=this.element[0].scrollHeight,e=this.element[0].clientHeight,t=t+(parseFloat(this.element.css("border-top-width"))+parseFloat(this.element.css("border-bottom-width")))+15,0===e&&(t+=parseFloat(this.element.css("padding-top"))+parseFloat(this.element.css("padding-bottom"))),this.element.css({height:t,"min-height":"","max-height":""}),this.window.scrollTop(i)},refresh:function(){this.options.autogrow&&this.isTextarea&&this._updateHeight()},_setOptions:function(e){this._super(e),void 0!==e.autogrow&&this.isTextarea&&(e.autogrow?this._autogrow():this._unbindAutogrow())}}),(Ut=e).widget("mobile.selectmenu",Ut.extend({initSelector:"select:not( :jqmData(role='slider')):not( :jqmData(role='flipswitch') )",options:{theme:null,icon:"carat-d",iconpos:"right",inline:!1,corners:!0,shadow:!0,iconshadow:!1,overlayTheme:null,dividerTheme:null,hidePlaceholderMenuItems:!0,closeText:"Close",nativeMenu:!0,preventFocusZoom:/iPhone|iPad|iPod/.test(navigator.platform)&&-1<navigator.userAgent.indexOf("AppleWebKit"),mini:!1},_button:function(){return Ut("<div/>")},_setDisabled:function(e){return this.element.attr("disabled",e),this.button.attr("aria-disabled",e),this._setOption("disabled",e)},_focusButton:function(){var e=this;setTimeout(function(){e.button.trigger("focus")},40)},_selectOptions:function(){return this.select.find("option")},_preExtension:function(){var e=this.options.inline||this.element.jqmData("inline"),t=this.options.mini||this.element.jqmData("mini"),i="";~this.element[0].className.indexOf("ui-btn-left")&&(i=" ui-btn-left"),~this.element[0].className.indexOf("ui-btn-right")&&(i=" ui-btn-right"),e&&(i+=" ui-btn-inline"),t&&(i+=" ui-mini"),this.select=this.element.removeClass("ui-btn-left ui-btn-right").wrap("<div class='ui-select"+i+"'>"),this.selectId=this.select.attr("id")||"select-"+this.uuid,this.buttonId=this.selectId+"-button",this.label=Ut("label[for='"+this.selectId+"']"),this.isMultiple=this.select[0].multiple},_destroy:function(){var e=this.element.parents(".ui-select");0<e.length&&(e.is(".ui-btn-left, .ui-btn-right")&&this.element.addClass(e.hasClass("ui-btn-left")?"ui-btn-left":"ui-btn-right"),this.element.insertAfter(e),e.remove())},_create:function(){this._preExtension(),this.button=this._button();var e=this,t=this.options,i=!!t.icon&&(t.iconpos||this.select.jqmData("iconpos")),i=this.button.insertBefore(this.select).attr("id",this.buttonId).addClass("ui-btn"+(t.icon?" ui-icon-"+t.icon+" ui-btn-icon-"+i+(t.iconshadow?" ui-shadow-icon":""):"")+(t.theme?" ui-btn-"+t.theme:"")+(t.corners?" ui-corner-all":"")+(t.shadow?" ui-shadow":""));this.setButtonText(),t.nativeMenu&&D.opera&&D.opera.version&&i.addClass("ui-select-nativeonly"),this.isMultiple&&(this.buttonCount=Ut("<span>").addClass("ui-li-count ui-body-inherit").hide().appendTo(i.addClass("ui-li-has-count"))),(t.disabled||this.element.attr("disabled"))&&this.disable(),this.select.on("change",function(){e.refresh(),t.nativeMenu&&e._delay(function(){e.select.trigger("blur")})}),this._handleFormReset(),this._on(this.button,{keydown:"_handleKeydown"}),this.build()},build:function(){var e=this;this.select.appendTo(e.button).on("vmousedown",function(){e.button.addClass(Ut.mobile.activeBtnClass)}).on("focus",function(){e.button.addClass(Ut.mobile.focusClass)}).on("blur",function(){e.button.removeClass(Ut.mobile.focusClass)}).on("focus vmouseover",function(){e.button.trigger("vmouseover")}).on("vmousemove",function(){e.button.removeClass(Ut.mobile.activeBtnClass)}).on("change blur vmouseout",function(){e.button.trigger("vmouseout").removeClass(Ut.mobile.activeBtnClass)}),e.button.on("vmousedown",function(){e.options.preventFocusZoom&&Ut.mobile.zoom.disable(!0)}),e.label.on("click focus",function(){e.options.preventFocusZoom&&Ut.mobile.zoom.disable(!0)}),e.select.on("focus",function(){e.options.preventFocusZoom&&Ut.mobile.zoom.disable(!0)}),e.button.on("mouseup",function(){e.options.preventFocusZoom&&setTimeout(function(){Ut.mobile.zoom.enable(!0)},0)}),e.select.on("blur",function(){e.options.preventFocusZoom&&Ut.mobile.zoom.enable(!0)})},selected:function(){return this._selectOptions().filter(":selected")},selectedIndices:function(){var e=this;return this.selected().map(function(){return e._selectOptions().index(this)}).get()},setButtonText:function(){var e=this,t=this.selected(),i=this.placeholder,s=Ut(E.createElement("span"));this.button.children("span").not(".ui-li-count").remove().end().end().prepend(((i=t.length?t.map(function(){return Ut(this).text()}).get().join(", "):e.placeholder)?s.text(i):s.html("&#160;"),s.addClass(e.select.attr("class")).addClass(t.attr("class")).removeClass("ui-screen-hidden")))},setButtonCount:function(){var e=this.selected();this.isMultiple&&this.buttonCount[1<e.length?"show":"hide"]().text(e.length)},_handleKeydown:function(){this._delay("_refreshButton")},_reset:function(){this.refresh()},_refreshButton:function(){this.setButtonText(),this.setButtonCount()},refresh:function(){this._refreshButton()},open:Ut.noop,close:Ut.noop,disable:function(){this._setDisabled(!0),this.button.addClass("ui-state-disabled")},enable:function(){this._setDisabled(!1),this.button.removeClass("ui-state-disabled")}},Ut.mobile.behaviors.formReset)),(Rt=e).mobile.links=function(e){Rt(e).find("a").jqmEnhanceable().filter(":jqmData(rel='popup')[href][href!='']").each(function(){var e=this.getAttribute("href").substring(1);e&&(this.setAttribute("aria-haspopup",!0),this.setAttribute("aria-owns",e),this.setAttribute("aria-expanded",!1))}).end().not(".ui-btn, :jqmData(role='none'), :jqmData(role='nojs')").addClass("ui-link")},(Wt=e).widget("mobile.popup",{options:{wrapperClass:null,theme:null,overlayTheme:null,shadow:!0,corners:!0,transition:"none",positionTo:"origin",tolerance:null,closeLinkSelector:"a:jqmData(rel='back')",closeLinkEvents:"click.popup",navigateEvents:"navigate.popup",closeEvents:"navigate.popup pagebeforechange.popup",dismissible:!0,enhanced:!1,history:!Wt.mobile.browser.oldIE},_handleDocumentVmousedown:function(e){this._isOpen&&Wt.contains(this._ui.container[0],e.target)&&this._ignoreResizeEvents()},_create:function(){var e=this.element,t=e.attr("id"),i=this.options;i.history=i.history&&Wt.mobile.ajaxEnabled&&Wt.mobile.hashListeningEnabled,this._on(this.document,{vmousedown:"_handleDocumentVmousedown"}),Wt.extend(this,{_scrollTop:0,_page:e.closest(".ui-page"),_ui:null,_fallbackTransition:"",_currentTransition:!1,_prerequisites:null,_isOpen:!1,_tolerance:null,_resizeData:null,_ignoreResizeTo:0,_orientationchangeInProgress:!1}),0===this._page.length&&(this._page=Wt("body")),i.enhanced?this._ui={container:e.parent(),screen:e.parent().prev(),placeholder:Wt(this.document[0].getElementById(t+"-placeholder"))}:(this._ui=this._enhance(e,t),this._applyTransition(i.transition)),this._setTolerance(i.tolerance)._ui.focusElement=this._ui.container,this._on(this._ui.screen,{vclick:"_eatEventAndClose"}),this._on(this.window,{orientationchange:Wt.proxy(this,"_handleWindowOrientationchange"),resize:Wt.proxy(this,"_handleWindowResize"),keyup:Wt.proxy(this,"_handleWindowKeyUp")}),this._on(this.document,{focusin:"_handleDocumentFocusIn"})},_enhance:function(e,t){var i=this.options,s=i.wrapperClass,n={screen:Wt("<div class='ui-screen-hidden ui-popup-screen "+this._themeClassFromOption("ui-overlay-",i.overlayTheme)+"'></div>"),placeholder:Wt("<div style='display: none;'>\x3c!-- placeholder --\x3e</div>"),container:Wt("<div class='ui-popup-container ui-popup-hidden ui-popup-truncate"+(s?" "+s:"")+"'></div>")},s=this.document[0].createDocumentFragment();return s.appendChild(n.screen[0]),s.appendChild(n.container[0]),t&&(n.screen.attr("id",t+"-screen"),n.container.attr("id",t+"-popup"),n.placeholder.attr("id",t+"-placeholder").html("\x3c!-- placeholder for "+t+" --\x3e")),this._page[0].appendChild(s),n.placeholder.insertAfter(e),e.detach().addClass("ui-popup "+this._themeClassFromOption("ui-body-",i.theme)+" "+(i.shadow?"ui-overlay-shadow ":"")+(i.corners?"ui-corner-all ":"")).appendTo(n.container),n},_eatEventAndClose:function(e){return e.preventDefault(),e.stopImmediatePropagation(),this.options.dismissible&&this.close(),!1},_resizeScreen:function(){var e=this._ui.screen,t=this._ui.container.outerHeight(!0),i=e.removeAttr("style").height(),s=this.document.height()-1;i<s?e.height(s):i<t&&e.height(t)},_handleWindowKeyUp:function(e){if(this._isOpen&&e.keyCode===Wt.mobile.keyCode.ESCAPE)return this._eatEventAndClose(e)},_expectResizeEvent:function(){var e=qi(this.window);if(this._resizeData){if(e.x===this._resizeData.windowCoordinates.x&&e.y===this._resizeData.windowCoordinates.y&&e.cx===this._resizeData.windowCoordinates.cx&&e.cy===this._resizeData.windowCoordinates.cy)return!1;clearTimeout(this._resizeData.timeoutId)}return this._resizeData={timeoutId:this._delay("_resizeTimeout",200),windowCoordinates:e},!0},_resizeTimeout:function(){this._isOpen?this._expectResizeEvent()||(this._ui.container.hasClass("ui-popup-hidden")&&(this._ui.container.removeClass("ui-popup-hidden ui-popup-truncate"),this.reposition({positionTo:"window"}),this._ignoreResizeEvents()),this._resizeScreen(),this._resizeData=null,this._orientationchangeInProgress=!1):(this._resizeData=null,this._orientationchangeInProgress=!1)},_stopIgnoringResizeEvents:function(){this._ignoreResizeTo=0},_ignoreResizeEvents:function(){this._ignoreResizeTo&&clearTimeout(this._ignoreResizeTo),this._ignoreResizeTo=this._delay("_stopIgnoringResizeEvents",1e3)},_handleWindowResize:function(){this._isOpen&&0===this._ignoreResizeTo&&(!this._expectResizeEvent()&&!this._orientationchangeInProgress||this._ui.container.hasClass("ui-popup-hidden")||this._ui.container.addClass("ui-popup-hidden ui-popup-truncate").removeAttr("style"))},_handleWindowOrientationchange:function(){!this._orientationchangeInProgress&&this._isOpen&&0===this._ignoreResizeTo&&(this._expectResizeEvent(),this._orientationchangeInProgress=!0)},_handleDocumentFocusIn:function(e){var t,i=e.target,s=this._ui;if(this._isOpen){if(i!==s.container[0]){if(t=Wt(i),!Wt.contains(s.container[0],i))return Wt(this.document[0].activeElement).one("focus",Wt.proxy(function(){this._safelyBlur(i)},this)),s.focusElement.trigger("focus"),e.preventDefault(),e.stopImmediatePropagation(),!1;s.focusElement[0]===s.container[0]&&(s.focusElement=t)}this._ignoreResizeEvents()}},_themeClassFromOption:function(e,t){return t?"none"===t?"":e+t:e+"inherit"},_applyTransition:function(e){return e&&(this._ui.container.removeClass(this._fallbackTransition),"none"!==e&&(this._fallbackTransition=Wt.mobile._maybeDegradeTransition(e),"none"===this._fallbackTransition&&(this._fallbackTransition=""),this._ui.container.addClass(this._fallbackTransition))),this},_setOptions:function(e){var t=this.options,i=this.element,s=this._ui.screen;return e.wrapperClass!==zt&&this._ui.container.removeClass(t.wrapperClass).addClass(e.wrapperClass),e.theme!==zt&&i.removeClass(this._themeClassFromOption("ui-body-",t.theme)).addClass(this._themeClassFromOption("ui-body-",e.theme)),e.overlayTheme!==zt&&(s.removeClass(this._themeClassFromOption("ui-overlay-",t.overlayTheme)).addClass(this._themeClassFromOption("ui-overlay-",e.overlayTheme)),this._isOpen&&s.addClass("in")),e.shadow!==zt&&i.toggleClass("ui-overlay-shadow",e.shadow),e.corners!==zt&&i.toggleClass("ui-corner-all",e.corners),e.transition!==zt&&(this._currentTransition||this._applyTransition(e.transition)),e.tolerance!==zt&&this._setTolerance(e.tolerance),e.disabled!==zt&&e.disabled&&this.close(),this._super(e)},_setTolerance:function(e){var i,t={t:30,r:15,b:30,l:15};if(e!==zt)switch(i=String(e).split(","),Wt.each(i,function(e,t){i[e]=parseInt(t,10)}),i.length){case 1:isNaN(i[0])||(t.t=t.r=t.b=t.l=i[0]);break;case 2:isNaN(i[0])||(t.t=t.b=i[0]),isNaN(i[1])||(t.l=t.r=i[1]);break;case 4:isNaN(i[0])||(t.t=i[0]),isNaN(i[1])||(t.r=i[1]),isNaN(i[2])||(t.b=i[2]),isNaN(i[3])||(t.l=i[3])}return this._tolerance=t,this},_clampPopupWidth:function(e){var t=qi(this.window),t={x:this._tolerance.l,y:t.y+this._tolerance.t,cx:t.cx-this._tolerance.l-this._tolerance.r,cy:t.cy-this._tolerance.t-this._tolerance.b};return e||this._ui.container.css("max-width",t.cx),{rc:t,menuSize:{cx:this._ui.container.outerWidth(!0),cy:this._ui.container.outerHeight(!0)}}},_calculateFinalLocation:function(e,t){var i=t.rc,t=t.menuSize,e={left:Hi(i.cx,t.cx,i.x,e.x),top:Hi(i.cy,t.cy,i.y,e.y)};return e.top=Math.max(0,e.top),e.top-=Math.min(e.top,Math.max(0,e.top+t.cy-this.document.height())),e},_placementCoords:function(e){return this._calculateFinalLocation(e,this._clampPopupWidth())},_createPrerequisites:function(e,t,i){var s=this,n={screen:Wt.Deferred(),container:Wt.Deferred()};n.screen.done(function(){n===s._prerequisites&&e()}),n.container.done(function(){n===s._prerequisites&&t()}),Wt.when(n.screen,n.container).done(function(){n===s._prerequisites&&(s._prerequisites=null,i())}),s._prerequisites=n},_animate:function(e){this._ui.screen.removeClass(e.classToRemove).addClass(e.screenClassToAdd),e.prerequisites.screen.resolve(),e.transition&&"none"!==e.transition&&(e.applyTransition&&this._applyTransition(e.transition),this._fallbackTransition)?this._ui.container.addClass(e.containerClassToAdd).removeClass(e.classToRemove).animationComplete(Wt.proxy(e.prerequisites.container,"resolve")):(this._ui.container.removeClass(e.classToRemove),e.prerequisites.container.resolve())},_desiredCoords:function(e){var t=null,i=qi(this.window),s=e.x,n=e.y,e=e.positionTo;if(e&&"origin"!==e)if("window"===e)s=i.cx/2+i.x,n=i.cy/2+i.y;else{try{t=Wt(e)}catch(e){t=null}t&&(t.filter(":visible"),0===t.length&&(t=null))}return t&&(s=(e=t.offset()).left+t.outerWidth()/2,n=e.top+t.outerHeight()/2),{x:s="number"!=typeof s||isNaN(s)?i.cx/2+i.x:s,y:n="number"!=typeof n||isNaN(n)?i.cy/2+i.y:n}},_reposition:function(e){e={x:e.x,y:e.y,positionTo:e.positionTo},this._trigger("beforeposition",zt,e),this._ui.container.offset(this._placementCoords(this._desiredCoords(e)))},reposition:function(e){this._isOpen&&this._reposition(e)},_safelyBlur:function(e){e!==this.window[0]&&"body"!==e.nodeName.toLowerCase()&&Wt(e).trigger("blur")},_openPrerequisitesComplete:function(){var e=this.element.attr("id"),t=this._ui.container.find(":focusable").first();this._ui.container.addClass("ui-popup-active"),this._isOpen=!0,this._resizeScreen(),Wt.contains(this._ui.container[0],this.document[0].activeElement)||this._safelyBlur(this.document[0].activeElement),0<t.length&&(this._ui.focusElement=t),this._ignoreResizeEvents(),e&&this.document.find("[aria-haspopup='true'][aria-owns='"+e+"']").attr("aria-expanded",!0),this._trigger("afteropen")},_open:function(e){var t,i,s,n=Wt.extend({},this.options,e),t=(t=navigator.userAgent,i=t.match(/AppleWebKit\/([0-9\.]+)/),s=!!i&&i[1],e=t.match(/Android (\d+(?:\.\d+))/),i=!!e&&e[1],t=-1<t.indexOf("Chrome"),!(!(null!==e&&"4.0"===i&&s&&534.13<s)||t));this._createPrerequisites(Wt.noop,Wt.noop,Wt.proxy(this,"_openPrerequisitesComplete")),this._currentTransition=n.transition,this._applyTransition(n.transition),this._ui.screen.removeClass("ui-screen-hidden"),this._ui.container.removeClass("ui-popup-truncate"),this._reposition(n),this._ui.container.removeClass("ui-popup-hidden"),this.options.overlayTheme&&t&&this.element.closest(".ui-page").addClass("ui-popup-open"),this._animate({additionalCondition:!0,transition:n.transition,classToRemove:"",screenClassToAdd:"in",containerClassToAdd:"in",applyTransition:!1,prerequisites:this._prerequisites})},_closePrerequisiteScreen:function(){this._ui.screen.removeClass("out").addClass("ui-screen-hidden")},_closePrerequisiteContainer:function(){this._ui.container.removeClass("reverse out").addClass("ui-popup-hidden ui-popup-truncate").removeAttr("style")},_closePrerequisitesDone:function(){var e=this._ui.container,t=this.element.attr("id");Wt.mobile.popup.active=zt,Wt(":focus",e[0]).add(e[0]).trigger("blur"),t&&this.document.find("[aria-haspopup='true'][aria-owns='"+t+"']").attr("aria-expanded",!1),this._trigger("afterclose")},_close:function(e){this._ui.container.removeClass("ui-popup-active"),this._page.removeClass("ui-popup-open"),this._isOpen=!1,this._createPrerequisites(Wt.proxy(this,"_closePrerequisiteScreen"),Wt.proxy(this,"_closePrerequisiteContainer"),Wt.proxy(this,"_closePrerequisitesDone")),this._animate({additionalCondition:this._ui.screen.hasClass("in"),transition:e?"none":this._currentTransition,classToRemove:"in",screenClassToAdd:"out",containerClassToAdd:"reverse out",applyTransition:!0,prerequisites:this._prerequisites})},_unenhance:function(){this.options.enhanced||(this._setOptions({theme:Wt.mobile.popup.prototype.options.theme}),this.element.detach().insertAfter(this._ui.placeholder).removeClass("ui-popup ui-overlay-shadow ui-corner-all ui-body-inherit"),this._ui.screen.remove(),this._ui.container.remove(),this._ui.placeholder.remove())},_destroy:function(){return Wt.mobile.popup.active===this?(this.element.one("popupafterclose",Wt.proxy(this,"_unenhance")),this.close()):this._unenhance(),this},_closePopup:function(e,t){var i=this.options,s=!1;e&&e.isDefaultPrevented()||Wt.mobile.popup.active!==this||(D.scrollTo(0,this._scrollTop),e&&"pagebeforechange"===e.type&&t&&(t="string"==typeof t.toPage?t.toPage:t.toPage.jqmData("url"),t=(t=Wt.mobile.path.parseUrl(t)).pathname+t.search+t.hash,this._myUrl!==Wt.mobile.path.makeUrlAbsolute(t)?s=!0:e.preventDefault()),this.window.off(i.closeEvents),this.element.off(i.closeLinkSelector,i.closeLinkEvents),this._close(s))},_bindContainerClose:function(){this.window.on(this.options.closeEvents,Wt.proxy(this,"_closePopup"))},widget:function(){return this._ui.container},open:function(t){var e,i,s,n,o=this,a=this.options;return Wt.mobile.popup.active||a.disabled?this:((Wt.mobile.popup.active=this)._scrollTop=this.window.scrollTop(),a.history?(n=Wt.mobile.navigate.history,e=Wt.mobile.dialogHashKey,s=!!(i=Wt.mobile.activePage)&&i.hasClass("ui-dialog"),this._myUrl=i=n.getActive().url,-1<i.indexOf(e)&&!s&&0<n.activeIndex?(o._open(t),o._bindContainerClose()):(-1!==i.indexOf(e)||s?i=Wt.mobile.path.parseLocation().hash+e:i+=-1<i.indexOf("#")?e:"#"+e,this.window.one("beforenavigate",function(e){e.preventDefault(),o._open(t),o._bindContainerClose()}),this.urlAltered=!0,Wt.mobile.navigate(i,{role:"dialog"})),this):(o._open(t),o._bindContainerClose(),o.element.on(a.closeLinkSelector,a.closeLinkEvents,function(e){o.close(),e.preventDefault()}),this))},close:function(){return Wt.mobile.popup.active!==this||(this._scrollTop=this.window.scrollTop(),this.options.history&&this.urlAltered?(Wt.mobile.back(),this.urlAltered=!1):this._closePopup()),this}}),Wt.mobile.popup.handleLink=function(e){var t=Wt.mobile.path,i=Wt(t.hashToSelector(t.parseUrl(e.attr("href")).hash)).first();0<i.length&&i.data("mobile-popup")&&(t=e.offset(),i.popup("open",{x:t.left+e.outerWidth()/2,y:t.top+e.outerHeight()/2,transition:e.jqmData("transition"),positionTo:e.jqmData("position-to")})),setTimeout(function(){e.removeClass(Wt.mobile.activeBtnClass)},300)},Wt.mobile.document.on("pagebeforechange",function(e,t){"popup"===t.options.role&&(Wt.mobile.popup.handleLink(t.options.link),e.preventDefault())}),Vt=".ui-disabled,.ui-state-disabled,.ui-li-divider,.ui-screen-hidden,:jqmData(role='placeholder')",(Kt=e).widget("mobile.selectmenu",Kt.mobile.selectmenu,{_create:function(){var e=this.options;return e.nativeMenu=e.nativeMenu||0<this.element.parents(":jqmData(role='popup'),:mobile-popup").length,this._super()},_handleSelectFocus:function(){this.element.trigger("blur"),this.button.trigger("focus")},_handleKeydown:function(e){this._super(e),this._handleButtonVclickKeydown(e)},_handleButtonVclickKeydown:function(e){this.options.disabled||this.isOpen||this.options.nativeMenu||"vclick"!==e.type&&(!e.keyCode||e.keyCode!==Kt.mobile.keyCode.ENTER&&e.keyCode!==Kt.mobile.keyCode.SPACE)||(this._decideFormat(),"overlay"===this.menuType?this.button.attr("href","#"+this.popupId).attr("data-"+(Kt.mobile.ns||"")+"rel","popup"):this.button.attr("href","#"+this.dialogId).attr("data-"+(Kt.mobile.ns||"")+"rel","dialog"),this.isOpen=!0)},_handleListFocus:function(e){var t="focusin"===e.type?{tabindex:"0",event:"vmouseover"}:{tabindex:"-1",event:"vmouseout"};Kt(e.target).attr("tabindex",t.tabindex).trigger(t.event)},_handleListKeydown:function(e){var t=Kt(e.target),i=t.closest("li");switch(e.keyCode){case 38:return Li(i,t,"prev"),!1;case 40:return Li(i,t,"next"),!1;case 13:case 32:return t.trigger("click"),!1}},_handleMenuPageHide:function(){this._delayedTrigger(),this.thisPage.page("bindRemove")},_handleHeaderCloseClick:function(){if("overlay"===this.menuType)return this.close(),!1},_handleListItemClick:function(e){var t=Kt(e.target).closest("li"),i=this.select[0].selectedIndex,s=Kt.mobile.getAttribute(t,"option-index"),n=this._selectOptions().eq(s)[0];n.selected=!this.isMultiple||!n.selected,this.isMultiple&&t.find("a").toggleClass("ui-checkbox-on",n.selected).toggleClass("ui-checkbox-off",!n.selected),this.isMultiple||i===s||(this._triggerChange=!0),this.isMultiple?(this.select.trigger("change"),this.list.find("li:not(.ui-li-divider)").eq(s).find("a").first().trigger("focus")):this.close(),e.preventDefault()},build:function(){var e,t,i,s,n,o,a,r,l,h,d,c,u,p=this.options;return p.nativeMenu?this._super():(t=(e=this.selectId)+"-listbox",i=e+"-dialog",s=this.label,n=this.element.closest(".ui-page"),o=this.element[0].multiple,a=e+"-menu",d=p.theme?" data-"+Kt.mobile.ns+"theme='"+p.theme+"'":"",h=(l=p.overlayTheme||p.theme||null)?" data-"+Kt.mobile.ns+"overlay-theme='"+l+"'":"",c=p.dividerTheme&&o?" data-"+Kt.mobile.ns+"divider-theme='"+p.dividerTheme+"'":"",r=Kt("<div data-"+Kt.mobile.ns+"role='dialog' class='ui-selectmenu' id='"+i+"'"+d+h+"><div data-"+Kt.mobile.ns+"role='header'><div class='ui-title'></div></div><div data-"+Kt.mobile.ns+"role='content'></div></div>"),l=Kt("<div"+d+h+" id='"+t+"' class='ui-selectmenu'></div>").insertAfter(this.select).popup(),h=Kt("<ul class='ui-selectmenu-list' id='"+a+"' role='listbox' aria-labelledby='"+this.buttonId+"'"+d+c+"></ul>").appendTo(l),d=Kt("<div class='ui-header ui-bar-"+(p.theme||"inherit")+"'></div>").prependTo(l),c=Kt("<h1 class='ui-title'></h1>").appendTo(d),this.isMultiple&&(u=Kt("<a>",{role:"button",text:p.closeText,href:"#",class:"ui-btn ui-corner-all ui-btn-left ui-btn-icon-notext ui-icon-delete"}).appendTo(d)),Kt.extend(this,{selectId:e,menuId:a,popupId:t,dialogId:i,thisPage:n,menuPage:r,label:s,isMultiple:o,theme:p.theme,listbox:l,list:h,header:d,headerTitle:c,headerClose:u,menuPageContent:void 0,menuPageClose:void 0,placeholder:""}),this.refresh(),void 0===this._origTabIndex&&(this._origTabIndex=null!==this.select[0].getAttribute("tabindex")&&this.select.attr("tabindex")),this.select.attr("tabindex","-1"),this._on(this.select,{focus:"_handleSelectFocus"}),this._on(this.button,{vclick:"_handleButtonVclickKeydown"}),this.list.attr("role","listbox"),this._on(this.list,{focusin:"_handleListFocus",focusout:"_handleListFocus",keydown:"_handleListKeydown","click li:not(.ui-disabled,.ui-state-disabled,.ui-li-divider)":"_handleListItemClick"}),this._on(this.menuPage,{pagehide:"_handleMenuPageHide"}),this._on(this.listbox,{popupafterclose:"_popupClosed"}),this.isMultiple&&this._on(this.headerClose,{click:"_handleHeaderCloseClick"}),this)},_popupClosed:function(){this.close(),this._delayedTrigger()},_delayedTrigger:function(){this._triggerChange&&this.element.trigger("change"),this._triggerChange=!1},_isRebuildRequired:function(){var e=this.list.find("li");return this._selectOptions().not(".ui-screen-hidden").text()!==e.text()},selected:function(){return this._selectOptions().filter(":selected:not( :jqmData(placeholder='true') )")},refresh:function(e){var i,s;if(this.options.nativeMenu)return this._super(e);i=this,(e||this._isRebuildRequired())&&i._buildList(),s=this.selectedIndices(),i.setButtonText(),i.setButtonCount(),i.list.find("li:not(.ui-li-divider)").find("a").removeClass(Kt.mobile.activeBtnClass).end().attr("aria-selected",!1).each(function(e){var t=Kt(this);-1<Kt.inArray(e,s)?(t.attr("aria-selected",!0),i.isMultiple?t.find("a").removeClass("ui-checkbox-off").addClass("ui-checkbox-on"):(t.hasClass("ui-screen-hidden")?t.next():t).find("a").addClass(Kt.mobile.activeBtnClass)):i.isMultiple&&t.find("a").removeClass("ui-checkbox-on").addClass("ui-checkbox-off")})},close:function(){var e;!this.options.disabled&&this.isOpen&&("page"===(e=this).menuType?(e.menuPage.dialog("close"),e.list.appendTo(e.listbox)):e.listbox.popup("close"),e._focusButton(),e.isOpen=!1)},open:function(){this.button.click()},_focusMenuItem:function(){var e=this.list.find("a."+Kt.mobile.activeBtnClass);(e=0===e.length?this.list.find("li:not("+Vt+") a.ui-btn"):e).first().trigger("focus")},_decideFormat:function(){var e=this,t=this.window,i=e.list.parent().outerHeight(),s=t.scrollTop(),n=e.button.offset().top,t=t.height();t-80<i||!Kt.support.scrollTop?(e.menuPage.appendTo(Kt.mobile.pageContainer).page(),e.menuPageContent=e.menuPage.find(".ui-content"),e.menuPageClose=e.menuPage.find(".ui-header a"),e.thisPage.off("pagehide.remove"),0===s&&t<n&&e.thisPage.one("pagehide",function(){Kt(this).jqmData("lastScroll",n)}),e.menuPage.one({pageshow:Kt.proxy(this,"_focusMenuItem"),pagehide:Kt.proxy(this,"close")}),e.menuType="page",e.menuPageContent.append(e.list),e.menuPage.find("div .ui-title").text(e.label.getEncodedText()||e.placeholder)):(e.menuType="overlay",e.listbox.one({popupafteropen:Kt.proxy(this,"_focusMenuItem")}))},_buildList:function(){var e,t,i,s,n,o,a,r,l,h,d,c,u=this.options,p=this.placeholder,m=!0,f="data-"+Kt.mobile.ns,g=f+"option-index",b=f+"icon",v=f+"role",_=f+"placeholder",C=E.createDocumentFragment(),w=!1;for(this.list.empty().filter(".ui-listview").listview("destroy"),t=(e=this._selectOptions()).length,i=this.select[0],n=0;n<t;n++,w=!1)o=e[n],(a=Kt(o)).hasClass("ui-screen-hidden")||(d=o.parentNode,l=[],c=a.text(),(r=E.createElement("a")).setAttribute("href","#"),r.appendChild(E.createTextNode(c)),d!==i&&"optgroup"===d.nodeName.toLowerCase()&&(h=d.getAttribute("label"))!==s&&((d=E.createElement("li")).setAttribute(v,"list-divider"),d.setAttribute("role","option"),d.setAttribute("tabindex","-1"),d.appendChild(E.createTextNode(h)),C.appendChild(d),s=h),!m||o.getAttribute("value")&&0!==c.length&&!a.jqmData("placeholder")||(w=!(m=!1),null===o.getAttribute(_)&&(this._removePlaceholderAttr=!0),o.setAttribute(_,!0),u.hidePlaceholderMenuItems&&l.push("ui-screen-hidden"),p!==c&&(p=this.placeholder=c)),c=E.createElement("li"),o.disabled&&(l.push("ui-state-disabled"),c.setAttribute("aria-disabled",!0)),c.setAttribute(g,n),c.setAttribute(b,"false"),w&&c.setAttribute(_,!0),c.className=l.join(" "),c.setAttribute("role","option"),r.setAttribute("tabindex","-1"),this.isMultiple&&Kt(r).addClass("ui-btn ui-checkbox-off ui-btn-icon-right"),c.appendChild(r),C.appendChild(c));this.list[0].appendChild(C),this.isMultiple||p.length?this.headerTitle.text(this.placeholder):this.header.addClass("ui-screen-hidden"),this.list.listview()},_button:function(){return this.options.nativeMenu?this._super():Kt("<a>",{href:"#",role:"button",id:this.buttonId,"aria-haspopup":"true","aria-owns":this.menuId})},_destroy:function(){this.options.nativeMenu||(this.close(),void 0!==this._origTabIndex&&(!1!==this._origTabIndex?this.select.attr("tabindex",this._origTabIndex):this.select.removeAttr("tabindex")),this._removePlaceholderAttr&&this._selectOptions().removeAttr("data-"+Kt.mobile.ns+"placeholder"),this.listbox.remove(),this.menuPage.remove()),this._super()}}),Xt={"ui-shadow":"shadow","ui-corner-all":"corners","ui-btn-inline":"inline","ui-shadow-icon":"iconshadow","ui-mini":"mini"},Zt=/[A-Z]/g,($t=e).fn.buttonMarkup=function(e,t){for(var i,s,n,o,a,r=$t.fn.buttonMarkup.defaults,l=0;l<this.length;l++){if(i=this[l],a=t?{alreadyEnhanced:!1,unknownClasses:[]}:function(e){var t,i,s,n=!1,o=!0,a={icon:"",inline:!1,shadow:!1,corners:!1,iconshadow:!1,mini:!1},r=[];for(e=e.split(" "),t=0;t<e.length;t++)s=!0,(i=Xt[e[t]])!==Gt?a[i]=!(s=!1):0===e[t].indexOf("ui-btn-icon-")?(o=s=!1,a.iconpos=e[t].substring(12)):0===e[t].indexOf("ui-icon-")?(s=!1,a.icon=e[t].substring(8)):0===e[t].indexOf("ui-btn-")&&8===e[t].length?(s=!1,a.theme=e[t].substring(7)):"ui-btn"===e[t]&&(n=!(s=!1)),s&&r.push(e[t]);return o&&(a.icon=""),{options:a,unknownClasses:r,alreadyEnhanced:n}}(i.className),s=$t.extend({},a.alreadyEnhanced?a.options:{},e),!a.alreadyEnhanced)for(n in r)s[n]===Gt&&(s[n]=function(){var e=$t.mobile.getAttribute.apply(this,arguments);return null==e?Gt:e}(i,n.replace(Zt,ji)));i.className=(o=$t.extend({},r,s),(a=(a=a.unknownClasses)||[]).push("ui-btn"),o.theme&&a.push("ui-btn-"+o.theme),o.icon&&(a=a.concat(["ui-icon-"+o.icon,"ui-btn-icon-"+o.iconpos]),o.iconshadow&&a.push("ui-shadow-icon")),o.inline&&a.push("ui-btn-inline"),o.shadow&&a.push("ui-shadow"),o.corners&&a.push("ui-corner-all"),o.mini&&a.push("ui-mini"),a.join(" ")),"button"!==i.tagName.toLowerCase()&&i.setAttribute("role","button")}return this},$t.fn.buttonMarkup.defaults={icon:"",iconpos:"left",theme:null,inline:!1,shadow:!0,corners:!0,iconshadow:!1,mini:!1},$t.extend($t.fn.buttonMarkup,{initSelector:"a:jqmData(role='button'), .ui-bar > a, .ui-bar > :jqmData(role='controlgroup') > a, button:not(:jqmData(role='navbar') button)"}),(Yt=e).widget("mobile.controlgroup",Yt.extend({options:{enhanced:!1,theme:null,shadow:!1,corners:!0,excludeInvisible:!0,type:"vertical",mini:!1},_create:function(){var e=this.element,t=this.options,i=Yt.mobile.page.prototype.keepNativeSelector();Yt.fn.buttonMarkup&&this.element.find(Yt.fn.buttonMarkup.initSelector).not(i).buttonMarkup(),Yt.each(this._childWidgets,Yt.proxy(function(e,t){Yt.mobile[t]&&this.element.find(Yt.mobile[t].initSelector).not(i)[t]()},this)),Yt.extend(this,{_ui:null,_initialRefresh:!0}),t.enhanced?this._ui={groupLegend:e.children(".ui-controlgroup-label").children(),childWrapper:e.children(".ui-controlgroup-controls")}:this._ui=this._enhance()},_childWidgets:["checkboxradio","selectmenu","button"],_themeClassFromOption:function(e){return!e||"none"===e?"":"ui-group-theme-"+e},_enhance:function(){var e=this.element,t=this.options,t={groupLegend:e.children("legend"),childWrapper:e.addClass("ui-controlgroup ui-controlgroup-"+("horizontal"===t.type?"horizontal":"vertical")+" "+this._themeClassFromOption(t.theme)+" "+(t.corners?"ui-corner-all ":"")+(t.mini?"ui-mini ":"")).wrapInner("<div class='ui-controlgroup-controls "+(!0===t.shadow?"ui-shadow":"")+"'></div>").children()};return 0<t.groupLegend.length&&Yt("<div role='heading' class='ui-controlgroup-label'></div>").append(t.groupLegend).prependTo(e),t},_init:function(){this.refresh()},_setOptions:function(e){var t,i=this.element;return e.type!==Qt&&(i.removeClass("ui-controlgroup-horizontal ui-controlgroup-vertical").addClass("ui-controlgroup-"+("horizontal"===e.type?"horizontal":"vertical")),t=!0),e.theme!==Qt&&i.removeClass(this._themeClassFromOption(this.options.theme)).addClass(this._themeClassFromOption(e.theme)),e.corners!==Qt&&i.toggleClass("ui-corner-all",e.corners),e.mini!==Qt&&i.toggleClass("ui-mini",e.mini),e.shadow!==Qt&&this._ui.childWrapper.toggleClass("ui-shadow",e.shadow),e.excludeInvisible!==Qt&&(this.options.excludeInvisible=e.excludeInvisible,t=!0),e=this._super(e),t&&this.refresh(),e},container:function(){return this._ui.childWrapper},refresh:function(){var e=this.container(),t=e.find(".ui-btn").not(".ui-slider-handle"),i=this._initialRefresh;Yt.mobile.checkboxradio&&e.find(":mobile-checkboxradio").checkboxradio("refresh"),this._addFirstLastClasses(t,this.options.excludeInvisible?this._getVisibles(t,i):t,i),this._initialRefresh=!1},_destroy:function(){var e,t=this.options;if(t.enhanced)return this;e=this._ui,t=this.element.removeClass("ui-controlgroup ui-controlgroup-horizontal ui-controlgroup-vertical ui-corner-all ui-mini "+this._themeClassFromOption(t.theme)).find(".ui-btn").not(".ui-slider-handle"),this._removeFirstLastClasses(t),e.groupLegend.unwrap(),e.childWrapper.children().unwrap()}},Yt.mobile.behaviors.addFirstLastClasses)),(Jt=e).widget("mobile.toolbar",{initSelector:":jqmData(role='footer'), :jqmData(role='header')",options:{theme:null,addBackBtn:!1,backBtnTheme:null,backBtnText:"Back"},_create:function(){var e=this.element.is(":jqmData(role='header')")?"header":"footer",t=this.element.closest(".ui-page");0===t.length&&(t=!1,this._on(this.document,{pageshow:"refresh"})),Jt.extend(this,{role:e,page:t,leftbtn:void 0,rightbtn:void 0}),this.element.attr("role","header"==e?"banner":"contentinfo").addClass("ui-"+e),this.refresh(),this._setOptions(this.options)},_setOptions:function(e){var t,i;e.addBackBtn!==ei&&this._updateBackButton(),null!=e.backBtnTheme&&this.element.find(".ui-toolbar-back-btn").addClass("ui-btn ui-btn-"+e.backBtnTheme),e.backBtnText!==ei&&this.element.find(".ui-toolbar-back-btn .ui-btn-text").text(e.backBtnText),e.theme!==ei&&(t=this.options.theme||"inherit",i=e.theme||"inherit",this.element.removeClass("ui-bar-"+t).addClass("ui-bar-"+i)),this._super(e)},refresh:function(){"header"===this.role&&this._addHeaderButtonClasses(),this.page||(this._setRelative(),"footer"===this.role?this.element.appendTo("body"):"header"===this.role&&this._updateBackButton()),this._addHeadingClasses(),this._btnMarkup()},_setRelative:function(){Jt("[data-"+Jt.mobile.ns+"role='page']").css({position:"relative"})},_btnMarkup:function(){this.element.children("a").filter(":not([data-"+Jt.mobile.ns+"role='none'])").attr("data-"+Jt.mobile.ns+"role","button"),this.element.trigger("create")},_addHeaderButtonClasses:function(){var e=this.element.children("a, button");this.leftbtn=e.hasClass("ui-btn-left")&&!e.hasClass("ui-toolbar-back-btn"),this.rightbtn=e.hasClass("ui-btn-right"),this.leftbtn=this.leftbtn||e.eq(0).not(".ui-btn-right,.ui-toolbar-back-btn").addClass("ui-btn-left").length,this.rightbtn=this.rightbtn||e.eq(1).addClass("ui-btn-right").length},_updateBackButton:function(){var e=this.options,t=e.backBtnTheme||e.theme,i=this._backButton=this._backButton||{};this.options.addBackBtn&&"header"===this.role&&1<Jt(".ui-page").length&&(this.page?this.page[0].getAttribute("data-"+Jt.mobile.ns+"url")!==Jt.mobile.path.stripHash(location.hash):Jt.mobile.navigate&&Jt.mobile.navigate.history&&0<Jt.mobile.navigate.history.activeIndex)&&!this.leftbtn?i.attached||(this.backButton=i.element=(i.element||Jt("<a role='button' href='javascript:void(0);' class='ui-btn ui-corner-all ui-shadow ui-btn-left "+(t?"ui-btn-"+t+" ":"")+"ui-toolbar-back-btn ui-icon-carat-l ui-btn-icon-left' data-"+Jt.mobile.ns+"rel='back'>"+e.backBtnText+"</a>")).prependTo(this.element),i.attached=!0):i.element&&(i.element.detach(),i.attached=!1)},_addHeadingClasses:function(){this.element.children("h1, h2, h3, h4, h5, h6").addClass("ui-title").attr({role:"heading","aria-level":"1"})},_destroy:function(){var e;this.element.children("h1, h2, h3, h4, h5, h6").removeClass("ui-title").removeAttr("role").removeAttr("aria-level"),"header"===this.role&&(this.element.children("a, button").removeClass("ui-btn-left ui-btn-right ui-btn ui-shadow ui-corner-all"),this.backButton&&this.backButton.remove()),e=this.options.theme||"inherit",this.element.removeClass("ui-bar-"+e),this.element.removeClass("ui-"+this.role).removeAttr("role")}}),(ti=e).widget("mobile.toolbar",ti.mobile.toolbar,{options:{position:null,visibleOnPageShow:!0,disablePageZoom:!0,transition:"slide",fullscreen:!1,tapToggle:!0,tapToggleBlacklist:"a, button, input, select, textarea, .ui-header-fixed, .ui-footer-fixed, .ui-flipswitch, .ui-popup, .ui-panel, .ui-panel-dismiss-open",hideDuringFocus:"input, textarea, select",updatePagePadding:!0,trackPersistentToolbars:!0,supportBlacklist:function(){return!ti.support.fixedPosition}},_create:function(){this._super(),this.pagecontainer=ti(":mobile-pagecontainer"),"fixed"!==this.options.position||this.options.supportBlacklist()||this._makeFixed()},_makeFixed:function(){this.element.addClass("ui-"+this.role+"-fixed"),this.updatePagePadding(),this._addTransitionClass(),this._bindPageEvents(),this._bindToggleHandlers()},_setOptions:function(e){var t;"fixed"===e.position&&"fixed"!==this.options.position&&this._makeFixed(),"fixed"!==this.options.position||this.options.supportBlacklist()||(t=this.page||(0<ti(".ui-page-active").length?ti(".ui-page-active"):ti(".ui-page").eq(0)),void 0!==e.fullscreen&&(e.fullscreen?(this.element.addClass("ui-"+this.role+"-fullscreen"),t.addClass("ui-page-"+this.role+"-fullscreen")):(this.element.removeClass("ui-"+this.role+"-fullscreen"),t.removeClass("ui-page-"+this.role+"-fullscreen").addClass("ui-page-"+this.role+"-fixed")))),this._super(e)},_addTransitionClass:function(){var e=this.options.transition;e&&"none"!==e&&("slide"===e&&(e=this.element.hasClass("ui-header")?"slidedown":"slideup"),this.element.addClass(e))},_bindPageEvents:function(){var e=this.page?this.element.closest(".ui-page"):this.document;this._on(e,{pagebeforeshow:"_handlePageBeforeShow",webkitAnimationStart:"_handleAnimationStart",animationstart:"_handleAnimationStart",updatelayout:"_handleAnimationStart",pageshow:"_handlePageShow",pagebeforehide:"_handlePageBeforeHide"})},_handlePageBeforeShow:function(){var e=this.options;e.disablePageZoom&&ti.mobile.zoom.disable(!0),e.visibleOnPageShow||this.hide(!0)},_handleAnimationStart:function(){this.options.updatePagePadding&&this.updatePagePadding(this.page||".ui-page-active")},_handlePageShow:function(){this.updatePagePadding(this.page||".ui-page-active"),this.options.updatePagePadding&&this._on(this.window,{throttledresize:"updatePagePadding"})},_handlePageBeforeHide:function(e,t){var i,s,n,o=this.options;o.disablePageZoom&&ti.mobile.zoom.enable(!0),o.updatePagePadding&&this._off(this.window,"throttledresize"),o.trackPersistentToolbars&&(i=ti(".ui-footer-fixed:jqmData(id)",this.page),o=ti(".ui-header-fixed:jqmData(id)",this.page),s=i.length&&t.nextPage&&ti(".ui-footer-fixed:jqmData(id='"+i.jqmData("id")+"')",t.nextPage)||ti(),n=o.length&&t.nextPage&&ti(".ui-header-fixed:jqmData(id='"+o.jqmData("id")+"')",t.nextPage)||ti(),(s.length||n.length)&&(s.add(n).appendTo(ti.mobile.pageContainer),t.nextPage.one("pageshow",function(){n.prependTo(this),s.appendTo(this)})))},_visible:!0,updatePagePadding:function(e){var t=this.element,i="header"===this.role,s=parseFloat(t.css(i?"top":"bottom"));this.options.fullscreen||(e=e&&void 0===e.type&&e||this.page||t.closest(".ui-page"),e=this.page||".ui-page-active",ti(e).css("padding-"+(i?"top":"bottom"),t.outerHeight()+s))},_useTransition:function(e){var t=this.window,i=this.element,s=t.scrollTop(),n=i.height(),t=(this.page?i.closest(".ui-page"):ti(".ui-page-active")).height(),i=ti.mobile.getScreenHeight();return!e&&(this.options.transition&&"none"!==this.options.transition&&("header"===this.role&&!this.options.fullscreen&&n<s||"footer"===this.role&&!this.options.fullscreen&&s+i<t-n)||this.options.fullscreen)},show:function(e){var t="ui-fixed-hidden",i=this.element;this._useTransition(e)?i.removeClass("out "+t).addClass("in").animationComplete(function(){i.removeClass("in")}):i.removeClass(t),this._visible=!0},hide:function(e){var t="ui-fixed-hidden",i=this.element,s="out"+("slide"===this.options.transition?" reverse":"");this._useTransition(e)?i.addClass(s).removeClass("in").animationComplete(function(){i.addClass(t).removeClass(s)}):i.addClass(t).removeClass(s),this._visible=!1},toggle:function(){this[this._visible?"hide":"show"]()},_bindToggleHandlers:function(){var t,i,s=this,n=s.options,o=!0;(this.page||ti(".ui-page")).on("vclick",function(e){n.tapToggle&&!ti(e.target).closest(n.tapToggleBlacklist).length&&s.toggle()}).on("focusin focusout",function(e){screen.width<1025&&ti(e.target).is(n.hideDuringFocus)&&!ti(e.target).closest(".ui-header-fixed, .ui-footer-fixed").length&&("focusout"!==e.type||o?"focusin"===e.type&&o&&(clearTimeout(t),o=!1,i=setTimeout(function(){s.hide()},0)):(o=!0,clearTimeout(i),t=setTimeout(function(){s.show()},0)))})},_setRelative:function(){"fixed"!==this.options.position&&ti("[data-"+ti.mobile.ns+"role='page']").css({position:"relative"})},_destroy:function(){var e,t,i,s=this.pagecontainer.pagecontainer("getActivePage");this._super(),"fixed"===this.options.position&&(t=0<ti("body>.ui-"+this.role+"-fixed").add(s.find(".ui-"+this.options.role+"-fixed")).not(this.element).length,i=0<ti("body>.ui-"+this.role+"-fixed").add(s.find(".ui-"+this.options.role+"-fullscreen")).not(this.element).length,this.element.removeClass("ui-header-fixed ui-footer-fixed ui-header-fullscreen in out ui-footer-fullscreen fade slidedown slideup ui-fixed-hidden"),i||(e="ui-page-"+this.role+"-fullscreen"),t||(t="header"===this.role,e+=" ui-page-"+this.role+"-fixed",s.css("padding-"+(t?"top":"bottom"),"")),s.removeClass(e))}}),(ii=e).widget("mobile.toolbar",ii.mobile.toolbar,{_makeFixed:function(){this._super(),this._workarounds()},_workarounds:function(){var e=navigator.userAgent,t=navigator.platform,i=e.match(/AppleWebKit\/([0-9]+)/),s=!!i&&i[1],i=null;if(-1<t.indexOf("iPhone")||-1<t.indexOf("iPad")||-1<t.indexOf("iPod"))i="ios";else{if(!(-1<e.indexOf("Android")))return;i="android"}"ios"===i?this._bindScrollWorkaround():"android"===i&&s&&s<534&&(this._bindScrollWorkaround(),this._bindListThumbWorkaround())},_viewportOffset:function(){var e=this.element,t=e.hasClass("ui-header"),i=Math.abs(e.offset().top-this.window.scrollTop());return i=!t?Math.round(i-this.window.height()+e.outerHeight())-60:i},_bindScrollWorkaround:function(){var e=this;this._on(this.window,{scrollstop:function(){2<e._viewportOffset()&&e._visible&&e._triggerRedraw()}})},_bindListThumbWorkaround:function(){this.element.closest(".ui-page").addClass("ui-android-2x-fixed")},_triggerRedraw:function(){var e=parseFloat(ii(".ui-page-active").css("padding-bottom"));ii(".ui-page-active").css("padding-bottom",e+1+"px"),setTimeout(function(){ii(".ui-page-active").css("padding-bottom",e+"px")},0)},destroy:function(){this._super(),this.element.closest(".ui-page-active").removeClass("ui-android-2x-fix")}}),oi=(si=e).mobile.browser.oldIE&&si.mobile.browser.oldIE<=8,ai=si("<div class='ui-popup-arrow-guide'></div><div class='ui-popup-arrow-container"+(oi?" ie":"")+"'><div class='ui-popup-arrow'></div></div>"),si.widget("mobile.popup",si.mobile.popup,{options:{arrow:""},_create:function(){var e=this._super();return this.options.arrow&&(this._ui.arrow=this._addArrow()),e},_addArrow:function(){var e,t,i=this.options,t=(e=(s=ai.clone()).eq(0),s=(t=s.eq(1)).children(),{arEls:t.add(e),gd:e,ct:t,ar:s}),s=this._themeClassFromOption("ui-body-",i.theme);return t.ar.addClass(s+(i.shadow?" ui-overlay-shadow":"")),t.arEls.hide().appendTo(this.element),t},_unenhance:function(){var e=this._ui.arrow;return e&&e.arEls.remove(),this._super()},_tryAnArrow:function(e,t,i,s,n){var o,a={},r={};return s.arFull[e.dimKey]>s.guideDims[e.dimKey]||(a[e.fst]=i[e.fst]+(s.arHalf[e.oDimKey]+s.menuHalf[e.oDimKey])*e.offsetFactor-s.contentBox[e.fst]+(s.clampInfo.menuSize[e.oDimKey]-s.contentBox[e.oDimKey])*e.arrowOffsetFactor,a[e.snd]=i[e.snd],a={x:(o=s.result||this._calculateFinalLocation(a,s.clampInfo)).left,y:o.top},r[e.fst]=a[e.fst]+s.contentBox[e.fst]+e.tipOffset,r[e.snd]=Math.max(o[e.prop]+s.guideOffset[e.prop]+s.arHalf[e.dimKey],Math.min(o[e.prop]+s.guideOffset[e.prop]+s.guideDims[e.dimKey]-s.arHalf[e.dimKey],i[e.snd])),i=Math.abs(i.x-r.x)+Math.abs(i.y-r.y),(!n||i<n.diff)&&(r[e.snd]-=s.arHalf[e.dimKey]+o[e.prop]+s.contentBox[e.snd],n={dir:t,diff:i,result:o,posProp:e.prop,posVal:r[e.snd]})),n},_getPlacementState:function(e){var t=this._ui.arrow,i={clampInfo:this._clampPopupWidth(!e),arFull:{cx:t.ct.width(),cy:t.ct.height()},guideDims:{cx:t.gd.width(),cy:t.gd.height()},guideOffset:t.gd.offset()},s=this.element.offset();return t.gd.css({left:0,top:0,right:0,bottom:0}),e=t.gd.offset(),i.contentBox={x:e.left-s.left,y:e.top-s.top,cx:t.gd.width(),cy:t.gd.height()},t.gd.removeAttr("style"),i.guideOffset={left:i.guideOffset.left-s.left,top:i.guideOffset.top-s.top},i.arHalf={cx:i.arFull.cx/2,cy:i.arFull.cy/2},i.menuHalf={cx:i.clampInfo.menuSize.cx/2,cy:i.clampInfo.menuSize.cy/2},i},_placementCoords:function(i){var s,n,o,e,t=this.options.arrow,a=this._ui.arrow;return a?(a.arEls.show(),e={},s=this._getPlacementState(!0),o={l:{fst:"x",snd:"y",prop:"top",dimKey:"cy",oDimKey:"cx",offsetFactor:1,tipOffset:-s.arHalf.cx,arrowOffsetFactor:0},r:{fst:"x",snd:"y",prop:"top",dimKey:"cy",oDimKey:"cx",offsetFactor:-1,tipOffset:s.arHalf.cx+s.contentBox.cx,arrowOffsetFactor:1},b:{fst:"y",snd:"x",prop:"left",dimKey:"cx",oDimKey:"cy",offsetFactor:-1,tipOffset:s.arHalf.cy+s.contentBox.cy,arrowOffsetFactor:1},t:{fst:"y",snd:"x",prop:"left",dimKey:"cx",oDimKey:"cy",offsetFactor:1,tipOffset:-s.arHalf.cy,arrowOffsetFactor:0}},si.each((!0===t?"l,t,r,b":t).split(","),si.proxy(function(e,t){n=this._tryAnArrow(o[t],t,i,s,n)},this)),n?(a.ct.removeClass("ui-popup-arrow-l ui-popup-arrow-t ui-popup-arrow-r ui-popup-arrow-b").addClass("ui-popup-arrow-"+n.dir).removeAttr("style").css(n.posProp,n.posVal).show(),oi||(t=this.element.offset(),e[o[n.dir].fst]=a.ct.offset(),e[o[n.dir].snd]={left:t.left+s.contentBox.x,top:t.top+s.contentBox.y}),n.result):(a.arEls.hide(),this._super(i))):this._super(i)},_setOptions:function(e){var t,i=this.options.theme,s=this._ui.arrow,n=this._super(e);if(e.arrow!==ni){if(!s&&e.arrow)return void(this._ui.arrow=this._addArrow());s&&!e.arrow&&(s.arEls.remove(),this._ui.arrow=null)}return(s=this._ui.arrow)&&(e.theme!==ni&&(i=this._themeClassFromOption("ui-body-",i),t=this._themeClassFromOption("ui-body-",e.theme),s.ar.removeClass(i).addClass(t)),e.shadow!==ni&&s.ar.toggleClass("ui-overlay-shadow",e.shadow)),n},_destroy:function(){var e=this._ui.arrow;return e&&e.arEls.remove(),this._super()}}),(ri=e).widget("mobile.panel",{options:{classes:{panel:"ui-panel",panelOpen:"ui-panel-open",panelClosed:"ui-panel-closed",panelFixed:"ui-panel-fixed",panelInner:"ui-panel-inner",modal:"ui-panel-dismiss",modalOpen:"ui-panel-dismiss-open",pageContainer:"ui-panel-page-container",pageWrapper:"ui-panel-wrapper",pageFixedToolbar:"ui-panel-fixed-toolbar",pageContentPrefix:"ui-panel-page-content",animate:"ui-panel-animate"},animate:!0,theme:null,position:"left",dismissible:!0,display:"reveal",swipeClose:!0,positionFixed:!1},_closeLink:null,_parentPage:null,_page:null,_modal:null,_panelInner:null,_wrapper:null,_fixedToolbars:null,_create:function(){var e=this.element,t=e.closest(".ui-page, :jqmData(role='page')");ri.extend(this,{_closeLink:e.find(":jqmData(rel='close')"),_parentPage:0<t.length&&t,_openedPage:null,_page:this._getPage,_panelInner:this._getPanelInner(),_fixedToolbars:this._getFixedToolbars}),"overlay"!==this.options.display&&this._getWrapper(),this._addPanelClasses(),ri.support.cssTransform3d&&this.options.animate&&this.element.addClass(this.options.classes.animate),this._bindUpdateLayout(),this._bindCloseEvents(),this._bindLinkListeners(),this._bindPageEvents(),this.options.dismissible&&this._createModal(),this._bindSwipeEvents()},_getPanelInner:function(){var e=this.element.find("."+this.options.classes.panelInner);return e=0===e.length?this.element.children().wrapAll("<div class='"+this.options.classes.panelInner+"' />").parent():e},_createModal:function(){var e=this,t=(e._parentPage||e.element).parent();e._modal=ri("<div class='"+e.options.classes.modal+"'></div>").on("mousedown",function(){e.close()}).appendTo(t)},_getPage:function(){return this._openedPage||this._parentPage||ri("."+ri.mobile.activePageClass)},_getWrapper:function(){var e=this._page().find("."+this.options.classes.pageWrapper);0===e.length&&(e=this._page().children(".ui-header:not(.ui-header-fixed), .ui-content:not(.ui-popup), .ui-footer:not(.ui-footer-fixed)").wrapAll("<div class='"+this.options.classes.pageWrapper+"'></div>").parent()),this._wrapper=e},_getFixedToolbars:function(){var e=ri("body").children(".ui-header-fixed, .ui-footer-fixed"),t=this._page().find(".ui-header-fixed, .ui-footer-fixed");return e.add(t).addClass(this.options.classes.pageFixedToolbar)},_getPosDisplayClasses:function(e){return e+"-position-"+this.options.position+" "+e+"-display-"+this.options.display},_getPanelClasses:function(){var e=this.options.classes.panel+" "+this._getPosDisplayClasses(this.options.classes.panel)+" "+this.options.classes.panelClosed+" ui-body-"+(this.options.theme||"inherit");return this.options.positionFixed&&(e+=" "+this.options.classes.panelFixed),e},_addPanelClasses:function(){this.element.addClass(this._getPanelClasses())},_handleCloseClick:function(e){e.isDefaultPrevented()||this.close()},_bindCloseEvents:function(){this._on(this._closeLink,{click:"_handleCloseClick"}),this._on({"click a:jqmData(ajax='false')":"_handleCloseClick"})},_positionPanel:function(e){var t=this._panelInner.outerHeight(),i=t>ri.mobile.getScreenHeight();i||!this.options.positionFixed?(i&&(this._unfixPanel(),ri.mobile.resetActivePageHeight(t)),e&&this.window[0].scrollTo(0,ri.mobile.defaultHomeScroll)):this._fixPanel()},_bindFixListener:function(){this._on(ri(D),{throttledresize:"_positionPanel"})},_unbindFixListener:function(){this._off(ri(D),"throttledresize")},_unfixPanel:function(){this.options.positionFixed&&ri.support.fixedPosition&&this.element.removeClass(this.options.classes.panelFixed)},_fixPanel:function(){this.options.positionFixed&&ri.support.fixedPosition&&this.element.addClass(this.options.classes.panelFixed)},_bindUpdateLayout:function(){var e=this;e.element.on("updatelayout",function(){e._open&&e._positionPanel()})},_bindLinkListeners:function(){this._on("body",{"click a":"_handleClick"})},_handleClick:function(e){var t,i=this.element.attr("id");e.currentTarget.href.split("#")[1]===i&&void 0!==i&&(e.preventDefault(),(t=ri(e.target)).hasClass("ui-btn")&&(t.addClass(ri.mobile.activeBtnClass),this.element.one("panelopen panelclose",function(){t.removeClass(ri.mobile.activeBtnClass)})),this.toggle())},_bindSwipeEvents:function(){var e=this,t=e._modal?e.element.add(e._modal):e.element;e.options.swipeClose&&("left"===e.options.position?t.on("swipeleft.panel",function(){e.close()}):t.on("swiperight.panel",function(){e.close()}))},_bindPageEvents:function(){var t=this;this.document.on("panelbeforeopen",function(e){t._open&&e.target!==t.element[0]&&t.close()}).on("keyup.panel",function(e){27===e.keyCode&&t._open&&t.close()}),this._parentPage||"overlay"===this.options.display||this._on(this.document,{pageshow:function(){this._openedPage=null,this._getWrapper()}}),t._parentPage?this.document.on("pagehide",":jqmData(role='page')",function(){t._open&&t.close(!0)}):this.document.on("pagebeforehide",function(){t._open&&t.close(!0)})},_open:!1,_pageContentOpenClasses:null,_modalOpenClasses:null,open:function(e){var t,i,s,n;this._open||(i=(t=this).options,s=function(){t._off(t.document,"panelclose"),t._page().jqmData("panel","open"),ri.support.cssTransform3d&&i.animate&&"overlay"!==i.display&&(t._wrapper.addClass(i.classes.animate),t._fixedToolbars().addClass(i.classes.animate)),!e&&ri.support.cssTransform3d&&i.animate?(t._wrapper||t.element).animationComplete(n,"transition"):setTimeout(n,0),i.theme&&"overlay"!==i.display&&t._page().parent().addClass(i.classes.pageContainer+"-themed "+i.classes.pageContainer+"-"+i.theme),t.element.removeClass(i.classes.panelClosed).addClass(i.classes.panelOpen),t._positionPanel(!0),t._pageContentOpenClasses=t._getPosDisplayClasses(i.classes.pageContentPrefix),"overlay"!==i.display&&(t._page().parent().addClass(i.classes.pageContainer),t._wrapper.addClass(t._pageContentOpenClasses),t._fixedToolbars().addClass(t._pageContentOpenClasses)),t._modalOpenClasses=t._getPosDisplayClasses(i.classes.modal)+" "+i.classes.modalOpen,t._modal&&t._modal.addClass(t._modalOpenClasses).height(Math.max(t._modal.height(),t.document.height()))},n=function(){t._open&&("overlay"!==i.display&&(t._wrapper.addClass(i.classes.pageContentPrefix+"-open"),t._fixedToolbars().addClass(i.classes.pageContentPrefix+"-open")),t._bindFixListener(),t._trigger("open"),t._openedPage=t._page())},t._trigger("beforeopen"),"open"===t._page().jqmData("panel")?t._on(t.document,{panelclose:s}):s(),t._open=!0)},close:function(e){var t,i,s;this._open&&(i=(t=this).options,s=function(){i.theme&&"overlay"!==i.display&&t._page().parent().removeClass(i.classes.pageContainer+"-themed "+i.classes.pageContainer+"-"+i.theme),t.element.addClass(i.classes.panelClosed),"overlay"!==i.display&&(t._page().parent().removeClass(i.classes.pageContainer),t._wrapper.removeClass(i.classes.pageContentPrefix+"-open"),t._fixedToolbars().removeClass(i.classes.pageContentPrefix+"-open")),ri.support.cssTransform3d&&i.animate&&"overlay"!==i.display&&(t._wrapper.removeClass(i.classes.animate),t._fixedToolbars().removeClass(i.classes.animate)),t._fixPanel(),t._unbindFixListener(),ri.mobile.resetActivePageHeight(),t._page().jqmRemoveData("panel"),t._trigger("close"),t._openedPage=null},t._trigger("beforeclose"),t.element.removeClass(i.classes.panelOpen),"overlay"!==i.display&&(t._wrapper.removeClass(t._pageContentOpenClasses),t._fixedToolbars().removeClass(t._pageContentOpenClasses)),!e&&ri.support.cssTransform3d&&i.animate?(t._wrapper||t.element).animationComplete(s,"transition"):setTimeout(s,0),t._modal&&t._modal.removeClass(t._modalOpenClasses).height(""),t._open=!1)},toggle:function(){this[this._open?"close":"open"]()},_destroy:function(){var e=this.options,t=1<ri("body > :mobile-panel").length+ri.mobile.activePage.find(":mobile-panel").length;"overlay"!==e.display&&(0===ri("body > :mobile-panel").add(ri.mobile.activePage.find(":mobile-panel")).not(".ui-panel-display-overlay").not(this.element).length&&this._wrapper.children().unwrap(),this._open&&(this._fixedToolbars().removeClass(e.classes.pageContentPrefix+"-open"),ri.support.cssTransform3d&&e.animate&&this._fixedToolbars().removeClass(e.classes.animate),this._page().parent().removeClass(e.classes.pageContainer),e.theme&&this._page().parent().removeClass(e.classes.pageContainer+"-themed "+e.classes.pageContainer+"-"+e.theme))),t||this.document.off("panelopen panelclose"),this._open&&this._page().jqmRemoveData("panel"),this._panelInner.children().unwrap(),this.element.removeClass([this._getPanelClasses(),e.classes.panelOpen,e.classes.animate].join(" ")).off("swipeleft.panel swiperight.panel").off("panelbeforeopen").off("panelhide").off("keyup.panel").off("updatelayout"),this._modal&&this._modal.remove()}}),(li=e).widget("mobile.table",{options:{classes:{table:"ui-table"},enhanced:!1},_create:function(){this.options.enhanced||this.element.addClass(this.options.classes.table),li.extend(this,{headers:void 0,allHeaders:void 0}),this._refresh(!0)},_setHeaders:function(){var e=this.element.find("thead tr");this.headers=this.element.find("tr:eq(0)").children(),this.allHeaders=this.headers.add(e.children())},refresh:function(){this._refresh()},rebuild:li.noop,_refresh:function(){var n=this.element,o=n.find("thead tr");this._setHeaders(),o.each(function(){var s=0;li(this).children().each(function(){var e,t=parseInt(this.getAttribute("colspan"),10),i=":nth-child("+(s+1)+")";if(this.setAttribute("data-"+li.mobile.ns+"colstart",s+1),t)for(e=0;e<t-1;e++)i+=", :nth-child("+(++s+1)+")";li(this).jqmData("cells",n.find("tr").not(o.eq(0)).not(this).children(i)),s++})})}}),(hi=e).widget("mobile.table",hi.mobile.table,{options:{mode:"columntoggle",columnBtnTheme:null,columnPopupTheme:null,columnBtnText:"Columns...",classes:hi.extend(hi.mobile.table.prototype.options.classes,{popup:"ui-table-columntoggle-popup",columnBtn:"ui-table-columntoggle-btn",priorityPrefix:"ui-table-priority-",columnToggleTable:"ui-table-columntoggle"})},_create:function(){this._super(),"columntoggle"===this.options.mode&&(hi.extend(this,{_menu:null}),this.options.enhanced?(this._menu=hi(this.document[0].getElementById(this._id()+"-popup")).children().first(),this._addToggles(this._menu,!0)):(this._menu=this._enhanceColToggle(),this.element.addClass(this.options.classes.columnToggleTable)),this._setupEvents(),this._setToggleState())},_id:function(){return this.element.attr("id")||this.widgetName+this.uuid},_setupEvents:function(){this._on(this.window,{throttledresize:"_setToggleState"}),this._on(this._menu,{"change input":"_menuInputChange"})},_addToggles:function(e,s){var n,o=0,a=this.options,r=e.controlgroup("container");s?n=e.find("input"):r.empty(),this.headers.not("td").each(function(){var e,t=hi(this),i=hi.mobile.getAttribute(this,"priority");i&&((e=t.add(t.jqmData("cells"))).addClass(a.classes.priorityPrefix+i),e=(s?n.eq(o++):hi("<label><input type='checkbox' checked />"+(t.children("abbr").first().attr("title")||t.text())+"</label>").appendTo(r).children(0).checkboxradio({theme:a.columnPopupTheme})).jqmData("header",t).jqmData("cells",e),t.jqmData("input",e))}),s||e.controlgroup("refresh")},_menuInputChange:function(e){var t=hi(e.target),e=t[0].checked;t.jqmData("cells").toggleClass("ui-table-cell-hidden",!e).toggleClass("ui-table-cell-visible",e)},_unlockCells:function(e){e.removeClass("ui-table-cell-hidden ui-table-cell-visible")},_enhanceColToggle:function(){var e=this.element,t=this.options,i=hi.mobile.ns,s=this.document[0].createDocumentFragment(),n=this._id()+"-popup",i=hi("<a href='#"+n+"' class='"+t.classes.columnBtn+" ui-btn ui-btn-"+(t.columnBtnTheme||"a")+" ui-corner-all ui-shadow ui-mini' data-"+i+"rel='popup'>"+t.columnBtnText+"</a>"),t=hi("<div class='"+t.classes.popup+"' id='"+n+"'></div>"),n=hi("<fieldset></fieldset>").controlgroup();return this._addToggles(n,!1),n.appendTo(t),s.appendChild(t[0]),s.appendChild(i[0]),e.before(s),t.popup(),n},rebuild:function(){this._super(),"columntoggle"===this.options.mode&&this._refresh(!1)},_refresh:function(e){var i,s,t;if(this._super(e),!e&&"columntoggle"===this.options.mode)for(i=this.headers,s=[],this._menu.find("input").each(function(){var e=hi(this),t=e.jqmData("header"),t=i.index(t[0]);-1<t&&!e.prop("checked")&&s.push(t)}),this._unlockCells(this.element.find(".ui-table-cell-hidden, .ui-table-cell-visible")),this._addToggles(this._menu,e),t=s.length-1;-1<t;t--)i.eq(s[t]).jqmData("input").prop("checked",!1).checkboxradio("refresh").trigger("change")},_setToggleState:function(){this._menu.find("input").each(function(){var e=hi(this);this.checked="table-cell"===e.jqmData("cells").eq(0).css("display"),e.checkboxradio("refresh")})},_destroy:function(){this._super()}}),(di=e).widget("mobile.table",di.mobile.table,{options:{mode:"reflow",classes:di.extend(di.mobile.table.prototype.options.classes,{reflowTable:"ui-table-reflow",cellLabels:"ui-table-cell-label"})},_create:function(){this._super(),"reflow"===this.options.mode&&(this.options.enhanced||(this.element.addClass(this.options.classes.reflowTable),this._updateReflow()))},rebuild:function(){this._super(),"reflow"===this.options.mode&&this._refresh(!1)},_refresh:function(e){this._super(e),e||"reflow"!==this.options.mode||this._updateReflow()},_updateReflow:function(){var a=this,r=this.options;di(a.allHeaders.get().reverse()).each(function(){var e,t,i=di(this).jqmData("cells"),s=di.mobile.getAttribute(this,"colstart"),n=i.not(this).filter("thead th").length&&" ui-table-cell-label-top",o=di(this).clone().contents();0<o.length&&(n?(e=parseInt(this.getAttribute("colspan"),10),t="",a._addLabels(i.filter(t=e?"td:nth-child("+e+"n + "+s+")":t),r.classes.cellLabels+n,o)):a._addLabels(i,r.classes.cellLabels,o))})},_addLabels:function(e,t,i){1===i.length&&"abbr"===i[0].nodeName.toLowerCase()&&(i=i.eq(0).attr("title")),e.not(":has(b."+t+")").prepend(di("<b class='"+t+"'></b>").append(i))}}),(ci=e).widget("mobile.filterable",{initSelector:":jqmData(filter='true')",options:{filterReveal:!1,filterCallback:Mi,enhanced:!1,input:null,children:"> li, > option, > optgroup option, > tbody tr, > .ui-controlgroup-controls > .ui-btn, > .ui-controlgroup-controls > .ui-checkbox, > .ui-controlgroup-controls > .ui-radio"},_create:function(){var e=this.options;ci.extend(this,{_search:null,_timer:0}),this._setInput(e.input),e.enhanced||this._filterItems((this._search&&this._search.val()||"").toLowerCase())},_onKeyUp:function(){var e,t,i=this._search;i&&(e=i.val().toLowerCase(),(t=ci.mobile.getAttribute(i[0],"lastval")+"")&&t===e||(this._timer&&(D.clearTimeout(this._timer),this._timer=0),this._timer=this._delay(function(){return!1!==this._trigger("beforefilter",null,{input:i})&&(i[0].setAttribute("data-"+ci.mobile.ns+"lastval",e),this._filterItems(e),void(this._timer=0))},250)))},_getFilterableItems:function(){var e=this.element,t=this.options.children,t=t?Wi(t)?t():t.nodeName?ci(t):t.jquery?t:this.element.find(t):{length:0};return t=0===t.length?e.children():t},_filterItems:function(e){var t,i,s,n=[],o=[],a=this.options,r=this._getFilterableItems();if(null!=e)for(i=a.filterCallback||Mi,s=r.length,t=0;t<s;t++)(i.call(r[t],t,e)?o:n).push(r[t]);0===o.length?r[a.filterReveal&&0===e.length?"addClass":"removeClass"]("ui-screen-hidden"):(ci(o).addClass("ui-screen-hidden"),ci(n).removeClass("ui-screen-hidden")),this._refreshChildWidget(),this._trigger("filter",null,{items:r})},_refreshChildWidget:function(){for(var e,t=["collapsibleset","selectmenu","controlgroup","listview"],i=t.length-1;-1<i;i--)ci.mobile[e=t[i]]&&(e=this.element.data("mobile-"+e))&&Wi(e.refresh)&&e.refresh()},_setInput:function(e){var t=this._search;this._timer&&(D.clearTimeout(this._timer),this._timer=0),t&&(this._off(t,"keyup change input"),t=null),e&&(t=e.jquery?e:e.nodeName?ci(e):this.document.find(e),this._on(t,{keydown:"_onKeyDown",keypress:"_onKeyPress",keyup:"_onKeyUp",change:"_onKeyUp",input:"_onKeyUp"})),this._search=t},_onKeyDown:function(e){e.keyCode===ci.ui.keyCode.ENTER&&(e.preventDefault(),this._preventKeyPress=!0)},_onKeyPress:function(e){this._preventKeyPress&&(e.preventDefault(),this._preventKeyPress=!1)},_setOptions:function(e){var t=!(e.filterReveal===ui&&e.filterCallback===ui&&e.children===ui);this._super(e),e.input!==ui&&(this._setInput(e.input),t=!0),t&&this.refresh()},_destroy:function(){var e=this.options,t=this._getFilterableItems();e.enhanced?t.toggleClass("ui-screen-hidden",e.filterReveal):t.removeClass("ui-screen-hidden")},refresh:function(){this._timer&&(D.clearTimeout(this._timer),this._timer=0),this._filterItems((this._search&&this._search.val()||"").toLowerCase())}}),fi=/(^|\s)ui-li-divider(\s|$)/,gi=(pi=e).mobile.filterable.prototype.options.filterCallback,pi.mobile.filterable.prototype.options.filterCallback=function(e,t){return!this.className.match(fi)&&gi.call(this,e,t)},pi.widget("mobile.filterable",pi.mobile.filterable,{options:{filterPlaceholder:"Filter items...",filterTheme:null},_create:function(){var e,t,i=this.element,s=["collapsibleset","selectmenu","controlgroup","listview"],n={};for(this._super(),pi.extend(this,{_widget:null}),e=s.length-1;-1<e;e--)if(pi.mobile[t=s[e]]){if(this._setWidget(i.data("mobile-"+t)))break;n[t+"create"]="_handleCreate"}this._widget||this._on(i,n)},_handleCreate:function(e){this._setWidget(this.element.data("mobile-"+e.type.substring(0,e.type.length-6)))},_trigger:function(e,t,i){return this._widget&&"mobile-listview"===this._widget.widgetFullName&&"beforefilter"===e&&this._widget._trigger("beforefilter",t,i),this._super(e,t,i)},_setWidget:function(e){var t,i;return!this._widget&&e&&(this._widget=e,this._widget._setOptions=(i=(t=this)._widget._setOptions,function(e){i.call(this,e),t._syncTextInputOptions(e)})),this._widget&&(this._syncTextInputOptions(this._widget.options),"listview"===this._widget.widgetName&&(this._widget.options.hideDividers=!0,this._widget.element.listview("refresh"))),!!this._widget},_isSearchInternal:function(){return this._search&&this._search.jqmData("ui-filterable-"+this.uuid+"-internal")},_setInput:function(t){var e=this.options,i=!0,s={};if(!t){if(this._isSearchInternal())return;i=!1,t=pi("<input data-"+pi.mobile.ns+"type='search' placeholder='"+e.filterPlaceholder+"'></input>").jqmData("ui-filterable-"+this.uuid+"-internal",!0),pi("<form class='ui-filterable'></form>").append(t).submit(function(e){e.preventDefault(),t.trigger("blur")}).insertBefore(this.element),pi.mobile.textinput&&(null!=this.options.filterTheme&&(s.theme=e.filterTheme),t.textinput(s))}this._super(t),this._isSearchInternal()&&i&&this._search.attr("placeholder",this.options.filterPlaceholder)},_setOptions:function(e){var t=this._super(e);return e.filterPlaceholder!==mi&&this._isSearchInternal()&&this._search.attr("placeholder",e.filterPlaceholder),e.filterTheme!==mi&&this._search&&pi.mobile.textinput&&this._search.textinput("option","theme",e.filterTheme),t},_refreshChildWidget:function(){this._refreshingChildWidget=!0,this._superApply(arguments),this._refreshingChildWidget=!1},refresh:function(){this._refreshingChildWidget||this._superApply(arguments)},_destroy:function(){this._isSearchInternal()&&this._search.remove(),this._super()},_syncTextInputOptions:function(e){var t,i={};if(this._isSearchInternal()&&pi.mobile.textinput){for(t in pi.mobile.textinput.prototype.options)e[t]!==mi&&("theme"===t&&null!=this.options.filterTheme?i[t]=this.options.filterTheme:i[t]=e[t]);this._search.textinput("option",i)}}}),pi.widget("mobile.listview",pi.mobile.listview,{options:{filter:!1},_create:function(){return!0!==this.options.filter||this.element.data("mobile-filterable")||this.element.filterable(),this._super()},refresh:function(){var e;this._superApply(arguments),!0===this.options.filter&&(e=this.element.data("mobile-filterable"))&&e.refresh()}}),vi=0,_i=/#.*$/,(bi=e).widget("ui.tabs",{version:"fadf2b312a05040436451c64bbfaf4814bc62c56",delay:300,options:{active:null,collapsible:!1,event:"click",heightStyle:"content",hide:null,show:null,activate:null,beforeActivate:null,beforeLoad:null,load:null},_create:function(){var t=this,e=this.options;this.running=!1,this.element.addClass("ui-tabs ui-widget ui-widget-content ui-corner-all").toggleClass("ui-tabs-collapsible",e.collapsible).on(".ui-tabs-nav > li","mousedown"+this.eventNamespace,function(e){bi(this).is(".ui-state-disabled")&&e.preventDefault()}).on(".ui-tabs-anchor","focus"+this.eventNamespace,function(){bi(this).closest("li").is(".ui-state-disabled")&&this.trigger("blur")}),this._processTabs(),e.active=this._initialActive(),bi.isArray(e.disabled)&&(e.disabled=bi.unique(e.disabled.concat(bi.map(this.tabs.filter(".ui-state-disabled"),function(e){return t.tabs.index(e)}))).sort()),!1!==this.options.active&&this.anchors.length?this.active=this._findActive(e.active):this.active=bi(),this._refresh(),this.active.length&&this.load(e.active)},_initialActive:function(){var i=this.options.active,e=this.options.collapsible,s=location.hash.substring(1);return null===i&&(s&&this.tabs.each(function(e,t){if(bi(t).attr("aria-controls")===s)return i=e,!1}),null!==(i=null===i?this.tabs.index(this.tabs.filter(".ui-tabs-active")):i)&&-1!==i||(i=!!this.tabs.length&&0)),!1!==i&&-1===(i=this.tabs.index(this.tabs.eq(i)))&&(i=!e&&0),i=!e&&!1===i&&this.anchors.length?0:i},_getCreateEventData:function(){return{tab:this.active,panel:this.active.length?this._getPanelForTab(this.active):bi()}},_tabKeydown:function(e){var t=bi(this.document[0].activeElement).closest("li"),i=this.tabs.index(t),s=!0;if(!this._handlePageNav(e)){switch(e.keyCode){case bi.ui.keyCode.RIGHT:case bi.ui.keyCode.DOWN:i++;break;case bi.ui.keyCode.UP:case bi.ui.keyCode.LEFT:s=!1,i--;break;case bi.ui.keyCode.END:i=this.anchors.length-1;break;case bi.ui.keyCode.HOME:i=0;break;case bi.ui.keyCode.SPACE:return e.preventDefault(),clearTimeout(this.activating),void this._activate(i);case bi.ui.keyCode.ENTER:return e.preventDefault(),clearTimeout(this.activating),void this._activate(i!==this.options.active&&i);default:return}e.preventDefault(),clearTimeout(this.activating),i=this._focusNextTab(i,s),e.ctrlKey||(t.attr("aria-selected","false"),this.tabs.eq(i).attr("aria-selected","true"),this.activating=this._delay(function(){this.option("active",i)},this.delay))}},_panelKeydown:function(e){this._handlePageNav(e)||e.ctrlKey&&e.keyCode===bi.ui.keyCode.UP&&(e.preventDefault(),this.active.trigger("focus"))},_handlePageNav:function(e){return e.altKey&&e.keyCode===bi.ui.keyCode.PAGE_UP?(this._activate(this._focusNextTab(this.options.active-1,!1)),!0):e.altKey&&e.keyCode===bi.ui.keyCode.PAGE_DOWN?(this._activate(this._focusNextTab(this.options.active+1,!0)),!0):void 0},_findNextTab:function(e,t){var i=this.tabs.length-1;for(;-1!==bi.inArray(e=(e=i<e?0:e)<0?i:e,this.options.disabled);)e=t?e+1:e-1;return e},_focusNextTab:function(e,t){return e=this._findNextTab(e,t),this.tabs.eq(e).trigger("focus"),e},_setOption:function(e,t){"active"!==e?"disabled"!==e?(this._super(e,t),"collapsible"===e&&(this.element.toggleClass("ui-tabs-collapsible",t),t||!1!==this.options.active||this._activate(0)),"event"===e&&this._setupEvents(t),"heightStyle"===e&&this._setupHeightStyle(t)):this._setupDisabled(t):this._activate(t)},_tabId:function(e){return e.attr("aria-controls")||"ui-tabs-"+ ++vi},_sanitizeSelector:function(e){return e?e.replace(/[!"$%&'()*+,.\/:;<=>?@\[\]\^`{|}~]/g,"\\$&"):""},refresh:function(){var e=this.options,t=this.tablist.children(":has(a[href])");e.disabled=bi.map(t.filter(".ui-state-disabled"),function(e){return t.index(e)}),this._processTabs(),!1!==e.active&&this.anchors.length?this.active.length&&!bi.contains(this.tablist[0],this.active[0])?this.tabs.length===e.disabled.length?(e.active=!1,this.active=bi()):this._activate(this._findNextTab(Math.max(0,e.active-1),!1)):e.active=this.tabs.index(this.active):(e.active=!1,this.active=bi()),this._refresh()},_refresh:function(){this._setupDisabled(this.options.disabled),this._setupEvents(this.options.event),this._setupHeightStyle(this.options.heightStyle),this.tabs.not(this.active).attr({"aria-selected":"false",tabIndex:-1}),this.panels.not(this._getPanelForTab(this.active)).hide().attr({"aria-expanded":"false","aria-hidden":"true"}),this.active.length?(this.active.addClass("ui-tabs-active ui-state-active").attr({"aria-selected":"true",tabIndex:0}),this._getPanelForTab(this.active).show().attr({"aria-expanded":"true","aria-hidden":"false"})):this.tabs.eq(0).attr("tabIndex",0)},_processTabs:function(){var r=this;this.tablist=this._getList().addClass("ui-tabs-nav ui-helper-reset ui-helper-clearfix ui-widget-header ui-corner-all").attr("role","tablist"),this.tabs=this.tablist.find("> li:has(a[href])").addClass("ui-state-default ui-corner-top").attr({role:"tab",tabIndex:-1}),this.anchors=this.tabs.map(function(){return bi("a",this)[0]}).addClass("ui-tabs-anchor").attr({role:"presentation",tabIndex:-1}),this.panels=bi(),this.anchors.each(function(e,t){var i,s,n=bi(t).uniqueId().attr("id"),o=bi(t).closest("li"),a=o.attr("aria-controls");Ui(t)?(i=t.hash,s=r.element.find(r._sanitizeSelector(i))):(t=r._tabId(o),(s=r.element.find(i="#"+t)).length||(s=r._createPanel(t)).insertAfter(r.panels[e-1]||r.tablist),s.attr("aria-live","polite")),s.length&&(r.panels=r.panels.add(s)),a&&o.data("ui-tabs-aria-controls",a),o.attr({"aria-controls":i.substring(1),"aria-labelledby":n}),s.attr("aria-labelledby",n)}),this.panels.addClass("ui-tabs-panel ui-widget-content ui-corner-bottom").attr("role","tabpanel")},_getList:function(){return this.element.find("ol,ul").eq(0)},_createPanel:function(e){return bi("<div>").attr("id",e).addClass("ui-tabs-panel ui-widget-content ui-corner-bottom").data("ui-tabs-destroy",!0)},_setupDisabled:function(e){bi.isArray(e)&&(e.length?e.length===this.anchors.length&&(e=!0):e=!1);for(var t,i=0;t=this.tabs[i];i++)!0===e||-1!==bi.inArray(i,e)?bi(t).addClass("ui-state-disabled").attr("aria-disabled","true"):bi(t).removeClass("ui-state-disabled").removeAttr("aria-disabled");this.options.disabled=e},_setupEvents:function(e){var i={click:function(e){e.preventDefault()}};e&&bi.each(e.split(" "),function(e,t){i[t]="_eventHandler"}),this._off(this.anchors.add(this.tabs).add(this.panels)),this._on(this.anchors,i),this._on(this.tabs,{keydown:"_tabKeydown"}),this._on(this.panels,{keydown:"_panelKeydown"}),this._focusable(this.tabs),this._hoverable(this.tabs)},_setupHeightStyle:function(e){var i,t=this.element.parent();"fill"===e?(i=t.height(),i-=this.element.outerHeight()-this.element.height(),this.element.siblings(":visible").each(function(){var e=bi(this),t=e.css("position");"absolute"!==t&&"fixed"!==t&&(i-=e.outerHeight(!0))}),this.element.children().not(this.panels).each(function(){i-=bi(this).outerHeight(!0)}),this.panels.each(function(){bi(this).height(Math.max(0,i-bi(this).innerHeight()+bi(this).height()))}).css("overflow","auto")):"auto"===e&&(i=0,this.panels.each(function(){i=Math.max(i,bi(this).height("").height())}).height(i))},_eventHandler:function(e){var t=this.options,i=this.active,s=bi(e.currentTarget).closest("li"),n=s[0]===i[0],o=n&&t.collapsible,a=o?bi():this._getPanelForTab(s),r=i.length?this._getPanelForTab(i):bi(),i={oldTab:i,oldPanel:r,newTab:o?bi():s,newPanel:a};e.preventDefault(),s.hasClass("ui-state-disabled")||s.hasClass("ui-tabs-loading")||this.running||n&&!t.collapsible||!1===this._trigger("beforeActivate",e,i)||(t.active=!o&&this.tabs.index(s),this.active=n?bi():s,this.xhr&&this.xhr.abort(),r.length||a.length||bi.error("jQuery UI Tabs: Mismatching fragment identifier."),a.length&&this.load(this.tabs.index(s),e),this._toggle(e,i))},_toggle:function(e,t){var i=this,s=t.newPanel,n=t.oldPanel;function o(){i.running=!1,i._trigger("activate",e,t)}function a(){t.newTab.closest("li").addClass("ui-tabs-active ui-state-active"),s.length&&i.options.show?i._show(s,i.options.show,o):(s.show(),o())}this.running=!0,n.length&&this.options.hide?this._hide(n,this.options.hide,function(){t.oldTab.closest("li").removeClass("ui-tabs-active ui-state-active"),a()}):(t.oldTab.closest("li").removeClass("ui-tabs-active ui-state-active"),n.hide(),a()),n.attr({"aria-expanded":"false","aria-hidden":"true"}),t.oldTab.attr("aria-selected","false"),s.length&&n.length?t.oldTab.attr("tabIndex",-1):s.length&&this.tabs.filter(function(){return 0===bi(this).attr("tabIndex")}).attr("tabIndex",-1),s.attr({"aria-expanded":"true","aria-hidden":"false"}),t.newTab.attr({"aria-selected":"true",tabIndex:0})},_activate:function(e){var e=this._findActive(e);e[0]!==this.active[0]&&(e=(e=!e.length?this.active:e).find(".ui-tabs-anchor")[0],this._eventHandler({target:e,currentTarget:e,preventDefault:bi.noop}))},_findActive:function(e){return!1===e?bi():this.tabs.eq(e)},_getIndex:function(e){return e="string"==typeof e?this.anchors.index(this.anchors.filter("[href$='"+e+"']")):e},_destroy:function(){this.xhr&&this.xhr.abort(),this.element.removeClass("ui-tabs ui-widget ui-widget-content ui-corner-all ui-tabs-collapsible"),this.tablist.removeClass("ui-tabs-nav ui-helper-reset ui-helper-clearfix ui-widget-header ui-corner-all").removeAttr("role"),this.anchors.removeClass("ui-tabs-anchor").removeAttr("role").removeAttr("tabIndex").removeUniqueId(),this.tabs.add(this.panels).each(function(){bi.data(this,"ui-tabs-destroy")?bi(this).remove():bi(this).removeClass("ui-state-default ui-state-active ui-state-disabled ui-corner-top ui-corner-bottom ui-widget-content ui-tabs-active ui-tabs-panel").removeAttr("tabIndex").removeAttr("aria-live").removeAttr("aria-busy").removeAttr("aria-selected").removeAttr("aria-labelledby").removeAttr("aria-hidden").removeAttr("aria-expanded").removeAttr("role")}),this.tabs.each(function(){var e=bi(this),t=e.data("ui-tabs-aria-controls");t?e.attr("aria-controls",t).removeData("ui-tabs-aria-controls"):e.removeAttr("aria-controls")}),this.panels.show(),"content"!==this.options.heightStyle&&this.panels.css("height","")},enable:function(i){var e=this.options.disabled;!1!==e&&(e=void 0!==i&&(i=this._getIndex(i),bi.isArray(e)?bi.map(e,function(e){return e!==i?e:null}):bi.map(this.tabs,function(e,t){return t!==i?t:null})),this._setupDisabled(e))},disable:function(e){var t=this.options.disabled;if(!0!==t){if(void 0===e)t=!0;else{if(e=this._getIndex(e),-1!==bi.inArray(e,t))return;t=bi.isArray(t)?bi.merge([e],t).sort():[e]}this._setupDisabled(t)}},load:function(e,t){e=this._getIndex(e);var i=this,s=this.tabs.eq(e),e=s.find(".ui-tabs-anchor"),n=this._getPanelForTab(s),o={tab:s,panel:n};Ui(e[0])||(this.xhr=bi.ajax(this._ajaxSettings(e,t,o)),this.xhr&&"canceled"!==this.xhr.statusText&&(s.addClass("ui-tabs-loading"),n.attr("aria-busy","true"),this.xhr.success(function(e){setTimeout(function(){n.html(e),i._trigger("load",t,o)},1)}).complete(function(e,t){setTimeout(function(){"abort"===t&&i.panels.stop(!1,!0),s.removeClass("ui-tabs-loading"),n.removeAttr("aria-busy"),e===i.xhr&&delete i.xhr},1)})))},_ajaxSettings:function(e,i,s){var n=this;return{url:e.attr("href"),beforeSend:function(e,t){return n._trigger("beforeLoad",i,bi.extend({jqXHR:e,ajaxSettings:t},s))}}},_getPanelForTab:function(e){e=bi(e).attr("aria-controls");return this.element.find(this._sanitizeSelector("#"+e))}}),function(e,t){e.mobile.iosorientationfixEnabled=!0;var i,s,n,o,a=navigator.userAgent;function r(e){n=e.originalEvent,o=n.accelerationIncludingGravity,s=Math.abs(o.x),n=Math.abs(o.y),o=Math.abs(o.z),!t.orientation&&(7<s||(6<o&&n<8||o<8&&6<n)&&5<s)?i.enabled&&i.disable():i.enabled||i.enable()}/iPhone|iPad|iPod/.test(navigator.platform)&&/OS [1-5]_[0-9_]* like Mac OS X/i.test(a)&&-1<a.indexOf("AppleWebKit")?(i=e.mobile.zoom,e.mobile.document.on("mobileinit",function(){e.mobile.iosorientationfixEnabled&&e.mobile.window.on("orientationchange.iosorientationfix",i.enable).on("devicemotion.iosorientationfix",r)})):e.mobile.iosorientationfixEnabled=!1}(e,this),wi=this,yi=(Ci=e)("html"),xi=Ci.mobile.window,Ci(wi.document).trigger("mobileinit"),Ci.mobile.gradeA()&&(Ci.mobile.ajaxBlacklist&&(Ci.mobile.ajaxEnabled=!1),yi.addClass("ui-mobile ui-mobile-rendering"),setTimeout(Ri,5e3),Ci.extend(Ci.mobile,{initializePage:function(){var t=Ci.mobile.path,e=Ci(":jqmData(role='page'), :jqmData(role='dialog')"),i=t.stripHash(t.stripQueryParams(t.parseLocation().hash)),s=Ci.mobile.path.parseLocation(),n=i?E.getElementById(i):void 0;(e=!e.length?Ci("body").wrapInner("<div data-"+Ci.mobile.ns+"role='page'></div>").children(0):e).each(function(){var e=Ci(this);e[0].getAttribute("data-"+Ci.mobile.ns+"url")||e.attr("data-"+Ci.mobile.ns+"url",e.attr("id")||t.convertUrlToDataUrl(s.pathname+s.search))}),Ci.mobile.firstPage=e.first(),Ci.mobile.pageContainer=Ci.mobile.firstPage.parent().addClass("ui-mobile-viewport").pagecontainer(),Ci.mobile.navreadyDeferred.resolve(),xi.trigger("pagecontainercreate"),Ci.mobile.loading("show"),Ri(),Ci.mobile.hashListeningEnabled&&Ci.mobile.path.isHashValid(location.hash)&&(Ci(n).is(":jqmData(role='page')")||Ci.mobile.path.isPath(i)||i===Ci.mobile.dialogHashKey)?Ci.event.special.navigate.isPushStateEnabled()?(Ci.mobile.navigate.history.stack=[],Ci.mobile.navigate(Ci.mobile.path.isPath(location.hash)?location.hash:location.href)):xi.trigger("hashchange",[!0]):(Ci.event.special.navigate.isPushStateEnabled()&&Ci.mobile.navigate.navigator.squash(t.parseLocation().href),Ci.mobile.changePage(Ci.mobile.firstPage,{transition:"none",reverse:!0,changeHash:!1,fromHashChange:!0}))}}),Ci(function(){Ci.support.inlineSVG(),Ci.mobile.hideUrlBar&&wi.scrollTo(0,1),Ci.mobile.defaultHomeScroll=Ci.support.scrollTop&&1!==Ci.mobile.window.scrollTop()?1:0,Ci.mobile.autoInitializePage&&Ci.mobile.initializePage(),Ci.mobile.hideUrlBar&&xi.on("load",Ci.mobile.silentScroll),Ci.support.cssPointerEvents||Ci.mobile.document.on(".ui-state-disabled,.ui-disabled","vclick",function(e){e.preventDefault(),e.stopImmediatePropagation()})}))});
