<!DOCTYPE html>
<!--
Copyright (c) 2007-2024, CKSource Holding sp. z o.o. All rights reserved.
For licensing, see LICENSE.html or https://ckeditor.com/sales/license/ckfinder
-->
<html>
<head>
	<meta charset="utf-8">
	<title>CKFinder 3 Samples</title>
	<meta name="viewport" content="width=device-width, initial-scale=1">
	<!--[if lt IE 9]>
	<script src="js/html5shiv.min.js"></script>
	<![endif]-->
	<link href="css/sample.css" rel="stylesheet">
</head>
<body>
<header class="header-a">
	<div class="grid-container">
		<h1 class="header-a-logo grid-width-30">
			<a href="index.html"><img src="img/logo.png" alt="CKFinder Logo"></a>
		</h1>
		<nav class="navigation-b grid-width-70">
			<ul>
				<li><a href="https://ckeditor.com/docs/ckfinder/ckfinder3/" class="button-a">Documentation</a></li>
			</ul>
		</nav>
	</div>
</header>
<main class="grid-container">
	<nav class="tree-a tree-a-layout grid-width-30">
		<h1>CKFinder Samples</h1>
		<h2>Website Integration</h2>
		<ul>
			<li><a href="widget.html">Widget</a></li>
			<li><a href="popups.html">Popup</a></li>
			<li><a href="modals.html">Modal</a></li>
			<li><a href="full-page.html">Full Page</a></li>
		</ul>
		<h2 class="tree-a-no-sub"><a href="ckeditor.html">CKEditor Integration</a></h2>
		<h2>Skins</h2>
		<ul>
			<li><a href="skins-neko.html">Neko</a></li>
			<li><a href="skins-moono.html">Moono</a></li>
			<li><a href="skins-jquery-mobile.html">jQuery Mobile</a></li>
		</ul>
		<h2 class="tree-a-active">User Interface</h2>
		<ul>
			<li><a href="user-interface-default.html">Default</a></li>
			<li><a href="user-interface-compact.html">Compact</a></li>
			<li class="tree-a-active"><a href="user-interface-mobile.html">Mobile</a></li>
			<li><a href="user-interface-listview.html">List View</a></li>
		</ul>
		<h2 class="tree-a-no-sub"><a href="localization.html">Localization</a></h2>
		<h2>Other</h2>
		<ul>
			<li><a href="other-read-only.html">Read-only Mode</a></li>
			<li><a href="other-custom-configuration.html">Custom Configuration</a></li>
		</ul>
		<h2 class="tree-a-no-sub"><a href="plugin-examples.html">Plugin Examples</a></h2>
	</nav>
	<section class="content grid-width-70">
		<h1>Mobile User Interface</h1>
		<p>Mobile user interface is enabled automatically when the width of the working area of the application gets below the value
		defined in the <a href="https://ckeditor.com/docs/ckfinder/ckfinder3/#!/api/CKFinder.Config-cfg-uiModeThreshold"><code>uiModeThreshold</code></a>
		configuration option.</p>
		<p>Note: This mode works best on mobile devices, as touch and swipe events are not enabled for desktop browsers.</p>

		<div id="ckfinder-widget" style="width: 500px; margin: 30px auto 0;"></div>
	</section>
</main>
<footer class="footer-a grid-container">
	<div class="grid-container">
		<p class="grid-width-100">
			CKFinder 3 &ndash; <a href="https://ckeditor.com/ckfinder/">https://ckeditor.com/ckfinder/</a>
		</p>
		<p class="grid-width-100">
			Copyright &copy; 2007-2024, <a class="samples" href="http://cksource.com/">CKSource Holding sp. z o.o.</a>
			<a href="https://ckeditor.com/sales/license/ckfinder">All rights reserved</a>.
		</p>
	</div>
</footer>
<nav class="navigation-a">
	<div class="grid-container">
		<ul class="navigation-a-left grid-width-70">
			<li><a href="https://ckeditor.com/ckfinder/">Project Homepage</a></li>
			<li class="global-is-mobile-hidden"><a href="https://github.com/ckfinder/ckfinder/issues">I found a bug in CKFinder</a></li>
			<li class="global-is-mobile-hidden"><a class="icon-pos-right icon-navigation-a-github" href="https://github.com/ckfinder/ckfinder-docs-samples">Sample Plugins on GitHub</a></li>
		</ul>
	</div>
</nav>

<script src="js/sf.js"></script>
<script src="js/tree-a.js"></script>
<script src="../ckfinder.js"></script>
<script>
	CKFinder.widget( 'ckfinder-widget', {
		displayFoldersPanel: false,
		width: 500,
		height: 700
	} );
</script>
<script src="//cdn.rawgit.com/google/code-prettify/master/loader/run_prettify.js" type="text/javascript"></script>

</body>
</html>
