<?php

namespace App\DTOs;

use App\Helpers\ValidationHelper;
use Illuminate\Contracts\Support\Arrayable;

class RegisterData implements Arrayable
{
  public string $fullname;
  public string $identifier;
  public string $identifierType; // 'email' or 'phone'
  public string $password;
  public string $passwordConfirmation;
  public ?string $address;

  public function __construct(
    string $fullname,
    string $identifier,
    string $password,
    string $passwordConfirmation,
    ?string $address = null
  ) {
    $this->fullname             = trim($fullname);
    $this->identifier           = trim(strtolower($identifier));
    $this->identifierType       = ValidationHelper::detectIdentifierType($this->identifier);
    $this->password             = $password;
    $this->passwordConfirmation = $passwordConfirmation;
    $this->address              = $address ? trim($address) : null;

    // Validate identifier type
    if (!$this->identifierType) {
      throw new \InvalidArgumentException('Identifier phải là email hoặc số điện tho<PERSON><PERSON> hợp lệ');
    }
  }


  /**
   * Get email value if identifier is email
   */
  public function getEmail(): ?string
  {
    return $this->identifierType === 'email' ? $this->identifier : null;
  }

  /**
   * Get phone value if identifier is phone
   */
  public function getPhone(): ?string
  {
    return $this->identifierType === 'phone' ? $this->identifier : null;
  }

  public function toArray(): array
  {
    return [
      'fullname'              => $this->fullname,
      'email'                 => $this->getEmail(),
      'phone'                 => $this->getPhone(),
      'password'              => $this->password,
      'password_confirmation' => $this->passwordConfirmation,
      'address'               => $this->address,
    ];
  }
}
