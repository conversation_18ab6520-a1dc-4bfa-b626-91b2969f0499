<?php

namespace App\DTOs;

use App\Helpers\ImageHelper;
use Illuminate\Http\UploadedFile;
use Illuminate\Http\Request;

class UpdateProfileData
{
  public ?string       $fullname;
  public ?string       $email;
  public ?string       $phone;
  public ?string       $address;
  public ?UploadedFile $avatar;
  public bool          $avatarRemoved;

  public function __construct(array $data)
  {
    $this->fullname      = $data['fullname']      ?? null;
    $this->email         = $data['email']         ?? null;
    $this->phone         = $data['phone']         ?? null;
    $this->address       = $data['address']       ?? null;
    $this->avatar        = $data['avatar']        ?? null;
    $this->avatarRemoved = !empty($data['avatar_removed']);
  }

  public static function fromRequest(Request $request): self
  {
    return new self([
      'fullname'       => $request->input('fullname'),
      'email'          => $request->input('email'),
      'phone'          => $request->input('phone'),
      'address'        => $request->input('address'),
      'avatar'         => $request->file('avatar'),
      'avatar_removed' => $request->input('avatar_removed'),
    ]);
  }

  public function toUpdateArray(): array
  {
    $base = array_filter([
      'fullname' => $this->fullname,
      'email'    => $this->email,
      'phone'    => $this->phone,
      'address'  => $this->address,
    ], fn($v) => $v !== null);

    if ($this->avatar instanceof UploadedFile) {
      $base['avatar'] = $this->avatar;
    } elseif ($this->avatarRemoved) {
      $base['avatar'] = null;
    }

    return $base;
  }

  /**
   * Process avatar upload and return processed data
   *
   * @param int $userId
   * @param string|null $currentAvatar
   * @return array
   */
  public function processAvatar(int $userId, ?string $currentAvatar = null): array
  {
    $data = $this->toUpdateArray();

    // Xử lý avatar upload
    if ($this->avatar instanceof UploadedFile) {
      // Xóa avatar cũ nếu có
      if (!empty($currentAvatar)) {
        ImageHelper::deleteImage($currentAvatar, 'user_profile_update');
      }

      // Upload avatar mới
      $data['avatar'] = ImageHelper::uploadUserAvatar($this->avatar, $userId);
    } elseif ($this->avatarRemoved) {
      // Xóa avatar nếu user muốn xóa
      if (!empty($currentAvatar)) {
        ImageHelper::deleteImage($currentAvatar, 'user_profile_update');
      }
      $data['avatar'] = null;
    }

    return $data;
  }

  /**
   * Get avatar URL for response
   *
   * @param string|null $avatarPath
   * @return string
   */
  public static function getAvatarUrl(?string $avatarPath): string
  {
    return ImageHelper::getUserAvatarUrl($avatarPath);
  }
}
