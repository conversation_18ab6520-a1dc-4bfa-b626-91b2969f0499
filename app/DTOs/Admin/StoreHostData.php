<?php

namespace App\DTOs\Admin;

use Illuminate\Http\Request;

class StoreHostData
{
    public ?int    $userId;
    public string  $name;
    public ?string $aliasName;
    public ?string $email;
    public ?string $phone;
    public ?string $zalo;
    public ?string $address;
    public bool    $status;
    public ?int    $inChargeId;
    public ?string $description;
    public ?int    $creatorId;
    public ?int    $editorId;

    public function __construct(array $data)
    {
        $this->userId     = $data['user_id']     ?? null;
        $this->name       = $data['name'];
        $this->aliasName  = $data['alias_name']  ?? null;
        $this->email      = $data['email']       ?? null;
        $this->phone      = $data['phone']       ?? null;
        $this->zalo       = $data['zalo']        ?? null;
        $this->address    = $data['address']     ?? null;
        $this->status     = (bool)($data['status'] ?? true);
        $this->inChargeId = $data['in_charge_id'] ?? null;
        $this->description = $data['description'] ?? null;
    }

    public static function fromRequest(Request $request): self
    {
        return new self([
            'user_id'      => $request->input('user_id'),
            'name'         => $request->input('name'),
            'alias_name'   => $request->input('alias_name'),
            'email'        => $request->input('email'),
            'phone'        => $request->input('phone'),
            'zalo'         => $request->input('zalo'),
            'address'      => $request->input('address'),
            'status'       => $request->boolean('status'),
            'in_charge_id' => $request->input('in_charge_id'),
            'description'  => $request->input('description'),
        ]);
    }

    public function toCreateArray(): array
    {
        $data = array_filter([
            'user_id'      => $this->userId,
            'name'         => $this->name,
            'alias_name'   => $this->aliasName,
            'email'        => $this->email,
            'phone'        => $this->phone,
            'zalo'         => $this->zalo,
            'address'      => $this->address,
            'status'      => $this->status,
            'in_charge_id' => $this->inChargeId,
            'description'  => $this->description,
        ], fn($v) => $v !== null);


        return $data;
    }
}
