<?php

namespace App\DTOs\Admin;

use Illuminate\Http\Request;

class UpdateTeamData
{
    public ?string $name;
    public ?string $code;
    public ?int    $managerId;
    public ?string $color;
    public ?string $telegram;
    public ?bool   $status;
    public ?int    $creatorId;
    public ?int    $editorId;
    public ?int    $position;

    public function __construct(array $data)
    {
        $this->name      = $data['name']       ?? null;
        $this->code      = $data['code']       ?? null;
        $this->managerId = $data['manager_id'] ?? null;
        $this->content   = $data['content']    ?? null;
        $this->color     = $data['color']      ?? null;
        $this->telegram  = $data['telegram']   ?? null;
        $this->status    = isset($data['status']) ? (bool) $data['status'] : null;
        $this->creatorId = $data['creator_id'] ?? null;
        $this->editorId  = $data['editor_id']  ?? null;
        $this->position  = $data['position']   ?? null;
    }


    public static function fromRequest(Request $request): self
    {
        return new self([
            'name'         => $request->input('name'),
            'code'         => $request->input('code'),
            'manager_id'   => $request->input('manager_id'),
            'content'      => $request->input('content'),
            'color'        => $request->input('color'),
            'telegram'     => $request->input('telegram'),
            'status'       => $request->has('status') ? $request->boolean('status') : null,
            'position'     => $request->input('position'),
        ]);
    }

    public function toUpdateArray(): array
    {
        $data = [
            'name'         => $this->name,
            'code'         => $this->code,
            'manager_id'   => $this->managerId,
            'content'      => $this->content,
            'color'        => $this->color,
            'telegram'     => $this->telegram,
            'creator_id'   => $this->creatorId,
            'editor_id'    => $this->editorId,
            'position'     => $this->position,
            'status' => $this->status
        ];

        return $data;
    }
}
