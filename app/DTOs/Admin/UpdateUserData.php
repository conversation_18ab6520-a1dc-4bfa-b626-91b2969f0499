<?php

namespace App\DTOs\Admin;

use Illuminate\Http\Request;

class UpdateUserData
{
    public ?string $fullname;
    public ?string $phone;
    public ?string $email;
    public ?string $address;
    public ?string $dateOfBirth;
    public ?string $gender;
    public ?bool   $status;
    public ?int    $supporterId;

    public function __construct(array $data)
    {
        $this->fullname     = $data['fullname']       ?? null;
        $this->phone        = $data['phone']          ?? null;
        $this->email        = $data['email']          ?? null;
        $this->address      = $data['address']        ?? null;
        $this->dateOfBirth  = $data['date_of_birth']  ?? null;
        $this->gender       = $data['gender']         ?? null;
        $this->status       = array_key_exists('status', $data)
            ? (bool) $data['status']
            : null;
        $this->supporterId  = $data['supporter_id']   ?? null;
    }

    public static function fromRequest(Request $request): self
    {
        return new self([
            'fullname'       => $request->input('fullname'),
            'phone'          => $request->input('phone'),
            'email'          => $request->input('email'),
            'address'        => $request->input('address'),
            'date_of_birth'  => $request->input('date_of_birth'),
            'gender'         => $request->input('gender'),
            'status'         => $request->has('status')
                ? $request->boolean('status')
                : null,
            'supporter_id'   => $request->input('supporter_id'),
        ]);
    }

    public function toUpdateArray(): array
    {
        return [
            'fullname'       => $this->fullname,
            'phone'          => $this->phone,
            'email'          => $this->email,
            'address'        => $this->address,
            'date_of_birth'  => $this->dateOfBirth,
            'gender'         => $this->gender,
            'status'         => $this->status,
            'supporter_id'   => $this->supporterId,
        ];
    }
}
