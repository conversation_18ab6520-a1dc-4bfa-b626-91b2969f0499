<?php

namespace App\DTOs\Admin;

use Illuminate\Http\Request;
use Illuminate\Http\UploadedFile;

class UpdateBeautiTypeData
{
    public string  $name;
    public ?string  $slug;
    public ?UploadedFile $image;
    public ?bool $imageRemoved;
    public ?string $description;
    public bool    $status;
    public bool    $featured;
    public ?int    $position;
    public ?string $seoTitle;
    public ?string $seoDescription;
    public ?string $seoKeywords;
    public ?UploadedFile $seo_image;
    public ?bool $seoImageRemoved;

    public function __construct(array $data)
    {
        $this->name            = $data['name'];
        $this->slug            = $data['slug'] ?? null;
        $this->image           = $data['image'] ?? null;
        $this->imageRemoved    = !empty($data['image_removed']);
        $this->description     = $data['description'] ?? null;
        $this->status          = $data['status'];
        $this->featured          = $data['featured'];
        $this->position        = $data['position'] ?? null;
        $this->seoTitle        = $data['seo_title'] ?? null;
        $this->seoDescription  = $data['seo_description'] ?? null;
        $this->seoKeywords     = $data['seo_keywords'] ?? null;
        $this->seo_image       = $data['seo_image'] ?? null;
        $this->seoImageRemoved = !empty($data['seo_image_removed']);
    }

    public static function fromRequest(Request $request): self
    {
        return new self([
            'name'            => $request->input('name'),
            'slug'            => $request->input('slug'),
            'image'           => $request->file('image'),
            'image_removed'   => $request->boolean('image_removed'),
            'description'     => $request->input('description'),
            'status'          => $request->boolean('status'),
            'featured'          => $request->boolean('featured'),
            'position'        => $request->input('position'),
            'seo_title'       => $request->input('seo_title'),
            'seo_description' => $request->input('seo_description'),
            'seo_keywords'    => $request->input('seo_keywords'),
            'seo_image'       => $request->file('seo_image'),
            'seo_image_removed' => $request->boolean('seo_image_removed')
        ]);
    }

    public function toUpdateArray(): array
    {
        $data =  [
            'name'            => $this->name,
            'slug'            => $this->slug,
            'status'          => $this->status,
            'featured'          => $this->featured,
            'description'     => $this->description,
            'position'        => $this->position,
            'seo_title'       => $this->seoTitle,
            'seo_description' => $this->seoDescription,
            'seo_keywords'    => $this->seoKeywords,
        ];

        if ($this->image instanceof UploadedFile) {
            $data['image'] = $this->image;
        } elseif ($this->imageRemoved) {
            $data['image'] = null;
        }
        if ($this->seo_image instanceof UploadedFile) {
            $data['seo_image'] = $this->seo_image;
        } elseif ($this->seoImageRemoved) {
            $data['seo_image'] = null;
        }
        return $data;
    }
}
