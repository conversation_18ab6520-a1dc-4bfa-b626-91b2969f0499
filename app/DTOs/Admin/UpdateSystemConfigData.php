<?php
// app/DTO/Admin/UpdateMemberData.php
namespace App\DTOs\Admin;

use App\Models\ErpMember;
use Illuminate\Http\UploadedFile;
use Illuminate\Http\Request;

class UpdateSystemConfigData
{
    public string       $name;
    public ?string       $code;
    public ?string       $title;
    public ?UploadedFile $image;
    public ?string       $link;
    public ?string       $urlTarget;
    public bool          $avatarRemoved;
    public ?bool         $status;
    public ?String        $description;
    public ?String        $data;

    public function __construct(array $data)
    {
        $this->name      = $data['name'];
        $this->code           = $data['code']           ?? null;
        $this->title           = $data['title']           ?? null;
        $this->image         = $data['image']         ?? null;
        $this->link          = $data['link']          ?? null;
        $this->imageRemoved  = !empty($data['image_removed']);
        $this->urlTarget         = $data['url_target']  ?? null;
        $this->status         = isset($data['status']) ? (bool) $data['status'] : null;
        $this->description         = $data['description']  ?? null;
        $this->data         = $data['data']  ?? null;
    }

    public static function fromRequest(Request $request): self
    {
        return new self([
            'name'      => $request->input('name'),
            'code'       => $request->input('code'),
            'title'          => $request->input('title'),
            'image'         => $request->file('image'),
            'image_removed' => $request->input('image_removed'),
            'link'          => $request->input('link'),
            'url_target'         => $request->input('url_target'),
            'status'         => $request->has('status') ? $request->boolean('status') : null,
            'description'          => $request->input('description'),
            'data'          => $request->input('data'),
        ]);
    }

    public function toUpdateArray(): array
    {
        $base = [
            'name'     => $this->name,
            'code'          => $this->code,
            'title'         => $this->title,
            'link'         => $this->link,
            'url_target'        => $this->urlTarget,
            'status'        => $this->status,
            'data'        => $this->data,
            'description'        => $this->description
        ];

        if ($this->image instanceof UploadedFile) {
            $base['image'] = $this->image;
        } elseif ($this->imageRemoved) {
            $base['image'] = null;
        }

        return $base;
    }
}
