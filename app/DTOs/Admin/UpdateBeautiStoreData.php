<?php

namespace App\DTOs\Admin;

use Illuminate\Http\Request;
use Illuminate\Http\UploadedFile;

class UpdateBeautiStoreData
{
    public string $name;
    public ?int $beautiId;
    public ?string $slug;
    public ?int $priceFrom;
    public ?UploadedFile $image;
    public ?bool $imageRemoved;
    public ?UploadedFile $logo;
    public ?bool $logoRemoved;
    public ?string $street;
    public ?string $wardCode;
    public ?string $districtCode;
    public ?string $provinceCode;
    public ?string $fullAddress;
    public ?string $description;
    public bool $status;
    public ?string $openingTime;
    public ?string $closingTime;
    public ?int $position;
    public ?string $seoTitle;
    public ?string $seoDescription;
    public ?string $seoKeywords;
    public ?UploadedFile $seoImage;
    public ?bool $seoImageRemoved;
    public array $services;
    public array $beautiTypes;

    public function __construct(array $data)
    {
        $this->name           = $data['name'];
        $this->beautiId         = $data['beauti_id'] ?? null;
        $this->slug           = $data['slug'] ?? null;
        $this->priceFrom      = $data['price_from'] ?? null;
        $this->image          = $data['image'] ?? null;
        $this->imageRemoved   = !empty($data['image_removed']);
        $this->logo          = $data['logo'] ?? null;
        $this->logoRemoved   = !empty($data['logo_removed']);
        $this->street         = $data['street'] ?? null;
        $this->wardCode       = $data['ward_code'] ?? null;
        $this->districtCode   = $data['district_code'] ?? null;
        $this->provinceCode   = $data['province_code'] ?? null;
        $this->fullAddress        = $data['full_address'] ?? null;
        $this->description        = $data['description'] ?? null;
        $this->fullAddress        = $data['full_address'] ?? null;
        $this->status         = $data['status'];
        $this->openingTime      = $data['opening_time'] ?? null;
        $this->closingTime      = $data['closing_time'] ?? null;
        $this->position       = $data['position'] ?? null;
        $this->seoTitle       = $data['seo_title'] ?? null;
        $this->seoDescription = $data['seo_description'] ?? null;
        $this->seoKeywords    = $data['seo_keywords'] ?? null;
        $this->seoImage       = $data['seo_image'] ?? null;
        $this->seoImageRemoved = !empty($data['seo_image_removed']);
        $this->services       = $data['services'] ?? [];
        $this->beautiTypes     = $data['beauti_types'] ?? [];
    }


    public static function fromRequest(Request $request): self
    {
        return new self([
            'name'              => $request->input('name'),
            'beauti_id'           => $request->input('beauti_id'),
            'slug'              => $request->input('slug'),
            'price_from'        => $request->input('price_from') !== null
                ? intval($request->input('price_from'))
                : null,
            'image'             => $request->file('image'),
            'image_removed'     => $request->boolean('image_removed'),
            'logo'             => $request->file('logo'),
            'logo_removed'     => $request->boolean('logo_removed'),
            'street'            => $request->input('street'),
            'ward_code'         => $request->input('ward_code'),
            'district_code'     => $request->input('district_code'),
            'province_code'     => $request->input('province_code'),
            'full_address'           => $request->input('full_address'),
            'description'           => $request->input('description'),
            'status'            => $request->boolean('status'),
            'opening_time'      => $request->input('opening_time'),
            'closing_time'      => $request->input('closing_time'),
            'position'          => $request->input('position'),
            'seo_title'         => $request->input('seo_title'),
            'seo_description'   => $request->input('seo_description'),
            'seo_keywords'      => $request->input('seo_keywords'),
            'seo_image'         => $request->file('seo_image'),
            'seo_image_removed' => $request->boolean('seo_image_removed'),
            'services'          => $request->input('services', []),
            'beauti_types'    => $request->input('beauti_types', []),
        ]);
    }


    public function toUpdateArray(): array
    {
        $data = [
            'name'            => $this->name,
            'beauti_id'         => $this->beautiId,
            'slug'            => $this->slug,
            'price_from'      => $this->priceFrom,
            'street'          => $this->street,
            'ward_code'       => $this->wardCode,
            'district_code'   => $this->districtCode,
            'province_code'   => $this->provinceCode,
            'full_address'         => $this->fullAddress,
            'description'         => $this->description,
            'status'          => $this->status,
            'opening_time'    => $this->openingTime,
            'closing_time'    => $this->closingTime,
            'position'        => $this->position,
            'seo_title'       => $this->seoTitle,
            'seo_description' => $this->seoDescription,
            'seo_keywords'    => $this->seoKeywords,
        ];

        if ($this->image instanceof UploadedFile) {
            $data['image'] = $this->image;
        } elseif ($this->imageRemoved) {
            $data['image'] = null;
        }
        if ($this->seoImage instanceof UploadedFile) {
            $data['seo_image'] = $this->seoImage;
        } elseif ($this->seoImageRemoved) {
            $data['seo_image'] = null;
        }

        if ($this->logo instanceof UploadedFile) {
            $data['logo'] = $this->logo;
        } elseif ($this->logoRemoved) {
            $data['logo'] = null;
        }

        return $data;
    }

    public function getServices(): array
    {
        return $this->services;
    }

        public function getBeautiTypes(): array
    {
        return $this->beautiTypes;
    }
}
