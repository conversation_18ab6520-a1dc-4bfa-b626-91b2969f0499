<?php

namespace App\DTOs\Admin;

use Illuminate\Http\Request;

class UpdateLocationWardData
{
    public string  $name;
    public string  $code;
    public string  $type;
    public string  $districtCode;

    public function __construct(array $data)
    {
        $this->name         = $data['name'];
        $this->code         = $data['code'];
        $this->type         = $data['type'];
        $this->districtCode = $data['district_code'];
    }

    public static function fromRequest(Request $request): self
    {
        return new self([
            'name'          => $request->input('name'),
            'code'          => $request->input('code'),
            'type'          => $request->input('type'),
            'district_code' => $request->input('district_code'),
        ]);
    }

    public function toUpdateArray(): array
    {
        $data = [
            'name'          => $this->name,
            'code'          => $this->code,
            'type'          => $this->type,
            'district_code' => $this->districtCode,
        ];


        return $data;
    }
}
