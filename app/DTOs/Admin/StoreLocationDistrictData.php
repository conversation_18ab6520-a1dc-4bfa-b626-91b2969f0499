<?php

namespace App\DTOs\Admin;

use Illuminate\Http\Request;

class StoreLocationDistrictData
{
    public string  $name;
    public string  $code;
    public string  $type;
    public string  $provinceCode; 

    public function __construct(array $data)
    {
        $this->name         = $data['name'];
        $this->code         = $data['code'];
        $this->type         = $data['type'];
        $this->provinceCode = $data['province_code'];
    }

    public static function fromRequest(Request $request): self
    {
        return new self([
            'name'          => $request->input('name'),
            'code'          => $request->input('code'),
            'type'          => $request->input('type'),
            'province_code' => $request->input('province_code'),
        ]);
    }

    public function toCreateArray(): array
    {
        $data = array_filter([
            'name'          => $this->name,
            'code'          => $this->code,
            'type'          => $this->type,
            'province_code' => $this->provinceCode,
        ], fn($value) => $value !== null);

        return $data;
    }
}