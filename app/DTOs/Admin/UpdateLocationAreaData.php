<?php

namespace App\DTOs\Admin;

use Illuminate\Http\Request;
use Illuminate\Http\UploadedFile;

class UpdateLocationAreaData
{
    public string $name;
    public string $provinceCode;
    public ?string $districtCode;
    public ?string $wardCode;
    public ?string $street;
    public string $fullAddress;
    public ?UploadedFile $image;
    public ?bool $imageRemoved;

    public function __construct(array $data)
    {
        $this->name         = $data['name'];
        $this->provinceCode = $data['province_code'];
        $this->districtCode = $data['district_code'] ?? null;
        $this->wardCode     = $data['ward_code'] ?? null;
        $this->street       = $data['street'] ?? null;
        $this->fullAddress  = $data['full_address'];
        $this->image        = $data['image'] ?? null;
        $this->imageRemoved  = !empty($data['image_removed']);
    }

    public static function fromRequest(Request $request): self
    {
        return new self([
            'name'          => $request->input('name'),
            'province_code' => $request->input('province_code'),
            'district_code' => $request->input('district_code'),
            'ward_code'     => $request->input('ward_code'),
            'street'        => $request->input('street'),
            'full_address'  => $request->input('full_address'),
            'image'         => $request->file('image'),
            'image_removed' => $request->input('image_removed'),
        ]);
    }

    public function toUpdateArray(): array
    {
        $data = [
            'name'          => $this->name,
            'province_code' => $this->provinceCode,
            'district_code' => $this->districtCode,
            'ward_code'     => $this->wardCode,
            'street'        => $this->street,
            'full_address'  => $this->fullAddress,
        ];
        if ($this->image instanceof UploadedFile) {
            $data['image'] = $this->image;
        } elseif ($this->imageRemoved) {
            $data['image'] = null;
        }

        return $data;
    }
}
