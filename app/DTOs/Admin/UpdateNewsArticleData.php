<?php

namespace App\DTOs\Admin;

use Illuminate\Http\Request;
use Illuminate\Http\UploadedFile;

class UpdateNewsArticleData
{
    public string $title;
    public ?array $categories;
    public ?string $content;
    public ?UploadedFile $image;
    public ?bool $imageRemoved;
    public ?string $description;
    public ?string $code;
    public ?string $link;
    public ?string $url_target;
    public ?int $position;
    public bool $status;
    public bool $featured;
    public ?string $seo_title;
    public ?string $seo_description;
    public ?string $seo_keywords;
    public ?UploadedFile $seo_image;
    public ?bool $seoImageRemoved;

    public function __construct(array $data)
    {
        $this->title           = $data['title'];
        $this->categories      = $data['categories'] ?? [];
        $this->content         = $data['content'] ?? null;
        $this->image           = $data['image'] ?? null;
        $this->imageRemoved    = !empty($data['image_removed']);
        $this->description     = $data['description'] ?? null;
        $this->code            = $data['code'] ?? null;
        $this->link            = $data['link'] ?? null;
        $this->url_target      = $data['url_target'] ?? null;
        $this->position        = isset($data['position']) ? (int) $data['position'] : null;
        $this->status          = (bool) ($data['status'] ?? 1);
        $this->featured        = (bool) ($data['featured'] ?? 0);
        $this->seo_title       = $data['seo_title'] ?? null;
        $this->seo_description = $data['seo_description'] ?? null;
        $this->seo_keywords    = $data['seo_keywords'] ?? null;
        $this->seo_image       = $data['seo_image'] ?? null;
        $this->seoImageRemoved = !empty($data['seo_image_removed']);
    }

    public static function fromRequest(Request $request): self
    {
        return new self([
            'title'           => $request->input('title'),
            'categories'      => $request->input('categories'),
            'content'         => $request->input('content'),
            'image'           => $request->file('image'),
            'image_removed'   => $request->boolean('image_removed'),
            'description'     => $request->input('description'),
            'code'            => $request->input('code'),
            'link'            => $request->input('link'),
            'url_target'      => $request->input('url_target'),
            'position'        => $request->input('position'),
            'status'          => $request->boolean('status'),
            'featured'        => $request->boolean('featured'),
            'seo_title'       => $request->input('seo_title'),
            'seo_description' => $request->input('seo_description'),
            'seo_keywords'    => $request->input('seo_keywords'),
            'seo_image'       => $request->file('seo_image'),
            'seo_image_removed' => $request->boolean('seo_image_removed')
        ]);
    }

    public function toUpdateArray(): array
    {
        $data = [
            'title'           => $this->title,
            'description'     => $this->description,
            'content'         => $this->content,
            'code'            => $this->code,
            'link'            => $this->link,
            'url_target'      => $this->url_target,
            'position'        => $this->position,
            'status'          => $this->status,
            'featured'        => $this->featured,
            'seo_title'       => $this->seo_title,
            'seo_description' => $this->seo_description,
            'seo_keywords'    => $this->seo_keywords,
        ];
        if ($this->image instanceof UploadedFile) {
            $data['image'] = $this->image;
        } elseif ($this->imageRemoved) {
            $data['image'] = null;
        }
        if ($this->seo_image instanceof UploadedFile) {
            $data['seo_image'] = $this->seo_image;
        } elseif ($this->seoImageRemoved) {
            $data['seo_image'] = null;
        }
        return $data;
    }

    public function getCategoryIds(): array
    {
        return array_map('intval', $this->categories ?? []);
    }
}
