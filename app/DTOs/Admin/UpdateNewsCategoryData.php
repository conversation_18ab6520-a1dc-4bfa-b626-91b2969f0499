<?php

namespace App\DTOs\Admin;

use Illuminate\Http\Request;
use Illuminate\Http\UploadedFile;

class UpdateNewsCategoryData
{
    public string $title;
    public ?int $parents;
    public ?UploadedFile $image;
    public ?bool $imageRemoved;
    public ?string $description;
    public ?string $code;
    public ?int $position;
    public int $status;
    public ?string $seo_title;
    public ?string $seo_description;
    public ?string $seo_keywords;

    public function __construct(array $data)
    {
        $this->title            = $data['title'];
        $this->parents          = isset($data['parents']) ? (int) $data['parents'] : null;
        $this->image            = $data['image'] ?? null;
        $this->imageRemoved  = !empty($data['image_removed']);
        $this->description      = $data['description'] ?? null;
        $this->code             = $data['code'] ?? null;
        $this->position         = isset($data['position']) ? (int) $data['position'] : null;
        $this->status           = isset($data['status']) ? (int) $data['status'] : 1;
        $this->seo_title        = $data['seo_title'] ?? null;
        $this->seo_description  = $data['seo_description'] ?? null;
        $this->seo_keywords     = $data['seo_keywords'] ?? null;
    }

    public static function fromRequest(Request $request): self
    {
        return new self([
            'parents'         => $request->input('parents'),
            'title'           => $request->input('title'),
            'image'           => $request->file('image'),
            'image_removed' => $request->input('image_removed'),
            'description'     => $request->input('description'),
            'code'            => $request->input('code'),
            'position'        => $request->input('position'),
            'status'          => $request->input('status'),
            'seo_title'       => $request->input('seo_title'),
            'seo_description' => $request->input('seo_description'),
            'seo_keywords'    => $request->input('seo_keywords'),
        ]);
    }

    public function toUpdateArray(): array
    {
        $data = [
            'parents'         => $this->parents,
            'title'           => $this->title,
            'description'     => $this->description,
            'code'            => $this->code,
            'position'        => $this->position,
            'status'          => $this->status,
            'seo_title'       => $this->seo_title,
            'seo_description' => $this->seo_description,
            'seo_keywords'    => $this->seo_keywords,
        ];
        if ($this->image instanceof UploadedFile) {
            $data['image'] = $this->image;
        } elseif ($this->imageRemoved) {
            $data['image'] = null;
        }
        return $data;
    }
}
