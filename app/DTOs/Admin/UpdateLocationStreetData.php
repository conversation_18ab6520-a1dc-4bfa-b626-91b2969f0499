<?php

namespace App\DTOs\Admin;

use Illuminate\Http\Request;

class UpdateLocationStreetData
{
    public string  $name;
    public string  $code;
    public ?string  $wardCode;
    public ?string  $districtCode;
    public string  $provinceCode;

    public function __construct(array $data)
    {
        $this->name         = $data['name'];
        $this->code         = $data['code'];
        $this->wardCode         = $data['ward_code'] ?? null;
        $this->districtCode = $data['district_code'] ?? null;
        $this->provinceCode = $data['province_code'];
    }

    public static function fromRequest(Request $request): self
    {
        return new self([
            'name'          => $request->input('name'),
            'code'          => $request->input('code'),
            'ward_code' => $request->input('ward_code'),
            'district_code' => $request->input('district_code'),
            'province_code' => $request->input('province_code'),
        ]);
    }

    public function toUpdateArray(): array
    {
        $data = [
            'name'          => $this->name,
            'code'          => $this->code,
            'ward_code'          => $this->wardCode,
            'district_code' => $this->districtCode,
            'province_code' => $this->provinceCode,
        ];


        return $data;
    }
}
