<?php

namespace App\DTOs\Admin;

use Illuminate\Http\Request;

class UpdateLocationDistrictData
{
    public string  $name;
    public string  $code;
    public string  $type;
    public string  $provinceCode; 

    public function __construct(array $data)
    {
        $this->name         = $data['name'];
        $this->code         = $data['code'];
        $this->type         = $data['type'];
        $this->provinceCode = $data['province_code'];
    }

    public static function fromRequest(Request $request): self
    {
        return new self([
            'name'          => $request->input('name'),
            'code'          => $request->input('code'),
            'type'          => $request->input('type'),
            'province_code' => $request->input('province_code'),
        ]);
    }

    public function toUpdateArray(): array
    {
        $data =[
            'name'          => $this->name,
            'code'          => $this->code,
            'type'          => $this->type,
            'province_code' => $this->provinceCode,
        ];


        return $data;
    }
}