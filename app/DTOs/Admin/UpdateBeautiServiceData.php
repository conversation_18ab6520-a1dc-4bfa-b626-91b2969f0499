<?php

namespace App\DTOs\Admin;

use Illuminate\Http\Request;
use Illuminate\Http\UploadedFile;

class UpdateBeautiServiceData
{
    public string $name;
    public array $types;
    public ?string $slug;
    public ?string $description;
    public ?UploadedFile $image;
    public ?bool $imageRemoved;
    public bool $status;
    public ?int $position;
    public ?string $seoTitle;
    public ?string $seoDescription;
    public ?string $seoKeywords;
    public ?UploadedFile $seoImage;
    public ?bool $seoImageRemoved;



    public function __construct(array $data)
    {
        $this->name            = $data['name'];
        $this->types          = $data['types'] ?? [];
        $this->slug            = $data['slug'] ?? null;
        $this->description     = $data['description'] ?? null;
        $this->image           = $data['image'] ?? null;
        $this->imageRemoved    = !empty($data['image_removed']);
        $this->status          = $data['status'];
        $this->position        = $data['position'] ?? null;
        $this->seoTitle        = $data['seo_title'] ?? null;
        $this->seoDescription  = $data['seo_description'] ?? null;
        $this->seoKeywords     = $data['seo_keywords'] ?? null;
        $this->seoImage        = $data['seo_image'] ?? null;
        $this->seoImageRemoved = !empty($data['seo_image_removed']);
    }


    public static function fromRequest(Request $request): self
    {
        return new self([
            'name'              => $request->input('name'),
            'types'             => $request->input('types', []),
            'slug'              => $request->input('slug'),
            'description'       => $request->input('description'),
            'image'             => $request->file('image'),
            'image_removed'     => $request->boolean('image_removed'),
            'status'            => $request->boolean('status'),
            'position'          => $request->input('position'),
            'seo_title'         => $request->input('seo_title'),
            'seo_description'   => $request->input('seo_description'),
            'seo_keywords'      => $request->input('seo_keywords'),
            'seo_image'         => $request->file('seo_image'),
            'seo_image_removed' => $request->boolean('seo_image_removed'),
        ]);
    }

    public function toUpdateArray(): array
    {
        $data = [
            'name'            => $this->name,
            'slug'            => $this->slug,
            'description'     => $this->description,
            'status'          => $this->status,
            'position'        => $this->position,
            'seo_title'       => $this->seoTitle,
            'seo_description' => $this->seoDescription,
            'seo_keywords'    => $this->seoKeywords,
        ];

        if ($this->image instanceof UploadedFile) {
            $data['image'] = $this->image;
        } elseif ($this->imageRemoved) {
            $data['image'] = null;
        }

        if ($this->seoImage instanceof UploadedFile) {
            $data['seo_image'] = $this->seoImage;
        } elseif ($this->seoImageRemoved) {
            $data['seo_image'] = null;
        }

        return $data;
    }
    
    public function getTypes(): array
    {
        return $this->types;
    }
}
