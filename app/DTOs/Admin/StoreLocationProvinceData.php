<?php

namespace App\DTOs\Admin;

use Illuminate\Http\Request;

class StoreLocationProvinceData
{
    public string  $name;
    public string  $code;
    public string  $type;
    public bool    $status;

    public function __construct(array $data)
    {
        $this->name     = $data['name'];
        $this->code     = $data['code'];
        $this->type     = $data['type'];
        $this->status   = (bool)($data['status'] ?? true);
    }


    public static function fromRequest(Request $request): self
    {
        return new self([
            'name'   => $request->input('name'),
            'code'   => $request->input('code'),
            'type'   => $request->input('type'),
            'status' => $request->boolean('status'),

        ]);
    }

    public function toCreateArray(): array
    {
        $data = array_filter([
            'name'   => $this->name,
            'code'   => $this->code,
            'type'   => $this->type,
        ], fn($value) => $value !== null);
        $data['status'] = $this->status;
        return $data;
    }
}
