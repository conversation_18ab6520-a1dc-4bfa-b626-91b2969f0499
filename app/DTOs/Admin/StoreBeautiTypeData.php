<?php

namespace App\DTOs\Admin;

use Illuminate\Http\Request;
use Illuminate\Http\UploadedFile;

class StoreBeautiTypeData
{
    public string  $name;
    public ?string  $slug;
    public ?UploadedFile $image;
    public ?string $description;
    public bool    $status;
    public bool    $featured;
    public ?int    $position;
    public ?string $seoTitle;
    public ?string $seoDescription;
    public ?string $seoKeywords;
    public ?UploadedFile $seoImage;

    public function __construct(array $data)
    {
        $this->name            = $data['name'];
        $this->slug            = $data['slug'] ?? null;
        $this->image           = $data['image'] ?? null;
        $this->description     = $data['description'] ?? null;
        $this->status          = $data['status'];
        $this->featured          = $data['featured'];
        $this->position        = $data['position'] ?? null;
        $this->seoTitle        = $data['seo_title'] ?? null;
        $this->seoDescription  = $data['seo_description'] ?? null;
        $this->seoKeywords     = $data['seo_keywords'] ?? null;
        $this->seoImage        = $data['seo_image'] ?? null;
    }

    public static function fromRequest(Request $request): self
    {
        return new self([
            'name'            => $request->input('name'),
            'slug'            => $request->input('slug'),
            'image'           => $request->file('image'),
            'description'     => $request->input('description'),
            'status'          => $request->boolean('status'),
            'featured'          => $request->boolean('featured'),
            'position'        => $request->input('position'),
            'seo_title'       => $request->input('seo_title'),
            'seo_description' => $request->input('seo_description'),
            'seo_keywords'    => $request->input('seo_keywords'),
            'seo_image'       => $request->file('seo_image'),
        ]);
    }

    public function toCreateArray(): array
    {
        $data =  array_filter([
            'name'            => $this->name,
            'slug'            => $this->slug,
            'description'     => $this->description,
            'status'          => $this->status,
            'featured'          => $this->featured,
            'position'        => $this->position,
            'seo_title'       => $this->seoTitle,
            'seo_description' => $this->seoDescription,
            'seo_keywords'    => $this->seoKeywords,
        ], fn($value) => $value !== null);

        if ($this->image instanceof UploadedFile) {
            $data['image'] = $this->image;
        }
        if ($this->seoImage instanceof UploadedFile) {
            $data['seo_image'] = $this->seoImage;
        }
        return $data;
    }
}
