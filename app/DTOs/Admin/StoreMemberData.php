<?php
// app/DTO/Admin/StoreMemberData.php
namespace App\DTOs\Admin;

use Illuminate\Http\UploadedFile;
use Illuminate\Http\Request;

class StoreMemberData
{
    public ?string       $firstname;
    public ?string       $lastname;
    public string        $username;
    public ?string       $code;
    public ?int          $departmentId;
    public ?string       $title;
    public ?UploadedFile $avatar;
    public ?string       $email;
    public ?string       $gender;
    public ?string       $color;
    public bool          $status;
    public ?array         $roles;

    public function __construct(array $data)
    {
        $this->firstname     = $data['firstname']      ?? null;
        $this->lastname      = $data['lastname']       ?? null;
        $this->username      = $data['username'];
        $this->code          = $data['code']           ?? null;
        $this->departmentId  = $data['department_id']  ?? null;
        $this->title         = $data['title']          ?? null;
        $this->avatar        = $data['avatar']         ?? null;
        $this->email         = $data['email']          ?? null;
        $this->gender        = $data['gender']         ?? null;
        $this->color         = $data['color']          ?? null;
        $this->status        = (bool) $data['status'];
        $this->roles         = $data['roles']          ?? [];
    }

    public static function fromRequest(Request $request): self
    {
        return new self([
            'firstname'     => $request->input('firstname'),
            'lastname'      => $request->input('lastname'),
            'username'      => $request->input('username'),
            'code'          => $request->input('code'),
            'department_id' => $request->input('department_id'),
            'title'         => $request->input('title'),
            'avatar'        => $request->file('avatar'),
            'email'         => $request->input('email'),
            'gender'        => $request->input('gender'),
            'color'         => $request->input('color'),
            'status'        => $request->boolean('status'),
            'roles'         => $request->input('roles', []),
        ]);
    }

    public function toCreateArray(): array
    {
        $base = array_filter([
            'firstname'     => $this->firstname,
            'lastname'      => $this->lastname,
            'username'      => $this->username,
            'code'          => $this->code,
            'department_id' => $this->departmentId,
            'title'         => $this->title,
            'email'         => $this->email,
            'gender'        => $this->gender,
            'color'         => $this->color,
        ], fn($v) => $v !== null);

        if ($this->avatar instanceof UploadedFile) {
            $base['avatar'] = $this->avatar;
        }
        $base['status'] = $this->status;
        return $base;
    }
}
