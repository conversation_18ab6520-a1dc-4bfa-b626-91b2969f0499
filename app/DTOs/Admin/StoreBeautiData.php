<?php

namespace App\DTOs\Admin;

use Illuminate\Http\Request;
use Illuminate\Http\UploadedFile;

class StoreBeautiData
{
    public string $name;
    public ?int $userId;
    public ?string $address;
    public ?string $phone;
    public ?string $fanpage;
    public ?string $tiktok;
    public ?string $website;
    public ?string $email;
    public ?string $description;
    public bool $status;

    public array $beautiTypes;

    public function __construct(array $data)
    {
        $this->name           = $data['name'];
        $this->userId         = $data['user_id'] ?? null;
        $this->address        = $data['address'] ?? null;
        $this->phone          = $data['phone'] ?? null;
        $this->fanpage        = $data['fanpage'] ?? null;
        $this->tiktok         = $data['tiktok'] ?? null;
        $this->website        = $data['website'] ?? null;
        $this->email          = $data['email'] ?? null;
        $this->description    = $data['description'] ?? null;
        $this->status         = $data['status'];
        $this->beautiTypes    = $data['beauti_types'] ?? [];
    }

    public static function fromRequest(Request $request): self
    {
        return new self([
            'name'            => $request->input('name'),
            'user_id'         => $request->input('user_id'),
            'address'         => $request->input('address'),
            'phone'           => $request->input('phone'),
            'fanpage'         => $request->input('fanpage'),
            'tiktok'          => $request->input('tiktok'),
            'website'         => $request->input('website'),
            'email'           => $request->input('email'),
            'description'     => $request->input('description'),
            'status'          => $request->boolean('status'),
            'beauti_types'    => $request->input('beauti_types', []),
        ]);
    }

  
    public function toCreateArray(): array 
    {
        $data = [
            'name'           => $this->name,
            'user_id'        => $this->userId,
            'address'        => $this->address,
            'phone'          => $this->phone,
            'fanpage'        => $this->fanpage,
            'tiktok'         => $this->tiktok,
            'website'        => $this->website,
            'email'          => $this->email,
            'description'    => $this->description,
            'status'         => $this->status,
        ];
        return $data;
    }

    public function getBeautiTypes(): array
    {
        return $this->beautiTypes;
    }
}