<?php
// app/DTO/Admin/UpdateMemberData.php
namespace App\DTOs\Admin;

use App\Models\ErpMember;
use Illuminate\Http\UploadedFile;
use Illuminate\Http\Request;

class UpdateMemberData
{
    public ?string       $firstname;
    public ?string       $lastname;
    public ?string       $username;
    public ?string       $code;
    public ?int          $departmentId;
    public ?string       $title;
    public ?UploadedFile $avatar;
    public bool          $avatarRemoved;
    public ?string       $email;
    public ?string       $gender;
    public ?string       $color;
    public ?bool         $status;
    public ?array        $roles;

    public function __construct(array $data)
    {
        $this->firstname      = $data['firstname']      ?? null;
        $this->lastname       = $data['lastname']       ?? null;
        $this->username       = $data['username']       ?? null;
        $this->code           = $data['code']           ?? null;
        $this->departmentId   = $data['department_id']  ?? null;
        $this->title          = $data['title']          ?? null;
        $this->avatar         = $data['avatar']         ?? null;
        $this->avatarRemoved  = !empty($data['avatar_removed']);
        $this->email          = $data['email']          ?? null;
        $this->gender         = $data['gender']         ?? null;
        $this->color          = $data['color']          ?? null;
        $this->status         = isset($data['status']) ? (bool) $data['status'] : null;
        $this->roles          = $data['roles']          ?? null;
    }

    public static function fromRequest(Request $request): self
    {
        return new self([
            'firstname'      => $request->input('firstname'),
            'lastname'       => $request->input('lastname'),
            'username'       => $request->input('username'),
            'code'           => $request->input('code'),
            'department_id'  => $request->input('department_id'),
            'title'          => $request->input('title'),
            'avatar'         => $request->file('avatar'),
            'avatar_removed' => $request->input('avatar_removed'),
            'email'          => $request->input('email'),
            'gender'         => $request->input('gender'),
            'color'          => $request->input('color'),
            'status'         => $request->has('status') ? $request->boolean('status') : null,
            'roles'          => $request->input('roles', null),
        ]);
    }

    public function toUpdateArray(): array
    {
        $base = [
            'firstname'     => $this->firstname,
            'lastname'      => $this->lastname,
            'username'      => $this->username,
            'code'          => $this->code,
            'department_id' => $this->departmentId,
            'title'         => $this->title,
            'email'         => $this->email,
            'gender'        => $this->gender,
            'color'         => $this->color,
            'status'        => $this->status
        ];

        if ($this->avatar instanceof UploadedFile) {
            $base['avatar'] = $this->avatar;
        } elseif ($this->avatarRemoved) {
            $base['avatar'] = null;
        }

        return $base;
    }
}
