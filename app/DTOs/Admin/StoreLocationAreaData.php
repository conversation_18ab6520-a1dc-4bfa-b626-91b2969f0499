<?php

namespace App\DTOs\Admin;

use Illuminate\Http\Request;
use Illuminate\Http\UploadedFile;

class StoreLocationAreaData
{
    public string $name;
    public string $provinceCode;
    public string $districtCode;
    public ?string $wardCode;
    public ?string $street;
    public string $fullAddress;
    public ?UploadedFile $image;

    public function __construct(array $data)
    {
        $this->name         = $data['name'];
        $this->provinceCode = $data['province_code'];
        $this->districtCode = $data['district_code'];
        $this->wardCode     = $data['ward_code'] ?? null;
        $this->street       = $data['street'] ?? null;
        $this->fullAddress  = $data['full_address'];
        $this->image        = $data['image'] ?? null;
    }

    public static function fromRequest(Request $request): self
    {
        return new self([
            'name'          => $request->input('name'),
            'province_code' => $request->input('province_code'),
            'district_code' => $request->input('district_code'),
            'ward_code'     => $request->input('ward_code'),
            'street'        => $request->input('street'),
            'full_address'  => $request->input('full_address'),
            'image'         => $request->file('image'),
        ]);
    }

    public function toCreateArray(): array
    {
        $data = array_filter([
            'name'          => $this->name,
            'province_code' => $this->provinceCode,
            'district_code' => $this->districtCode,
            'ward_code'     => $this->wardCode,
            'street'        => $this->street,
            'full_address'  => $this->fullAddress,
        ], fn($value) => $value !== null);
        if ($this->image instanceof UploadedFile) {
            $data['image'] = $this->image;
        }
        return $data;
    }
}
