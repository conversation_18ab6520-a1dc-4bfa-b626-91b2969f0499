<?php

namespace App\DTOs\Admin;

use Illuminate\Http\Request;

class StoreTeamData
{
    public string  $name;
    public ?string $code;
    public ?int    $managerId; 
    public ?string $content;
    public ?string $color;
    public ?string $telegram;
    public bool    $status;
    public ?int    $creatorId; 
    public ?int    $editorId;  
    public ?int    $position;

    public function __construct(array $data)
    {
        $this->name      = $data['name'];
        $this->code      = $data['code']       ?? null;
        $this->managerId = $data['manager_id'] ?? null;
        $this->color     = $data['color']      ?? null;
        $this->telegram  = $data['telegram']   ?? null;
        $this->status    = (bool)($data['status'] ?? false); 
        $this->creatorId = $data['creator_id'] ?? null;
        $this->editorId  = $data['editor_id']  ?? null;
        $this->position  = $data['position']   ?? null;
    }

 
    public static function fromRequest(Request $request): self
    {
        return new self([
            'name'         => $request->input('name'),
            'code'         => $request->input('code'),
            'manager_id'   => $request->input('manager_id'),
            'color'        => $request->input('color'),
            'telegram'     => $request->input('telegram'),
            'status'       => $request->boolean('status'),
            'position'     => $request->input('position'),
        ]);
    }

   
    public function toCreateArray(): array
    {
        $base = array_filter([
            'name'         => $this->name,
            'code'         => $this->code,
            'manager_id'   => $this->managerId,
            'color'        => $this->color,
            'telegram'     => $this->telegram,
            'creator_id'   => $this->creatorId,
            'editor_id'    => $this->editorId,
            'position'     => $this->position,
        ], fn($v) => $v !== null);

        $base['status'] = $this->status;

        return $base;
    }
}