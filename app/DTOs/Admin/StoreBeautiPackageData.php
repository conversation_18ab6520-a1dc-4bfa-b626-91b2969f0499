<?php

namespace App\DTOs\Admin;

use Illuminate\Http\Request;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Str;

class StoreBeautiPackageData
{
    public int $storeId;
    public int $serviceId;
    public string $name;
    public ?string $slug;
    public ?int $durationDays;
    public ?int $warrantyDays;
    public ?int $priceOld;
    public ?int $priceNew;
    public ?string $description;
    public bool $status;
    public ?string $seoTitle;
    public ?string $seoDescription;
    public ?string $seoKeywords;
    public ?UploadedFile $seoImage;


    public function __construct(array $data)
    {
        $this->storeId = (int) $data['store_id'];
        $this->serviceId = (int) $data['service_id'];
        $this->name           = $data['name'];
        $this->slug           = $data['slug'] ?? null;
        $this->durationDays   = isset($data['duration_days']) ? (int) $data['duration_days'] : null;
        $this->warrantyDays   = isset($data['warranty_days']) ? (int) $data['warranty_days'] : null;
        $this->priceOld          = isset($data['price_old']) ? (int) $data['price_old'] : null;
        $this->priceNew          = isset($data['price_new']) ? (int) $data['price_new'] : null;
        $this->description    = $data['description'] ?? null;
        $this->status         = (bool) $data['status'];
        $this->seoTitle       = $data['seo_title'] ?? null;
        $this->seoDescription = $data['seo_description'] ?? null;
        $this->seoKeywords    = $data['seo_keywords'] ?? null;
        $this->seoImage       = $data['seo_image'] ?? null;
    }


    public static function fromRequest(Request $request): self
    {
        return new self([
            'store_id' => $request->input('store_id'),
            'service_id' => $request->input('service_id'),
            'name'             => $request->input('name'),
            'slug'             => $request->input('slug'),
            'duration_days'    => $request->input('duration_days'),
            'warranty_days'    => $request->input('warranty_days'),
            'price_old'            => $request->input('price_old'),
            'price_new'            => $request->input('price_new'),
            'description'      => $request->input('description'),
            'status'           => $request->boolean('status'),
            'seo_title'        => $request->input('seo_title'),
            'seo_description'  => $request->input('seo_description'),
            'seo_keywords'     => $request->input('seo_keywords'),
            'seo_image'        => $request->file('seo_image'),
        ]);
    }

    /**
     * Prepare array for creating or updating model
     *
     * @return array
     */
    public function toCreateArray(): array
    {
        $data = [
            'store_id' => $this->storeId,
            'service_id' => $this->serviceId,
            'name'             => $this->name,
            'slug'             => $this->slug,
            'duration_days'    => $this->durationDays,
            'warranty_days'    => $this->warrantyDays,
            'price_old'            => $this->priceOld,
            'price_new'            => $this->priceNew,
            'description'      => $this->description,
            'status'           => $this->status,
            'seo_title'        => $this->seoTitle,
            'seo_description'  => $this->seoDescription,
            'seo_keywords'     => $this->seoKeywords,
        ];

        if ($this->seoImage instanceof UploadedFile) {
            $data['seo_image'] = $this->seoImage;
        }

        return $data;
    }
}
