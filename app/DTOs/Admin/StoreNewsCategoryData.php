<?php

namespace App\DTOs\Admin;

use Illuminate\Http\Request;
use Illuminate\Http\UploadedFile;

class StoreNewsCategoryData  
{
    public string $title;
    public ?int $parents;
    public ?UploadedFile $image;
    public ?string $description;
    public ?string $code;
    public ?int $position;
    public bool $status;
    public ?string $seo_title;
    public ?string $seo_description;
    public ?string $seo_keywords;

    public function __construct(array $data)
    {
        $this->title           = $data['title'];
        $this->parents         = $data['parents'] ?? null;
        $this->image           = $data['image'] ?? null;
        $this->description     = $data['description'] ?? null;
        $this->code            = $data['code'] ?? null;
        $this->position        = isset($data['position']) ? (int) $data['position'] : null;
        $this->status        = (bool) $data['status'];
        $this->seo_title       = $data['seo_title'] ?? null;
        $this->seo_description = $data['seo_description'] ?? null;
        $this->seo_keywords    = $data['seo_keywords'] ?? null;
    }

    public static function fromRequest(Request $request): self
    {
        return new self([
            'title'           => $request->input('title'),
            'parents'         => $request->input('parents'),
            'image'           => $request->file('image'),
            'description'     => $request->input('description'),
            'code'            => $request->input('code'),
            'position'        => $request->input('position'),
            'status'          => $request->boolean('status'),
            'seo_title'       => $request->input('seo_title'),
            'seo_description' => $request->input('seo_description'),
            'seo_keywords'    => $request->input('seo_keywords'),
        ]);
    }

    public function toCreateArray(): array
    {
        $data = array_filter([
            'title'           => $this->title,
            'parents'         => $this->parents,
            'description'     => $this->description,
            'code'            => $this->code,
            'position'        => $this->position,
            'status'          => $this->status,
            'seo_title'       => $this->seo_title,
            'seo_description' => $this->seo_description,
            'seo_keywords'    => $this->seo_keywords,
        ], fn($value) => $value !== null);
        if ($this->image instanceof UploadedFile) {
            $data['image'] = $this->image;
        }
        return $data;
    }
}
