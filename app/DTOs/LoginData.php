<?php

namespace App\DTOs;

use Illuminate\Contracts\Support\Arrayable;

class LoginData implements Arrayable
{
  public string $identifier;
  public string $password;


  public function __construct(string $identifier, string $password)
  {
    $this->identifier = trim(strtolower($identifier));
    $this->password   = $password;
  }

  public function toArray(): array
  {
    return [
      'identifier' => $this->identifier,
      'password'   => $this->password,
    ];
  }
}
