<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class VerifyEmail extends Mailable
{
  use Queueable, SerializesModels;

  public $otpCode;
  public $recipientName;

  /**
   * Create a new message instance.
   */
  public function __construct($otpCode, $recipientName = null)
  {
    $this->otpCode       = $otpCode;
    $this->recipientName = $recipientName;
  }

  /**
   * Get the message envelope.
   */
  public function envelope(): Envelope
  {
    return new Envelope(
      subject: 'Mã OTP xác thực tài kho<PERSON>n',
    );
  }

  /**
   * Get the message content definition.
   */
  public function content(): Content
  {
    return new Content(
      view: 'emails.verify-email',
      with: [
        'otp_code'        => $this->otpCode,
        'recipient_name'  => $this->recipientName,
      ],
    );
  }

  /**
   * Get the attachments for the message.
   *
   * @return array<int, \Illuminate\Mail\Mailables\Attachment>
   */
  public function attachments(): array
  {
    return [];
  }
}
