<?php

namespace App\Rules;

use Closure;
use Illuminate\Contracts\Validation\ValidationRule;

class VietnamPhone implements ValidationRule
{
    protected $type;

    public function __construct(string $type = 'all')
    {
        $this->type = $type; // 'mobile', 'landline', 'all'
    }

    /**
     * Run the validation rule.
     *
     * @param  \Closure(string, ?string=): \Illuminate\Translation\PotentiallyTranslatedString  $fail
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        if (empty($value)) {
            return;
        }

        $pattern = config("validation.regex.phone.{$this->type}");
        $message = config("validation.messages.phone.{$this->type}");

        if (!$pattern || !preg_match($pattern, $value)) {
            $fail($message ?: 'Số điện thoại không đúng định dạng');
        }
    }
}
