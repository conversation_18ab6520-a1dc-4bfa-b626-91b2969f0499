<?php

namespace App\Rules;

use Closure;
use Illuminate\Contracts\Validation\ValidationRule;

class CustomEmail implements ValidationRule
{
    /**
     * Run the validation rule.
     *
     * @param  \Closure(string, ?string=): \Illuminate\Translation\PotentiallyTranslatedString  $fail
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        if (empty($value)) {
            return;
        }

        $pattern = config('validation.regex.email');
        $message = config('validation.messages.email');

        if (!$pattern || !preg_match($pattern, $value)) {
            $fail($message ?: 'Email không đúng định dạng');
        }
    }
}
