<?php

namespace App\Repositories\Admin;

use App\Models\Beauti;
use App\Models\BeautiStorePackage;
use App\Models\BeautiType;
use App\Repositories\Admin\Interfaces\BeautiPackageRepositoryInterface;

class BeautiPackageRepository implements BeautiPackageRepositoryInterface
{
 
    public function all()
    {
     return BeautiStorePackage::with('creator','service','store','store.beauties','storeService');
    }

    public function find(int $id): ?BeautiStorePackage
    {
        return BeautiStorePackage::find($id);
    }

    public function findOrFail(int $id): BeautiStorePackage
    {
        return BeautiStorePackage::findOrFail($id);
    }

    public function create(array $data): BeautiStorePackage
    {
        return BeautiStorePackage::create($data);
    }

    public function update(BeautiStorePackage $beautiStorePackage, array $data): BeautiStorePackage
    {
        $beautiStorePackage->update($data);
        return $beautiStorePackage;
    }

    public function delete(BeautiStorePackage $beautiStorePackage): ?bool
    {
        return $beautiStorePackage->delete();
    }

}