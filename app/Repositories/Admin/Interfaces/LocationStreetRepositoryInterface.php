<?php

namespace App\Repositories\Admin\Interfaces;

use App\Models\LocationStreet;

interface LocationStreetRepositoryInterface
{
    public function all();
    public function find(int $id): ?LocationStreet;
    public function findOrFail(int $id): LocationStreet;
    public function create(array $data): LocationStreet;
    public function update(LocationStreet $locationStreet, array $data): LocationStreet;
    public function delete(LocationStreet $locationStreet): ?bool;
}
