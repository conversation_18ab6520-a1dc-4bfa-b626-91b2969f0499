<?php

namespace App\Repositories\Admin\Interfaces;

use App\Models\LocationArea;

interface LocationAreaRepositoryInterface
{
    public function all();
    public function find(int $id): ?LocationArea;
    public function findOrFail(int $id): LocationArea;
    public function create(array $data): LocationArea;
    public function update(LocationArea $locationArea, array $data): LocationArea;
    public function delete(LocationArea $locationArea): ?bool;
}
