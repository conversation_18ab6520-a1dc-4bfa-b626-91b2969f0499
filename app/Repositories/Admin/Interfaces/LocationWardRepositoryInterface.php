<?php

namespace App\Repositories\Admin\Interfaces;

use App\Models\LocationWard;

interface LocationWardRepositoryInterface
{
    public function all();
    public function find(int $id): ?LocationWard;
    public function findOrFail(int $id): LocationWard;
    public function create(array $data): LocationWard;
    public function update(LocationWard $locationWard, array $data): LocationWard;
    public function delete(LocationWard $locationWard): ?bool;
}
