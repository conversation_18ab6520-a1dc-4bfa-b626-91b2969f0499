<?php

namespace App\Repositories\Admin\Interfaces;

use App\Models\ErpMember;

interface MemberRepositoryInterface
{
    public function all();
    public function find(int $id): ?ErpMember;
    public function findOrFail(int $id): ErpMember;
    public function findByUsername(string $username): ?ErpMember;
    public function create(array $data): ErpMember;
    public function update(int $id, array $data): ErpMember;
    public function delete(ErpMember $member): ?bool;
}
