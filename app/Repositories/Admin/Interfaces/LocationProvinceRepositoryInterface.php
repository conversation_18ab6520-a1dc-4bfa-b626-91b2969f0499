<?php

namespace App\Repositories\Admin\Interfaces;

use App\Models\LocationProvince;

interface LocationProvinceRepositoryInterface
{
    public function all();
    public function find(int $id): ?LocationProvince;
    public function findOrFail(int $id): LocationProvince;
    public function create(array $data): LocationProvince;
    public function update(LocationProvince $locationProvince, array $data): LocationProvince;
    public function delete(LocationProvince $locationProvince): ?bool;
}
