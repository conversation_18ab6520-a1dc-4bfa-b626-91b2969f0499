<?php

namespace App\Repositories\Admin\Interfaces;

use App\Models\BeautiStore;

interface BeautiStoreRepositoryInterface
{
    public function all();
    public function find(int $id): ?BeautiStore;
    public function findOrFail(int $id): BeautiStore;
    public function create(array $data): BeautiStore;
    public function update(BeautiStore $beautiStore, array $data): BeautiStore;
    public function delete(BeautiStore $beautiStore): ?bool;
}
