<?php

namespace App\Repositories\Admin\Interfaces;

use App\Models\NewsCategory;

interface NewsCategoryRepositoryInterface
{
    public function all();
    public function find(int $id): ?NewsCategory;
    public function findOrFail(int $id): NewsCategory;
    public function create(array $data): NewsCategory;
    public function update(NewsCategory $newsCategory, array $data): NewsCategory;
    public function delete(NewsCategory $newsCategory): ?bool;
}
