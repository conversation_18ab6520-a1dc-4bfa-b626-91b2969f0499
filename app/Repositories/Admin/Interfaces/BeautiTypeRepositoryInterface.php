<?php

namespace App\Repositories\Admin\Interfaces;

use App\Models\BeautiType;

interface BeautiTypeRepositoryInterface
{
    public function all();
    public function find(int $id): ?BeautiType;
    public function findOrFail(int $id): BeautiType;
    public function create(array $data): BeautiType;
    public function update(BeautiType $beautiType, array $data): BeautiType;
    public function delete(BeautiType $beautiType): ?bool;
}
