<?php

namespace App\Repositories\Admin\Interfaces;

use App\Models\BeautiStorePackage;

interface BeautiPackageRepositoryInterface
{
    public function all();
    public function find(int $id): ?BeautiStorePackage;
    public function findOrFail(int $id): BeautiStorePackage;
    public function create(array $data): BeautiStorePackage;
    public function update(BeautiStorePackage $beautiPackage, array $data): BeautiStorePackage;
    public function delete(BeautiStorePackage $beautiPackage): ?bool;
}
