<?php

namespace App\Repositories\Admin\Interfaces;

use App\Models\BeautiService;

interface BeautiServiceRepositoryInterface
{
    public function all();
    public function find(int $id): ?BeautiService;
    public function findOrFail(int $id): BeautiService;
    public function create(array $data): BeautiService;
    public function update(BeautiService $beautiService, array $data): BeautiService;
    public function delete(BeautiService $beautiService): ?bool;
}
