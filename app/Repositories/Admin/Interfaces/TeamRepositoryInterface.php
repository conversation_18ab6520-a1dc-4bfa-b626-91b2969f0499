<?php

namespace App\Repositories\Admin\Interfaces;

use App\Models\ErpDepartment;

interface TeamRepositoryInterface
{
    public function all();
    public function find(int $id): ?ErpDepartment;
    public function findOrFail(int $id): ErpDepartment;
    public function create(array $data): ErpDepartment;
    public function update(ErpDepartment $department, array $data): ErpDepartment;
    public function delete(ErpDepartment $department): ?bool;
}
