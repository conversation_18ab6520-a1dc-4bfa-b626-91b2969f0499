<?php

namespace App\Repositories\Admin\Interfaces;

use App\Models\Beauti;

interface BeautiRepositoryInterface
{
    public function all();
    public function find(int $id): ?Beauti;
    public function findOrFail(int $id): <PERSON><PERSON>;
    public function create(array $data): <PERSON><PERSON>;
    public function update(Beauti $beauti, array $data): <PERSON><PERSON>;
    public function delete(Beauti $beauti): ?bool;
}
