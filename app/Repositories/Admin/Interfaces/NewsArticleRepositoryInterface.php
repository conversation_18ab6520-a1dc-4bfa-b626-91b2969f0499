<?php

namespace App\Repositories\Admin\Interfaces;

use App\Models\NewsArticle;

interface NewsArticleRepositoryInterface
{
    public function all();
    public function find(int $id): ?NewsArticle;
    public function findOrFail(int $id): NewsArticle;
    public function create(array $data): NewsArticle;
    public function update(NewsArticle $newsArticle, array $data): NewsArticle;
    public function delete(NewsArticle $newsArticle): ?bool;
}
