<?php

namespace App\Repositories\Admin\Interfaces;

use App\Models\LocationDistrict;

interface LocationDistrictRepositoryInterface
{
    public function all();
    public function find(int $id): ?LocationDistrict;
    public function findOrFail(int $id): LocationDistrict;
    public function create(array $data): LocationDistrict;
    public function update(LocationDistrict $locationDistrict, array $data): LocationDistrict;
    public function delete(LocationDistrict $locationDistrict): ?bool;
}
