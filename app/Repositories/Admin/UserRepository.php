<?php

namespace App\Repositories\Admin;

use App\Models\ErpDepartment;
use App\Models\User;
use App\Repositories\Admin\Interfaces\TeamRepositoryInterface;
use App\Repositories\Admin\Interfaces\UserRepositoryInterface;

class UserRepository implements UserRepositoryInterface
{
 
    public function all()
    {
         return User::with(['creator','supporter']);
    }

    public function find(int $id): ?User
    {
        return User::find($id);
    }

    public function findOrFail(int $id): User
    {
        return User::findOrFail($id);
    }

    public function create(array $data): User
    {
        return User::create($data);
    }

    public function update(User $user, array $data): User
    {
        $user->update($data);
        return $user;
    }

    public function delete(User $user): ?bool
    {
        return $user->delete();
    }

}