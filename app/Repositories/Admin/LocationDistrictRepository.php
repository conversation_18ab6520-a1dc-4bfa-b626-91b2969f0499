<?php

namespace App\Repositories\Admin;

use App\Models\LocationDistrict;
use App\Repositories\Admin\Interfaces\LocationDistrictRepositoryInterface;

class LocationDistrictRepository implements LocationDistrictRepositoryInterface
{
 
    public function all()
    {
     return LocationDistrict::with('province');
    }

    public function find(int $id): ?LocationDistrict
    {
        return LocationDistrict::find($id);
    }

    public function findOrFail(int $id): LocationDistrict
    {
        return LocationDistrict::findOrFail($id);
    }

    public function create(array $data): LocationDistrict
    {
        return LocationDistrict::create($data);
    }

    public function update(LocationDistrict $locationDistrict, array $data): LocationDistrict
    {
        $locationDistrict->update($data);
        return $locationDistrict;
    }

    public function delete(LocationDistrict $locationDistrict): ?bool
    {
        return $locationDistrict->delete();
    }

}