<?php

namespace App\Repositories\Admin;

use App\Models\BeautiType;
use App\Models\LocationProvince;
use App\Repositories\Admin\Interfaces\BeautiTypeRepositoryInterface;
use App\Repositories\Admin\Interfaces\LocationProvinceRepositoryInterface;
use App\Repositories\Admin\Interfaces\LocationProvinceServiceInterface;
use App\Repositories\Admin\Interfaces\TeamRepositoryInterface;

class BeautiTypeRepository implements BeautiTypeRepositoryInterface
{
 
    public function all()
    {
     return BeautiType::with('creator');
    }

    public function find(int $id): ?BeautiType
    {
        return BeautiType::find($id);
    }

    public function findOrFail(int $id): BeautiType
    {
        return BeautiType::findOrFail($id);
    }

    public function create(array $data): BeautiType
    {
        return BeautiType::create($data);
    }

    public function update(BeautiType $beautiType, array $data): BeautiType
    {
        $beautiType->update($data);
        return $beautiType;
    }

    public function delete(BeautiType $beautiType): ?bool
    {
        return $beautiType->delete();
    }

}