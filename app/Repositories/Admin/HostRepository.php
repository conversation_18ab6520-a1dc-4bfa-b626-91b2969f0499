<?php

namespace App\Repositories\Admin;

use App\Models\Host;
use App\Repositories\Admin\Interfaces\HostRepositoryInterface;

class HostRepository implements HostRepositoryInterface
{
 
    public function all()
    {
      return Host::with(['creator','inCharge'])
                ->select('host.*');
            
    }

    public function find(int $id): ?Host
    {
        return Host::find($id);
    }

    public function findOrFail(int $id): Host
    {
      return Host::with(['creator','inCharge'])->findOrFail($id);
    }

    public function create(array $data): Host
    {
        return Host::create($data);
    }

    public function update(Host $host, array $data): Host
    {
        $host->update($data);
        return $host;
    }

    public function delete(Host $host): ?bool
    {
        return $host->delete();
    }

}