<?php

namespace App\Repositories\Admin;

use App\Models\ErpDepartment;
use App\Repositories\Admin\Interfaces\TeamRepositoryInterface;

class TeamRepository implements TeamRepositoryInterface
{
 
    public function all()
    {
         return ErpDepartment::with('manager');
    }

    public function find(int $id): ?ErpDepartment
    {
        return ErpDepartment::find($id);
    }

    public function findOrFail(int $id): ErpDepartment
    {
        return ErpDepartment::findOrFail($id);
    }

    public function create(array $data): ErpDepartment
    {
        return ErpDepartment::create($data);
    }

    public function update(ErpDepartment $department, array $data): ErpDepartment
    {
        $department->update($data);
        return $department;
    }

    public function delete(ErpDepartment $department): ?bool
    {
        return $department->delete();
    }

}