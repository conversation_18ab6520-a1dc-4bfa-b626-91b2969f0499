<?php

namespace App\Repositories\Admin;

use App\Models\SystemConfig;
use App\Repositories\Admin\Interfaces\SystemConfigRepositoryInterface;

class SystemConfigRepository implements SystemConfigRepositoryInterface
{

    public function all()
    {
        return SystemConfig::with(['creator', 'editor']);
    }

    public function find(int $id): ?SystemConfig
    {
        return SystemConfig::find($id);
    }

    public function findOrFail(int $id): SystemConfig
    {
        return SystemConfig::findOrFail($id);
    }

    public function update(SystemConfig $systemconfig, array $data): SystemConfig
    {
        $systemconfig->update($data);
        return $systemconfig;
    }
 
}
