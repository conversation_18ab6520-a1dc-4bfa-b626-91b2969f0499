<?php

namespace App\Repositories\Admin;

use App\Models\LocationArea;
use App\Repositories\Admin\Interfaces\LocationAreaRepositoryInterface;
use App\Repositories\Admin\Interfaces\LocationStreetRepositoryInterface;

class LocationAreaRepository implements LocationAreaRepositoryInterface
{

    public function all()
    {
        return LocationArea::with(['province', 'district', 'ward','street']);
    }

    public function find(int $id): ?LocationArea
    {
        return LocationArea::find($id);
    }

    public function findOrFail(int $id): LocationArea
    {
        return LocationArea::findOrFail($id);
    }

    public function create(array $data): LocationArea
    {
        return LocationArea::create($data);
    }

    public function update(LocationArea $locationArea, array $data): LocationArea
    {
        $locationArea->update($data);
        return $locationArea;
    }

    public function delete(LocationArea $locationArea): ?bool
    {
        return $locationArea->delete();
    }
}
