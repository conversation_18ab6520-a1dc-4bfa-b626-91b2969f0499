<?php

namespace App\Repositories\Admin;

use App\Models\BeautiStore;
use App\Repositories\Admin\Interfaces\BeautiStoreRepositoryInterface;

class BeautiStoreRepository implements BeautiStoreRepositoryInterface
{
 
    public function all()
    {
     return BeautiStore::with('creator','beauties','province');
    }

    public function find(int $id): ?BeautiStore
    {
        return BeautiStore::find($id);
    }

    public function findOrFail(int $id): BeautiStore
    {
        return BeautiStore::findOrFail($id);
    }

    public function create(array $data): BeautiStore
    {
        return BeautiStore::create($data);
    }

    public function update(BeautiStore $beautiStore, array $data): BeautiStore
    {
        $beautiStore->update($data);
        return $beautiStore;
    }

    public function delete(BeautiStore $beautiStore): ?bool
    {
        return $beautiStore->delete();
    }

}