<?php

namespace App\Repositories\Admin;

use App\Models\LocationStreet;
use App\Repositories\Admin\Interfaces\LocationStreetRepositoryInterface;

class LocationStreetRepository implements LocationStreetRepositoryInterface
{

    public function all()
    {
        return LocationStreet::with(['province', 'district', 'ward']);
    }

    public function find(int $id): ?LocationStreet
    {
        return LocationStreet::find($id);
    }

    public function findOrFail(int $id): LocationStreet
    {
        return LocationStreet::findOrFail($id);
    }

    public function create(array $data): LocationStreet
    {
        return LocationStreet::create($data);
    }

    public function update(LocationStreet $locationstreet, array $data): LocationStreet
    {
        $locationstreet->update($data);
        return $locationstreet;
    }

    public function delete(LocationStreet $locationstreet): ?bool
    {
        return $locationstreet->delete();
    }
}
