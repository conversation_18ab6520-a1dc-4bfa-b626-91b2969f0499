<?php

namespace App\Repositories\Admin;

use App\Models\Beauti;
use App\Models\BeautiType;
use App\Repositories\Admin\Interfaces\BeautiRepositoryInterface;

class BeautiRepository implements BeautiRepositoryInterface
{
 
    public function all()
    {
     return Beauti::with('creator','types');
    }

    public function find(int $id): ?Beauti
    {
        return Beauti::find($id);
    }

    public function findOrFail(int $id): <PERSON><PERSON>
    {
        return Beauti::findOrFail($id);
    }

    public function create(array $data): <PERSON><PERSON>
    {
        return Beauti::create($data);
    }

    public function update(Beauti $beauti, array $data): <PERSON><PERSON>
    {
        $beauti->update($data);
        return $beauti;
    }

    public function delete(Beauti $beauti): ?bool
    {
        return $beauti->delete();
    }

}