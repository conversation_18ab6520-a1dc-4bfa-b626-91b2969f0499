<?php

namespace App\Repositories\Admin;

use App\Models\NewsArticle;
use App\Repositories\Admin\Interfaces\NewsArticleRepositoryInterface;

class NewsArticleRepository implements NewsArticleRepositoryInterface
{
 
    public function all()
    {
      return NewsArticle::with(['creator','primaryCategory','categories'])
                ->select('news_articles.*');
            
    }

    public function find(int $id): ?NewsArticle
    {
        return NewsArticle::find($id);
    }

    public function findOrFail(int $id): NewsArticle
    {
      return NewsArticle::with(['creator', 'primaryCategory', 'categories'])->findOrFail($id);
    }

    public function create(array $data): NewsArticle
    {
        return NewsArticle::create($data);
    }

    public function update(NewsArticle $newsArticle, array $data): NewsArticle
    {
        $newsArticle->update($data);
        return $newsArticle;
    }

    public function delete(NewsArticle $newsArticle): ?bool
    {
        return $newsArticle->delete();
    }

}