<?php

namespace App\Repositories\Admin;

use App\Models\ErpMember;
use App\Repositories\Admin\Interfaces\MemberRepositoryInterface;
use Illuminate\Support\Facades\Auth;

class MemberRepository implements MemberRepositoryInterface
{
    public function all()
    {
        return ErpMember::with('department');
    }

    public function find(int $id): ?ErpMember
    {
        return ErpMember::with('department')->find($id);
    }

    public function findOrFail(int $id): ErpMember
    {
        return ErpMember::with('department')->findOrFail($id);
    }

    public function findByUsername(string $username): ?ErpMember
    {
        return ErpMember::where('username', $username)->first();
    }

    public function create(array $data): ErpMember
    {
        return ErpMember::create($data);
    }

    public function update(int $id, array $data): ErpMember
    {
        $member = ErpMember::findOrFail($id);
        $member->update($data);
        return $member;
    }

    public function delete(ErpMember $member): bool
    {
        $loggedInUserId = Auth::guard('admin')->id();
        if ($loggedInUserId == $member->id) {
            throw new \Exception('Bạn không thể tự xóa tài khoản đang đăng nhập.');
        }
        return $member->delete();
    }
}
