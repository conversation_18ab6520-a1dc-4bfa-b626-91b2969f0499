<?php

namespace App\Repositories\Admin;

use App\Models\BeautiService;
use App\Repositories\Admin\Interfaces\BeautiServiceRepositoryInterface;

class BeautiServiceRepository implements BeautiServiceRepositoryInterface
{

    public function all()
    {
        return BeautiService::with('creator', 'types');
    }

    public function find(int $id): ?BeautiService
    {
        return BeautiService::find($id);
    }

    public function findOrFail(int $id): BeautiService
    {
        return BeautiService::findOrFail($id);
    }

    public function create(array $data): BeautiService
    {
        return BeautiService::create($data);
    }

    public function update(BeautiService $beautiServie, array $data): BeautiService
    {
        $beautiServie->update($data);
        return $beautiServie;
    }

    public function delete(BeautiService $beautiServie): ?bool
    {
        return $beautiServie->delete();
    }
}
