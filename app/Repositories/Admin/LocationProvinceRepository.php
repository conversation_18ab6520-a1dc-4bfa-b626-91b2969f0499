<?php

namespace App\Repositories\Admin;

use App\Models\LocationProvince;
use App\Repositories\Admin\Interfaces\LocationProvinceRepositoryInterface;
use App\Repositories\Admin\Interfaces\LocationProvinceServiceInterface;
use App\Repositories\Admin\Interfaces\TeamRepositoryInterface;

class LocationProvinceRepository implements LocationProvinceRepositoryInterface
{
 
    public function all()
    {
     return LocationProvince::query();
    }

    public function find(int $id): ?LocationProvince
    {
        return LocationProvince::find($id);
    }

    public function findOrFail(int $id): LocationProvince
    {
        return LocationProvince::findOrFail($id);
    }

    public function create(array $data): LocationProvince
    {
        return LocationProvince::create($data);
    }

    public function update(LocationProvince $locationProvince, array $data): LocationProvince
    {
        $locationProvince->update($data);
        return $locationProvince;
    }

    public function delete(LocationProvince $locationProvince): ?bool
    {
        return $locationProvince->delete();
    }

}