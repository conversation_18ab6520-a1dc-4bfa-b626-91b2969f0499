<?php

namespace App\Repositories\Admin;

use App\Models\LocationWard;
use App\Repositories\Admin\Interfaces\LocationWardRepositoryInterface;

class LocationWardRepository implements LocationWardRepositoryInterface
{
 
    public function all()
    {
     return LocationWard::with('district');
    }

    public function find(int $id): ?LocationWard
    {
        return LocationWard::find($id);
    }

    public function findOrFail(int $id): LocationWard
    {
        return LocationWard::findOrFail($id);
    }

    public function create(array $data): LocationWard
    {
        return LocationWard::create($data);
    }

    public function update(LocationWard $locationWard, array $data): LocationWard
    {
        $locationWard->update($data);
        return $locationWard;
    }

    public function delete(LocationWard $locationWard): ?bool
    {
        return $locationWard->delete();
    }

}