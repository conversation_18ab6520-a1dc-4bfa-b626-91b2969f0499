<?php

namespace App\Repositories\Admin;

use App\Models\NewsCategory;
use App\Repositories\Admin\Interfaces\NewsCategoryRepositoryInterface;

class NewsCategoryRepository implements NewsCategoryRepositoryInterface
{
 
    public function all()
    {
      return NewsCategory::with(['parent', 'creator']);
    }

    public function find(int $id): ?NewsCategory
    {
        return NewsCategory::find($id);
    }

    public function findOrFail(int $id): NewsCategory
    {
        return NewsCategory::findOrFail($id);
    }

    public function create(array $data): NewsCategory
    {
        return NewsCategory::create($data);
    }

    public function update(NewsCategory $newsCategory, array $data): NewsCategory
    {
        $newsCategory->update($data);
        return $newsCategory;
    }

    public function delete(NewsCategory $newsCategory): ?bool
    {
        return $newsCategory->delete();
    }

}