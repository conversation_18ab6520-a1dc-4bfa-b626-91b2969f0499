<?php

namespace App\Repositories;

use App\Models\User;
use App\Models\UserVerification;
use App\Repositories\Interfaces\UserRepositoryInterface;
use App\Repositories\Interfaces\UserVerificationRepositoryInterface;
use Carbon\Carbon;
use Illuminate\Support\Facades\Hash;

class UserRepository implements UserRepositoryInterface
{
  protected UserVerificationRepositoryInterface $verificationRepo;

  public function __construct(UserVerificationRepositoryInterface $verificationRepo)
  {
    $this->verificationRepo = $verificationRepo;
  }

  public function findByEmail(string $email): ?User
  {
    return User::select(['id', 'fullname', 'email', 'phone', 'status'])
      ->with(['emailVerification', 'phoneVerification'])
      ->where('email', $email)
      ->first();
  }

  public function findByPhone(string $phone): ?User
  {
    return User::select(['id', 'fullname', 'email', 'phone', 'status'])
      ->with(['emailVerification', 'phoneVerification'])
      ->where('phone', $phone)
      ->first();
  }

  public function findByEmailOrPhone(string $identifier): ?User
  {
    return User::select(['id', 'fullname', 'email', 'phone', 'status', 'password'])
      ->with(['emailVerification', 'phoneVerification'])
      ->where('email', $identifier)
      ->orWhere('phone', $identifier)
      ->first();
  }

  /**
   * Check if email or phone already exists (optimized single query)
   */
  public function checkEmailOrPhoneExists(?string $email, ?string $phone): bool
  {
    return User::where(function ($query) use ($email, $phone) {
      if ($email) {
        $query->where('email', $email);
      }
      if ($phone) {
        $query->orWhere('phone', $phone);
      }
    })->exists();
  }

  public function create(array $data): User
  {
    $extra = [
      'password' => Hash::make($data['password']),
    ];
    $userData = array_merge($data, $extra);

    return User::create($userData);
  }

  public function update(User $user, array $data): User
  {
    $user->update($data);
    return $user->load(['emailVerification', 'phoneVerification']);
  }

  public function resetEmailVerification(User $user): void
  {
    $user->emailVerification()->delete();
  }

  public function resetPhoneVerification(User $user): void
  {
    $user->phoneVerification()->delete();
  }

  public function markAsVerified(User $user, string $type): void
  {
    $verification = $this->verificationRepo->findByUserAndType($user, $type);
    if ($verification) {
      $this->verificationRepo->markAsVerified($verification);
    }
  }

  public function updateEmailVerificationOTP(User $user, string $otp, Carbon $expiresAt): void
  {
    $this->verificationRepo->createOrUpdate($user, UserVerification::TYPE_EMAIL, $otp, $expiresAt);
  }

  public function updatePhoneVerificationCode(User $user, string $code, Carbon $expiresAt): void
  {
    $this->verificationRepo->createOrUpdate($user, UserVerification::TYPE_PHONE, $code, $expiresAt);
  }

  public function findUserByOTP(string $otp, string $type): ?User
  {
    $verification = $this->verificationRepo->findByToken($otp, $type);

    if (!$verification) {
      return null;
    }

    // Check if OTP is expired
    if ($verification->isExpired()) {
      $verification->delete();
      return null;
    }

    return $verification->user;
  }

  /**
   * Find user by identifier (email or phone)
   */
  public function findByIdentifier(string $identifier): ?User
  {
    return $this->findByEmailOrPhone($identifier);
  }

  /**
   * Update user password
   */
  public function updatePassword(User $user, string $newPassword): void
  {
    $user->update(['password' => Hash::make($newPassword)]);
  }

  /**
   * Get verification by type
   */
  public function getVerificationByType(User $user, string $type): ?UserVerification
  {
    return $this->verificationRepo->findByUserAndType($user, $type);
  }

  /**
   * Clear verification OTP
   */
  public function clearVerificationOTP(User $user, string $type): void
  {
    $this->verificationRepo->deleteByUserAndType($user, $type);
  }
}
