<?php
namespace App\Repositories;

use App\Models\BeautiStore;
use App\Models\BeautiType;
use App\Repositories\Interfaces\BeautiStoreRepositoryInterface;
use Illuminate\Database\Eloquent\Collection;

class BeautiStoreRepository implements BeautiStoreRepositoryInterface
{
     public function getStoresByTypeAndProvince(string $typeSlug, string $provinceCode): Collection
    {
        return BeautiStore::with('beauties')
            ->whereHas('beauties.types', function ($q) use ($typeSlug) {
                $q->where('slug', $typeSlug);
            })
            ->where('province_code', $provinceCode)
            ->where('status', 1)
            ->get();
    }
}
