<?php 
namespace App\Repositories;

use App\Models\NewsArticle;
use App\Repositories\Interfaces\NewsArticleRepositoryInterface;

class NewsArticleRepository implements NewsArticleRepositoryInterface
{
    public function getAllPublishedWithCategory(): \Illuminate\Contracts\Pagination\LengthAwarePaginator
    {
        return NewsArticle::with(['categories','creator'])
            ->where('status', 1)
            ->orderByDesc('published')
            ->paginate(7);
    }
}
