<?php

namespace App\Repositories;

use App\Models\User;
use App\Models\UserVerification;
use App\Repositories\Interfaces\UserVerificationRepositoryInterface;
use Carbon\Carbon;

class UserVerificationRepository implements UserVerificationRepositoryInterface
{
  public function findByToken(string $token, string $type): ?UserVerification
  {
    return UserVerification::where('token', $token)
      ->where('type', $type)
      ->unverified()
      ->valid()
      ->first();
  }

  public function findByUserAndType(User $user, string $type): ?UserVerification
  {
    return UserVerification::where('user_id', $user->id)
      ->where('type', $type)
      ->first();
  }

  public function createOrUpdate(User $user, string $type, string $token, ?Carbon $expiresAt = null): UserVerification
  {
    // Validate OTP format for both phone and email verification
    if (($type === UserVerification::TYPE_PHONE || $type === UserVerification::TYPE_EMAIL) && !preg_match('/^\d{6}$/', $token)) {
      throw new \InvalidArgumentException('OTP phải là số 6 chữ số');
    }

    return UserVerification::updateOrCreate(
      [
        'user_id' => $user->id,
        'type'    => $type,
      ],
      [
        'token'      => $token,
        'expires_at' => $expiresAt,
        'verified_at' => null, // Reset verification status
      ]
    );
  }

  public function markAsVerified(UserVerification $verification): void
  {
    $verification->markAsVerified();
  }

  public function deleteExpired(): int
  {
    return UserVerification::where('expires_at', '<', now())
      ->whereNull('verified_at')
      ->delete();
  }

  public function deleteByUserAndType(User $user, string $type): bool
  {
    return UserVerification::where('user_id', $user->id)
      ->where('type', $type)
      ->delete() > 0;
  }

  /**
   * Create OTP verification for email
   */
  public function createEmailOTP(User $user, ?int $expireMinutes = null): UserVerification
  {
    $otp = UserVerification::generateOTP();
    $expiresAt = now()->addMinutes($expireMinutes ?? config('auth.email_verification.expire_minutes', 60));

    return $this->createOrUpdate($user, UserVerification::TYPE_EMAIL, $otp, $expiresAt);
  }

  /**
   * Create OTP verification for phone
   */
  public function createPhoneOTP(User $user, ?int $expireMinutes = null): UserVerification
  {
    $otp = UserVerification::generateOTP();
    $expiresAt = now()->addMinutes($expireMinutes ?? config('auth.phone_verification.expire_minutes', 10));

    return $this->createOrUpdate($user, UserVerification::TYPE_PHONE, $otp, $expiresAt);
  }

  /**
   * Create short OTP verification for phone (4 digits)
   */
  public function createShortPhoneOTP(User $user, ?int $expireMinutes = null): UserVerification
  {
    $otp = UserVerification::generateShortOTP();
    $expiresAt = now()->addMinutes($expireMinutes ?? config('auth.phone_verification.expire_minutes', 5));

    return $this->createOrUpdate($user, UserVerification::TYPE_PHONE, $otp, $expiresAt);
  }

  /**
   * Verify OTP for user
   */
  public function verifyOTP(User $user, string $type, string $otp): bool
  {
    $verification = $this->findByUserAndType($user, $type);

    if (!$verification || !$verification->isValid() || $verification->token !== $otp) {
      return false;
    }

    $this->markAsVerified($verification);
    return true;
  }
}
