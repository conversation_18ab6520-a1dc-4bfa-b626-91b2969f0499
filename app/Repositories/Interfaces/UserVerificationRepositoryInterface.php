<?php

namespace App\Repositories\Interfaces;

use App\Models\User;
use App\Models\UserVerification;
use Carbon\Carbon;

interface UserVerificationRepositoryInterface
{
  public function findByToken(string $token, string $type): ?UserVerification;
  public function findByUserAndType(User $user, string $type): ?UserVerification;
  public function createOrUpdate(User $user, string $type, string $token, ?Carbon $expiresAt = null): UserVerification;
  public function markAsVerified(UserVerification $verification): void;
  public function deleteExpired(): int;
  public function deleteByUserAndType(User $user, string $type): bool;
  public function createEmailOTP(User $user, ?int $expireMinutes = null): UserVerification;
  public function createPhoneOTP(User $user, ?int $expireMinutes = null): UserVerification;
  public function createShortPhoneOTP(User $user, ?int $expireMinutes = null): UserVerification;
  public function verifyOTP(User $user, string $type, string $otp): bool;
}
