<?php

namespace App\Repositories\Interfaces;

use App\Models\User;
use Carbon\Carbon;

interface UserRepositoryInterface
{
  public function findByEmail(string $email): ?User;
  public function findByPhone(string $phone): ?User;
  public function findByEmailOrPhone(string $identifier): ?User;
  public function checkEmailOrPhoneExists(?string $email, ?string $phone): bool;
  public function create(array $data): User;
  public function update(User $user, array $data): User;
  public function resetEmailVerification(User $user): void;
  public function resetPhoneVerification(User $user): void;
  public function markAsVerified(User $user, string $type): void;
  public function updateEmailVerificationOTP(User $user, string $otp, Carbon $expiresAt): void;
  public function updatePhoneVerificationCode(User $user, string $code, Carbon $expiresAt): void;
  public function findUserByOTP(string $otp, string $type): ?User;
}
