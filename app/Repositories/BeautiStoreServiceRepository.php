<?php
namespace App\Repositories;

use App\Repositories\Interfaces\BeautiStoreServiceRepositoryInterface;
use App\Models\BeautiStoreService;
use Illuminate\Database\Eloquent\Collection;

class BeautiStoreServiceRepository implements BeautiStoreServiceRepositoryInterface
{
    public function getServicesByStore(int $storeId): Collection
    {
        return BeautiStoreService::with('service')
            ->where('store_id', $storeId)
            ->where('status', 1)
            ->get();
    }
}