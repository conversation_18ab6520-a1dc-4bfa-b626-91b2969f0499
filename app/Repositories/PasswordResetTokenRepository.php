<?php

namespace App\Repositories;

use App\Models\PasswordResetToken;
use App\Repositories\Interfaces\PasswordResetTokenRepositoryInterface;
use Illuminate\Support\Facades\Hash;

class PasswordResetTokenRepository implements PasswordResetTokenRepositoryInterface
{
  public function create(array $data): PasswordResetToken
  {
    return PasswordResetToken::create($data);
  }

  public function findByToken(string $token): ?PasswordResetToken
  {
    return PasswordResetToken::where('token', $token)
      ->whereNull('used_at')
      ->where('expires_at', '>', now())
      ->first();
  }

  public function delete(int $id): bool
  {
    return PasswordResetToken::where('id', $id)->delete() > 0;
  }

  public function deleteByEmail(string $email): bool
  {
    return PasswordResetToken::where('email', $email)->delete() > 0;
  }
}
