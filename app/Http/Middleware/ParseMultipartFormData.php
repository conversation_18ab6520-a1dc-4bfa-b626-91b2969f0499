<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class ParseMultipartFormData
{
    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next): Response
    {
        if ($request->isMethod('PUT') || $request->isMethod('PATCH')) {
            $contentType = $request->header('Content-Type');
            
            if (str_contains($contentType, 'multipart/form-data')) {
                $this->parseMultipartFormData($request);
            }
        }

        return $next($request);
    }

    /**
     * Parse multipart form data for PUT/PATCH requests
     */
    private function parseMultipartFormData(Request $request): void
    {
        $input = file_get_contents('php://input');
        
        if (empty($input)) {
            return;
        }

        $boundary = $this->getBoundary($request->header('Content-Type'));
        
        if (!$boundary) {
            return;
        }

        $parts = $this->parseMultipart($input, $boundary);
        
        foreach ($parts as $part) {
            if (isset($part['name'])) {
                if (isset($part['filename'])) {
                    // Handle file upload
                    $this->handleFileUpload($request, $part);
                } else {
                    // Handle regular field
                    $request->merge([$part['name'] => $part['data']]);
                }
            }
        }
    }

    /**
     * Get boundary from Content-Type header
     */
    private function getBoundary(string $contentType): ?string
    {
        if (preg_match('/boundary=(.+)$/', $contentType, $matches)) {
            return '--' . $matches[1];
        }
        
        return null;
    }

    /**
     * Parse multipart data
     */
    private function parseMultipart(string $input, string $boundary): array
    {
        $parts = [];
        $blocks = explode($boundary, $input);
        
        foreach ($blocks as $block) {
            if (empty(trim($block)) || trim($block) === '--') {
                continue;
            }
            
            $part = $this->parseBlock($block);
            if ($part) {
                $parts[] = $part;
            }
        }
        
        return $parts;
    }

    /**
     * Parse individual block
     */
    private function parseBlock(string $block): ?array
    {
        $parts = explode("\r\n\r\n", $block, 2);
        
        if (count($parts) !== 2) {
            return null;
        }
        
        [$headers, $data] = $parts;
        $data = rtrim($data, "\r\n");
        
        $headerLines = explode("\r\n", $headers);
        $parsedHeaders = [];
        
        foreach ($headerLines as $line) {
            if (preg_match('/^Content-Disposition:\s*form-data;\s*name="([^"]+)"(?:;\s*filename="([^"]+)")?/i', $line, $matches)) {
                $parsedHeaders['name'] = $matches[1];
                if (isset($matches[2])) {
                    $parsedHeaders['filename'] = $matches[2];
                }
            } elseif (preg_match('/^Content-Type:\s*(.+)$/i', $line, $matches)) {
                $parsedHeaders['content_type'] = $matches[1];
            }
        }
        
        if (!isset($parsedHeaders['name'])) {
            return null;
        }
        
        return [
            'name' => $parsedHeaders['name'],
            'filename' => $parsedHeaders['filename'] ?? null,
            'content_type' => $parsedHeaders['content_type'] ?? null,
            'data' => $data
        ];
    }

    /**
     * Handle file upload
     */
    private function handleFileUpload(Request $request, array $part): void
    {
        if (empty($part['data']) || !isset($part['filename'])) {
            return;
        }

        $tempFile = tempnam(sys_get_temp_dir(), 'upload_');
        file_put_contents($tempFile, $part['data']);

        $uploadedFile = new \Illuminate\Http\UploadedFile(
            $tempFile,
            $part['filename'],
            $part['content_type'] ?? 'application/octet-stream',
            null,
            true
        );

        $request->files->set($part['name'], $uploadedFile);
    }
}
