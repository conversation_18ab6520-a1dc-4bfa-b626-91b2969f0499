<?php

namespace App\Http\Middleware\Admin;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class AdminAuthenticate
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $user = Auth::guard('admin')->user();
        if (!$user || !$user->hasRole('admin')) {
            Auth::guard('admin')->logout(); 
            return redirect()->route('admin.login')->withErrors(['msg' => 'Bạn không có quyền truy cập.']);
        }
        return $next($request);
    }
}
