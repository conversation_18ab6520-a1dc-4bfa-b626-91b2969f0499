<?php

namespace App\Http\Middleware;

use App\Http\Controllers\Api\BaseController;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpFoundation\Response;

class ApiRateLimit extends BaseController
{
  /**
   * Handle an incoming request.
   */
  public function handle(Request $request, Closure $next, int $maxAttempts = 60): Response
  {
    $key = $this->resolveRequestSignature($request);

    // Kiểm tra rate limit
    if ($this->tooManyAttempts($key, $maxAttempts)) {
      return $this->buildResponse($key, $maxAttempts);
    }

    // Increment counter
    $this->hit($key);

    // Log API request
    $this->logApiRequest($request);

    $response = $next($request);

    // Add rate limit headers
    return $this->addHeaders(
      $response,
      $maxAttempts,
      $this->calculateRemainingAttempts($key, $maxAttempts)
    );
  }

  /**
   * Resolve request signature
   */
  protected function resolveRequestSignature(Request $request): string
  {
    $user = $request->user();

    if ($user) {
      return 'api_limit:user:' . $user->id;
    }

    return 'api_limit:ip:' . $request->ip();
  }

  /**
   * Check if too many attempts
   */
  protected function tooManyAttempts(string $key, int $maxAttempts): bool
  {
    return Cache::get($key, 0) >= $maxAttempts;
  }

  /**
   * Increment attempts counter
   */
  protected function hit(string $key): int
  {
    return Cache::increment($key, 1) ?: Cache::put($key, 1, 60);
  }

  /**
   * Calculate remaining attempts
   */
  protected function calculateRemainingAttempts(string $key, int $maxAttempts): int
  {
    return $maxAttempts - Cache::get($key, 0);
  }

  /**
   * Build rate limit response
   */
  protected function buildResponse(string $key, int $maxAttempts): Response
  {
    $retryAfter = 60; // seconds

    return $this->sendTooManyRequests('Quá nhiều request. Vui lòng thử lại sau.')
      ->withHeaders([
        'X-RateLimit-Limit'     => $maxAttempts,
        'X-RateLimit-Remaining' => 0,
        'Retry-After'           => $retryAfter,
      ]);
  }

  /**
   * Add rate limit headers to response
   */
  protected function addHeaders(Response $response, int $maxAttempts, int $remaining): Response
  {
    $response->headers->add([
      'X-RateLimit-Limit'     => $maxAttempts,
      'X-RateLimit-Remaining' => max(0, $remaining),
    ]);

    return $response;
  }

  /**
   * Log API request for monitoring
   */
  protected function logApiRequest(Request $request): void
  {
    Log::info('API Request', [
      'method'     => $request->method(),
      'url'        => $request->fullUrl(),
      'ip'         => $request->ip(),
      'user_agent' => $request->header('User-Agent'),
      'user_id'    => $request->user()?->id
    ]);
  }
}
