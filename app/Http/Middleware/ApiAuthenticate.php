<?php

namespace App\Http\Middleware;

use App\Http\Controllers\Api\BaseController;
use App\Services\JWTService;
use Closure;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpFoundation\Response;

class ApiAuthenticate extends BaseController
{
  protected JWTService $jwtService;

  public function __construct(JWTService $jwtService)
  {
    $this->jwtService = $jwtService;
  }

  /**
   * Handle an incoming request.
   */
  public function handle(Request $request, Closure $next): Response
  {
    try {
      // 1. Kiểm tra Authorization header
      $token = $this->extractTokenFromRequest($request);
      if (!$token) {
        return $this->unauthorizedResponse('Token không được cung cấp');
      }

      // 2. Validate JWT token
      if (!$this->jwtService->isTokenValid($token)) {
        return $this->unauthorizedResponse('Token không hợp lệ hoặc đã hết hạn');
      }

      // 3. Lấy user từ token
      $user = $this->jwtService->getUserFromToken($token);
      if (!$user) {
        return $this->unauthorizedResponse('Người dùng không tồn tại');
      }

      // 4. Kiểm tra user status
      if (!$user->status) {
        return $this->unauthorizedResponse('Tài khoản đã bị khóa');
      }

      // 5. Set user vào request để sử dụng trong controller
      $request->setUserResolver(function () use ($user) {
        return $user;
      });

      // 6. Log API access
      $this->logApiAccess($request, $user);

      return $next($request);
    } catch (\Exception $e) {
      Log::error('API Authentication failed', [
        'ip'         => $request->ip(),
        'user_agent' => $request->header('User-Agent'),
        'error'      => $e->getMessage()
      ]);

      return $this->unauthorizedResponse('Xác thực thất bại');
    }
  }

  /**
   * Extract token from Authorization header
   */
  private function extractTokenFromRequest(Request $request): ?string
  {
    $header = $request->header('Authorization', '');

    if (preg_match('/Bearer\s+(.*)$/i', $header, $matches)) {
      return $matches[1];
    }

    return null;
  }

  /**
   * Return unauthorized response
   */
  private function unauthorizedResponse(string $message): JsonResponse
  {
    return $this->sendUnauthorized($message);
  }

  /**
   * Log API access for monitoring
   */
  private function logApiAccess(Request $request, $user): void
  {
    Log::info('API Access', [
      'user_id'    => $user->id,
      'email'      => $user->email,
      'endpoint'   => $request->fullUrl(),
      'method'     => $request->method(),
      'ip'         => $request->ip(),
      'user_agent' => $request->header('User-Agent')
    ]);
  }
}
