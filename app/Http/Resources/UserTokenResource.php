<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class UserTokenResource extends JsonResource
{
  /**
   * Transform the resource into an array.
   *
   * @return array<string, mixed>
   */
  public function toArray(Request $request): array
  {
    return [
      'id'           => $this->id,
      'token_type'   => $this->token_type,
      'device_name'  => $this->device_name,
      'ip_address'   => $this->ip_address,
      'user_agent'   => $this->user_agent,
      'last_used_at' => $this->last_used_at?->format('Y-m-d H:i:s'),
      'expires_at'   => $this->expires_at?->format('Y-m-d H:i:s'),
      'created_at'   => $this->created_at?->format('Y-m-d H:i:s'),
      'is_expired'   => $this->isExpired(),
    ];
  }
}
