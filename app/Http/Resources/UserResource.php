<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class UserResource extends JsonResource
{

  public function toArray(Request $request): array
  {
    return [
      'id'         => $this->id,
      'fullname'   => $this->fullname,
      'email'      => [
        'value'        => $this->email,
        'verified_at'  => optional($this->emailVerification?->first()?->verified_at)->format('Y-m-d H:i:s'),
      ],
      'phone'      => [
        'value'        => $this->phone,
        'verified_at'  => optional($this->phoneVerification?->first()?->verified_at)->format('Y-m-d H:i:s'),
      ],
      'address'    => $this->address,
      'avatar'     => $this->avatar_url,
      'status'     => $this->status,
      'created_at' => optional($this->created_at)->format('Y-m-d H:i:s'),
      'updated_at' => optional($this->updated_at)->format('Y-m-d H:i:s'),
    ];
  }
}
