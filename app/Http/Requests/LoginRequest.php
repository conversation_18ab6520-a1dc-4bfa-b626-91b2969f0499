<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class LoginRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'identifier' => 'required|string',
            'password'          => 'required|string',
        ];
    }

    public function messages()
    {
        return [
            'identifier.required' => 'Số điện thoại hoặc email không được để trống.',
            'identifier.string'   => 'Số điện thoại hoặc email phải là chuỗi ký tự.',
            'password.required'          => 'Mật khẩu không được để trống.',
            'password.string'            => 'Mật khẩu phải là chuỗi ký tự.',
        ];
    }
}
