<?php

namespace App\Http\Requests\Admin;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class StoreLocationWardRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'name' => [
                'required',
                'string',
                'max:50',
            ],
            'code' => [
                'required',
                'string',
                'max:50',
                Rule::unique('location_ward', 'code'),
            ],
            'type' => [
                'required',
                'string',
                'max:10',
                Rule::in(['Xã', 'Thị trấn', 'Phường']),
            ],
            'district_code' => [
                'required',
                'string',
                'max:255',
            ],

            'coordinates'   => 'nullable|string|max:255',
            'latitude'      => ['nullable', 'numeric'],
            'longitude'     => ['nullable', 'numeric'],
        ];
    }

       public function messages(): array
    {
        return [
            'name.required' => 'Tên <PERSON>/Xã/Thị trấn không được để trống.',
            'name.string' => 'Tên Phường/Xã/Thị trấn phải là chuỗi ký tự.',
            'name.max' => 'Tên Phường/Xã/Thị trấn không được vượt quá :max ký tự.',

            'code.required' => 'Mã Phường/Xã/Thị trấn không được để trống.',
            'code.string' => 'Mã Phường/Xã/Thị trấn phải là chuỗi ký tự.',
            'code.max' => 'Mã Phường/Xã/Thị trấn không được vượt quá :max ký tự.',
            'code.unique' => 'Mã Phường/Xã/Thị trấn đã tồn tại trong hệ thống.',

            'type.required' => 'Loại không được để trống.',
            'type.string' => 'Loại phải là chuỗi ký tự.',
            'type.max' => 'Loại không được vượt quá :max ký tự.',
            'type.in' => 'Loại không hợp lệ. Chỉ chấp nhận "Xã", "Thị trấn" hoặc "Phường".',

            'district_code.required' => 'Quận/Huyện không được để trống.',
            'district_code.string' => 'Mã Quận/Huyện phải là chuỗi ký tự.',
            'district_code.max' => 'Mã Quận/Huyện không được vượt quá :max ký tự.',


            'latitude.numeric' => 'Vĩ độ phải là số.',
            'longitude.numeric' => 'Kinh độ phải là số.',
        ];
    }
}
