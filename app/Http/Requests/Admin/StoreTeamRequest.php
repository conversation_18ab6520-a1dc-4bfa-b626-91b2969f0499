<?php

namespace App\Http\Requests\Admin;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class StoreTeamRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'name'         => ['required', 'string', 'max:50', Rule::unique('erp_departments', 'name')],
            'code'         => ['nullable', 'string', 'max:50', Rule::unique('erp_departments', 'code')],
            'manager_id'   => ['nullable', 'integer', Rule::exists('erp_members', 'id')],
            'content'      => ['nullable', 'string'],
            'color'        => ['nullable', 'string', 'max:15'],
            'telegram'     => ['nullable', 'string', 'max:30'],
            'status'       => ['boolean'],
            'creator_id'   => ['nullable', 'integer', Rule::exists('erp_members', 'id')],
            'editor_id'    => ['nullable', 'integer', Rule::exists('erp_members', 'id')],
            'position'     => ['nullable', 'integer', 'min:0', 'max:255'],
        ];
    }

    public function messages(): array
    {
        return [
            'name.required'       => 'Tên teams là bắt buộc.',
            'name.string'         => 'Tên teams phải là chuỗi ký tự.',
            'name.max'            => 'Tên teams không được vượt quá :max ký tự.',
            'name.unique'         => 'Tên teams đã tồn tại.',

            'code.string'         => 'Mã teams phải là chuỗi ký tự.',
            'code.max'            => 'Mã teams không được vượt quá :max ký tự.',
            'code.unique'         => 'Mã teams đã tồn tại.',

            'manager_id.integer'  => 'ID quản lý phải là số nguyên.',
            'manager_id.exists'   => 'Quản lý được chọn không tồn tại.',

            'content.string'      => 'Nội dung phải là chuỗi ký tự.',

            'color.string'        => 'Màu sắc phải là chuỗi ký tự.',
            'color.max'           => 'Mã màu không được vượt quá :max ký tự.',

            'telegram.string'     => 'Telegram phải là chuỗi ký tự.',
            'telegram.max'        => 'Telegram không được vượt quá :max ký tự.',

            'status.boolean'      => 'Trạng thái phải là giá trị boolean (true/false).',

            'creator_id.integer'  => 'ID người tạo phải là số nguyên.',
            'creator_id.exists'   => 'Người tạo không tồn tại.',

            'editor_id.integer'   => 'ID người chỉnh sửa phải là số nguyên.',
            'editor_id.exists'    => 'Người chỉnh sửa không tồn tại.',

            'position.integer'    => 'Vị trí phải là số nguyên.',
            'position.min'        => 'Vị trí phải lớn hơn hoặc bằng :min.',
            'position.max'        => 'Vị trí không được vượt quá :max.',
        ];
    }
}
