<?php

namespace App\Http\Requests\Admin;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class StoreBeautiServiceRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'name' => [
                'required',
                'string',
                'max:150',
                Rule::unique('beauti_services', 'name')->ignore($this->route('beauti_service')), // Đảm bảo tên là duy nhất
            ],
            'types' => [
                'nullable',
                'array',
            ],
             'types.*' => [
                'integer',
                'exists:beauti_types,id', 
            ],
            'slug' => [
                'nullable',
                'string',
                'max:150',
                Rule::unique('beauti_services', 'slug'),
            ],
            'description' => ['nullable', 'string'],
            'image' => [
                'nullable',
                'image',
                'mimes:jpeg,png,jpg,gif,webp',
                'max:2048',
            ],
            'status' => [
                'boolean',
                'required',
            ],
            'position' => [
                'nullable',
                'integer',
                'min:0',
            ],
            'seo_title' => [
                'nullable',
                'string',
                'max:100',
            ],
            'seo_description' => ['nullable', 'string'],
            'seo_keywords' => ['nullable', 'string'],
            'seo_image' => [
                'nullable',
                'image',
                'mimes:jpeg,png,jpg,gif,webp',
                'max:2048',
            ],
        ];
    }

    public function messages(): array
    {
        return [
            'name.required' => 'Tên dịch vụ là bắt buộc.',
            'name.unique' => 'Tên dịch vụ này đã tồn tại.',
            'name.max' => 'Tên dịch vụ không được vượt quá :max ký tự.',
            'slug.unique' => 'Slug này đã tồn tại, vui lòng chọn slug khác hoặc để trống để hệ thống tự tạo.',
            'image.image' => 'File tải lên phải là một hình ảnh.',
            'image.mimes' => 'Hình ảnh phải có định dạng: :values.',
            'image.max' => 'Kích thước hình ảnh không được vượt quá :max kilobytes.',
            'types.array' => 'Các loại hình phải là một mảng.',
            'types.*.integer' => 'ID loại hình phải là số nguyên.',
            'types.*.exists' => 'Loại hình có ID :input không tồn tại.',
            'status.required' => 'Trạng thái dịch vụ là bắt buộc.',
            'position.integer' => 'Vị trí phải là một số nguyên.',
            'position.min' => 'Vị trí không được nhỏ hơn :min.',
            'seo_image.image' => 'File tải lên cho ảnh SEO phải là một hình ảnh.',
            'seo_image.mimes' => 'Ảnh SEO phải có định dạng: :values.',
            'seo_image.max' => 'Kích thước ảnh SEO không được vượt quá :max kilobytes.',
        ];
    }
}
