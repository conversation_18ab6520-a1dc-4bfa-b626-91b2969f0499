<?php

namespace App\Http\Requests\Admin;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdateMemberRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $memberId = $this->route('member');
        return [
            'firstname'     => 'nullable|string|max:70',
            'lastname'      => 'nullable|string|max:30',
            'username'      => [
                'required',
                'string',
                'max:50',
                Rule::unique('erp_members', 'username')->ignore($memberId),
            ],
            'code'          => [
                'nullable',
                'string',
                'max:50',
                Rule::unique('erp_members', 'code')->ignore($memberId),
            ],
            'department_id' => 'nullable|integer|exists:erp_departments,id',
            'title'         => 'nullable|string|max:20',
            'avatar' => [
                'nullable',
                'image',
                'mimes:jpeg,png,jpg,gif,svg,webp',
                'max:2048',
            ],
            'avatar_removed' => ['nullable', 'boolean'],
            'email'         => [
                'nullable',
                'email',
                'max:50',
                Rule::unique('erp_members', 'email')->ignore($memberId),
            ],
            'gender'        => 'nullable|in:0,1,2',
            'color'         => 'nullable|string|max:15',
            'status'        => 'required|boolean',
            'roles'         => 'nullable|array',
            'roles.*'       => 'exists:roles,name',
        ];
    }

    public function messages(): array
    {
        return [
            'email.email'           => 'Địa chỉ email không hợp lệ.',
            'email.unique'          => 'Địa chỉ email này đã được sử dụng bởi người khác.',
            'department_id.exists'  => 'Team không tồn tại trong hệ thống.',
            'username.unique'       => 'Tên đăng nhập này đã tồn tại bởi người khác.',
            'code.unique'           => 'Mã nhân viên này đã tồn tại bởi người khác.',
            'roles.*.exists'        => 'Một hoặc nhiều vai trò được chọn không hợp lệ.',

            'username.required'     => 'Tên đăng nhập là bắt buộc.',
            'status.required'       => 'Trạng thái là bắt buộc.',

            'firstname.string'      => 'Tên phải là chuỗi ký tự.',
            'lastname.string'       => 'Họ phải là chuỗi ký tự.',
            'username.string'       => 'Tên đăng nhập phải là chuỗi ký tự.',
            'code.string'           => 'Mã nhân viên phải là chuỗi ký tự.',
            'title.string'          => 'Chức danh phải là chuỗi ký tự.',
            'color.string'          => 'Mã màu phải là chuỗi ký tự.',

            'firstname.max'         => 'Tên không được vượt quá :max ký tự.',
            'lastname.max'          => 'Họ không được vượt quá :max ký tự.',
            'username.max'          => 'Tên đăng nhập không được vượt quá :max ký tự.',
            'code.max'              => 'Mã nhân viên không được vượt quá :max ký tự.',
            'title.max'             => 'Chức danh không được vượt quá :max ký tự.',
            'email.max'             => 'Email không được vượt quá :max ký tự.',
            'color.max'             => 'Mã màu không được vượt quá :max ký tự.',

            'department_id.integer' => 'ID Team phải là số nguyên.',

            'avatar.image' => 'Tệp được tải lên phải là một hình ảnh.',
            'avatar.mimes' => 'Hình ảnh phải có định dạng JPEG, PNG, JPG, GIF, SVG hoặc WEBP.',
            'avatar.max' => 'Kích thước hình ảnh không được vượt quá 2MB.',

            'gender.in'             => 'Giới tính không hợp lệ. Vui lòng chọn 0 (Nam), 1 (Nữ) hoặc 2 (Khác).',

            'status.boolean'        => 'Trạng thái phải là giá trị boolean (true/false hoặc 0/1).',

            'roles.array'           => 'Vai trò phải là một mảng.',
        ];
    }
}
