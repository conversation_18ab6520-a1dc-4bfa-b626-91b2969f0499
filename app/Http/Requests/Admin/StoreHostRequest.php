<?php

namespace App\Http\Requests\Admin;

use Illuminate\Foundation\Http\FormRequest;

class StoreHostRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            // 'user_id'      => 'nullable|integer|exists:user,id',
            'name'         => 'required|string|max:255',
            'alias_name'   => 'nullable|string|max:20',
            'email'        => 'nullable|email|max:50',
            'phone'        => 'nullable|string|max:50',
            'zalo'         => 'nullable|string|max:50',
            'address'      => 'nullable|string|max:255',
            'status'       => 'boolean',
            'in_charge_id' => 'nullable|integer|exists:erp_members,id',
            'description'  => 'nullable|string',
        ];
    }

    public function messages()
{
    return [
        // 'user_id.integer'       => 'User ID phải là số nguyên.',
        // 'user_id.exists'        => 'User ID không tồn tại trong hệ thống.',
        'name.required'         => 'Tên là trường bắt buộc.',
        'name.string'           => 'Tên phải là chuỗi ký tự.',
        'name.max'              => 'Tên không được dài quá 255 ký tự.',
        'alias_name.string'     => 'Tên biệt danh phải là chuỗi ký tự.',
        'alias_name.max'        => 'Tên biệt danh không được dài quá 20 ký tự.',
        'email.email'           => 'Email không đúng định dạng.',
        'email.max'             => 'Email không được dài quá 50 ký tự.',
        'phone.string'          => 'Số điện thoại phải là chuỗi ký tự.',
        'phone.max'             => 'Số điện thoại không được dài quá 50 ký tự.',
        'zalo.string'           => 'Zalo phải là chuỗi ký tự.',
        'zalo.max'              => 'Zalo không được dài quá 50 ký tự.',
        'address.string'        => 'Địa chỉ phải là chuỗi ký tự.',
        'address.max'           => 'Địa chỉ không được dài quá 255 ký tự.',
        'status.boolean'        => 'Trạng thái phải là giá trị đúng hoặc sai.',
        'in_charge_id.integer'  => 'ID người phụ trách phải là số nguyên.',
        'in_charge_id.exists'   => 'Người phụ trách không tồn tại.',
        'description.string'    => 'Mô tả phải là chuỗi ký tự.',
    ];
}

}
