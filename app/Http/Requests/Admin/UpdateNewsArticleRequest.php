<?php

namespace App\Http\Requests\Admin;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdateNewsArticleRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $articleId = $this->route('news_article');
        return [
            'title'           => ['required', 'string', 'max:255'],
            'categories'      => ['nullable', 'array'],
            'categories.*'    => ['integer', 'exists:news_categories,id'],
            'content'         => ['nullable', 'string'],
            'image'           => ['nullable', 'image', 'mimes:jpeg,png,jpg,gif,svg,webp', 'max:2048'],
            'image_removed'   => ['nullable', 'boolean'], 
            'description'     => ['nullable', 'string', 'max:1000'],
            'code'            => [
                'nullable',
                'string',
                'max:255',
                Rule::unique('news_articles', 'code')->ignore($articleId),
            ],
            'link'            => ['nullable', 'url', 'max:255'],
            'url_target'      => ['nullable', 'string', 'in:_self,_blank'],
            'position'        => ['nullable', 'integer', 'min:0'],
            'status'          => ['required', 'boolean'],
            'featured'        => ['required', 'boolean'],
            'seo_title'       => ['nullable', 'string', 'max:255'],
            'seo_description' => ['nullable', 'string', 'max:500'],
            'seo_keywords'    => ['nullable', 'string', 'max:255'],
            'seo_image'       => ['nullable', 'image', 'mimes:jpeg,png,jpg,gif,svg,webp', 'max:2048'],
            'seo_image_removed' => ['nullable', 'boolean'], 
        ];
    }

      public function messages(): array
    {
        return [
            'title.required'        => 'Tiêu đề là bắt buộc.',
            'title.string'          => 'Tiêu đề phải là chuỗi ký tự.',
            'title.max'             => 'Tiêu đề không được vượt quá :max ký tự.',
            'categories.array'      => 'Danh mục phải là một mảng.',
            'categories.*.integer'  => 'ID danh mục phải là số nguyên.',
            'categories.*.exists'   => 'Danh mục đã chọn không hợp lệ.',
            'content.string'        => 'Nội dung phải là chuỗi ký tự.',
            'image.image'           => 'File ảnh phải là định dạng hình ảnh.',
            'image.mimes'           => 'Ảnh chỉ chấp nhận định dạng: jpeg, png, jpg, gif, svg, webp.',
            'image.max'             => 'Ảnh không được vượt quá :max KB.',
            'image_removed.boolean' => 'Giá trị xóa ảnh không hợp lệ.',
            'description.string'    => 'Mô tả phải là chuỗi ký tự.',
            'description.max'       => 'Mô tả không được vượt quá :max ký tự.',
            'code.string'           => 'Mã phải là chuỗi ký tự.',
            'code.max'              => 'Mã không được vượt quá :max ký tự.',
            'code.unique'           => 'Mã này đã tồn tại.',
            'link.url'              => 'Liên kết không đúng định dạng URL.',
            'link.max'              => 'Liên kết không được vượt quá :max ký tự.',
            'url_target.in'         => 'Mục tiêu URL không hợp lệ.',
            'position.integer'      => 'Thứ tự phải là số nguyên.',
            'position.min'          => 'Thứ tự phải lớn hơn hoặc bằng :min.',
            'status.required'       => 'Trạng thái hiển thị là bắt buộc.',
            'status.boolean'        => 'Trạng thái hiển thị phải là Có hoặc Không.',
            'featured.required'     => 'Trạng thái nổi bật là bắt buộc.',
            'featured.boolean'      => 'Trạng thái nổi bật phải là Có hoặc Không.',
            'seo_title.string'      => 'Tiêu đề SEO phải là chuỗi ký tự.',
            'seo_title.max'         => 'Tiêu đề SEO không được vượt quá :max ký tự.',
            'seo_description.string'=> 'Mô tả SEO phải là chuỗi ký tự.',
            'seo_description.max'   => 'Mô tả SEO không được vượt quá :max ký tự.',
            'seo_keywords.string'   => 'Từ khóa SEO phải là chuỗi ký tự.',
            'seo_keywords.max'      => 'Từ khóa SEO không được vượt quá :max ký tự.',
            'seo_image.image'       => 'File ảnh SEO phải là định dạng hình ảnh.',
            'seo_image.mimes'       => 'Ảnh SEO chỉ chấp nhận định dạng: jpeg, png, jpg, gif, svg, webp.',
            'seo_image.max'         => 'Ảnh SEO không được vượt quá :max KB.',
            'seo_image_removed.boolean' => 'Giá trị xóa ảnh SEO không hợp lệ.',
        ];
    }
}
