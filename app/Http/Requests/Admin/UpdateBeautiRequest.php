<?php

namespace App\Http\Requests\Admin;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdateBeautiRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $beautiId = $this->route('beauty');
        return [
             'name' => [
                'required',
                'string',
                'max:255',
                Rule::unique('beauties', 'name')->ignore($beautiId),
            ],
            'user_id' => [
                'nullable',
                'exists:users,id'
            ],
            'address' => [
                'nullable',
                'string',
                'max:255'
            ],
            'phone' => [
                'nullable',
                'string',
                'max:50',
                'regex:/^([0-9\s\-\+\(\)]*)$/'
            ],
            'fanpage' => [
                'nullable',
                'string',
                'max:255',
            ],
            'tiktok' => [
                'nullable',
                'string',
                'max:255',
            ],
            'website' => [
                'nullable',
                'string',
                'max:100',
            ],
            'email' => [
                'nullable',
                'string',
                'email',
                'max:50'
            ],
            'description' => [
                'nullable',
                'string'
            ],
            'status' => [
                'boolean'
            ],
            'creator_id' => [ 
                'nullable',
                'exists:users,id'
            ],
            'beauti_types' => [
                'nullable',
                'array'
            ],
            'beauti_types.*' => [
                'integer',
                'exists:beauti_types,id'
            ],
        ];
    }

     public function messages(): array
    {
        return [
            'name.required' => 'Tên là bắt buộc.',
            'name.string' => 'Tên phải là chuỗi ký tự.',
            'name.max' => 'Tên không được vượt quá :max ký tự.',
            'name.unique' => 'Tên đã tồn tại.',
            
            'user_id.exists' => 'ID người dùng không hợp lệ.',
            
            'address.string' => 'Địa chỉ phải là chuỗi ký tự.',
            'address.max' => 'Địa chỉ không được vượt quá :max ký tự.',
            
            'phone.string' => 'Số điện thoại phải là chuỗi ký tự.',
            'phone.max' => 'Số điện thoại không được vượt quá :max ký tự.',
            'phone.regex' => 'Số điện thoại không đúng định dạng.',
            
            'fanpage.string' => 'Fanpage phải là chuỗi ký tự.',
            'fanpage.max' => 'Fanpage không được vượt quá :max ký tự.',

            'tiktok.string' => 'Tiktok phải là chuỗi ký tự.',
            'tiktok.max' => 'Tiktok không được vượt quá :max ký tự.',

            'website.string' => 'Website phải là chuỗi ký tự.',
            'website.max' => 'Website không được vượt quá :max ký tự.',
            
            'email.string' => 'Email phải là chuỗi ký tự.',
            'email.email' => 'Email không đúng định dạng.',
            'email.max' => 'Email không được vượt quá :max ký tự.',
            
            'description.string' => 'Mô tả phải là chuỗi ký tự.',
            
            'status.boolean' => 'Trạng thái là không hợp lệ.',
            
            'creator_id.exists' => 'ID người tạo không hợp lệ.',

            'beauti_types.array' => 'Các loại dịch vụ phải là một mảng.',
            'beauti_types.*.integer' => 'Mỗi ID loại dịch vụ phải là số nguyên.',
            'beauti_types.*.exists' => 'Một hoặc nhiều loại dịch vụ đã chọn không hợp lệ.',
        ];
    }
}
