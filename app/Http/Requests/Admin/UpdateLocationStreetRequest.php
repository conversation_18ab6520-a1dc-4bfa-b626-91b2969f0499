<?php

namespace App\Http\Requests\Admin;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdateLocationStreetRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $streetId = $this->route('location_street');
        return [
            'name' => [
                'required',
                'string',
                'max:100',
            ],
            'code' => [
                'required',
                'string',
                'max:50',
                Rule::unique('location_street', 'code')->ignore($streetId),
            ],
            'ward_code' => [
                'nullable',
                'string',
                'max:255',
            ],
            'district_code' => [
                'nullable',
                'string',
                'max:255',
            ],
            'province_code' => [
                'required',
                'string',
                'max:255',
            ],
            'coordinates'   => 'nullable|string|max:255',
            'latitude'      => ['nullable', 'numeric'],
            'longitude'     => ['nullable', 'numeric'],
        ];
    }

        public function messages(): array
    {
        return [
            'name.required' => 'Tên đường không được để trống.',
            'name.string' => 'Tên đường phải là chuỗi ký tự.',
            'name.max' => 'Tên đường không được vượt quá :max ký tự.',

            'code.required' => 'Mã đường không được để trống.',
            'code.string' => 'Mã đường phải là chuỗi ký tự.',
            'code.max' => 'Mã đường không được vượt quá :max ký tự.',
            'code.unique' => 'Mã đường đã tồn tại trong hệ thống.',

            'ward_code.string' => 'Mã Phường/Xã/Thị trấn phải là chuỗi ký tự.',
            'ward_code.max' => 'Mã Phường/Xã/Thị trấn không được vượt quá :max ký tự.',

            'district_code.string' => 'Mã Quận/Huyện phải là chuỗi ký tự.',
            'district_code.max' => 'Mã Quận/Huyện không được vượt quá :max ký tự.',

            'province_code.required' => 'Tỉnh/Thành phố không được để trống.',
            'province_code.string' => 'Mã Tỉnh/Thành phố phải là chuỗi ký tự.',
            'province_code.max' => 'Mã Tỉnh/Thành phố không được vượt quá :max ký tự.',

            'latitude.numeric' => 'Vĩ độ phải là số.',
            'longitude.numeric' => 'Kinh độ phải là số.',
        ];
    }
}
