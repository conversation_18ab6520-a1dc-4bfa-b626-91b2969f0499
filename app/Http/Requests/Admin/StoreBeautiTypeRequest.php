<?php

namespace App\Http\Requests\Admin;

use Illuminate\Foundation\Http\FormRequest;

class StoreBeautiTypeRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'name' => [
                'required',
                'string',
                'max:100',
                'unique:beauti_types,name',
            ],
            'slug' => [
                'nullable',
                'string',
                'max:80',
                'unique:beauti_types,slug',
            ],
            'image'           => [
                'nullable',
                'image',
                'mimes:jpeg,png,jpg,gif,svg,webp',
                'max:2048'
            ],
            'description' => [
                'nullable',
                'string',
            ],
            'status' => [
                'required',
                'boolean',
            ],
            'featured' => [
                'required',
                'boolean',
            ],
            'position' => [
                'nullable',
                'integer',
                'min:0',
            ],
            'seo_title' => [
                'nullable',
                'string',
                'max:100',
            ],
            'seo_description' => [
                'nullable',
                'string',
            ],
            'seo_keywords' => [
                'nullable',
                'string',
                'max:255',
            ],
            'seo_image'       => [
                'nullable',
                'image',
                'mimes:jpeg,png,jpg,gif,svg,webp',
                'max:2048'
            ],
        ];
    }


    public function messages(): array
    {
        return [
            'name.required' => 'Bạn cần nhập tên loại hình.',
            'name.max'             => 'Tiêu đề không được vượt quá :max ký tự.',
            'name.unique'   => 'Tên loại hình này đã tồn tại.',
            'slug.string'           => 'Slug phải là chuỗi ký tự.',
            'slug.unique'   => 'Slug này đã được sử dụng.',
            'slug.max'              => 'Slug không được vượt quá :max ký tự.',
            'description.string'    => 'Mô tả phải là chuỗi ký tự.',
            'description.max'       => 'Mô tả không được vượt quá :max ký tự.',
            'image.image'           => 'File ảnh phải là định dạng hình ảnh.',
            'image.mimes'           => 'Ảnh chỉ chấp nhận định dạng: jpeg, png, jpg, gif, svg, webp.',
            'image.max'             => 'Ảnh không được vượt quá :max KB.',
            'status.boolean' => 'Trường trạng thái phải là boolean.',
            'featured.boolean' => 'Trường nổi bật phải là boolean.',
            'position.integer' => 'Vị trí ưu tiên phải là số nguyên.',
            'seo_title.string'      => 'Tiêu đề SEO phải là chuỗi ký tự.',
            'seo_title.max'         => 'Tiêu đề SEO không được vượt quá :max ký tự.',
            'seo_description.string' => 'Mô tả SEO phải là chuỗi ký tự.',
            'seo_description.max'   => 'Mô tả SEO không được vượt quá :max ký tự.',
            'seo_keywords.string'   => 'Từ khóa SEO phải là chuỗi ký tự.',
            'seo_keywords.max'      => 'Từ khóa SEO không được vượt quá :max ký tự.',
            'seo_image.image'       => 'File ảnh SEO phải là định dạng hình ảnh.',
            'seo_image.mimes'       => 'Ảnh SEO chỉ chấp nhận định dạng: jpeg, png, jpg, gif, svg, webp.',
            'seo_image.max'         => 'Ảnh SEO không được vượt quá :max KB.',
        ];
    }
}
