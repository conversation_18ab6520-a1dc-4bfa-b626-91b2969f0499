<?php

namespace App\Http\Requests\Admin;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class StoreBeautiStoreRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'name' => [
                'required',
                'string',
                'max:150',
                Rule::unique('beauti_stores', 'name'),
            ],
            'beauti_id' => [
                'nullable',
                'exists:beauties,id',
            ],
            'slug' => [
                'nullable',
                'string',
                'max:150',
                Rule::unique('beauti_stores', 'slug'),
            ],
            'description' => [
                'nullable',
                'string'
            ],
            'price_from' => ['nullable', 'integer', 'min:0'],
            'image' => [
                'nullable',
                'image',
                'mimes:jpeg,png,jpg,gif,webp',
                'max:2048',
            ],
            'street' => ['nullable', 'string', 'max:255'],
            'ward_code' => ['nullable', 'string', 'max:20'],
            'district_code' => ['nullable', 'string', 'max:20'],
            'province_code' => ['nullable', 'string', 'max:20'],
            'full_address' => ['nullable', 'string', 'max:255'],
            'status' => ['boolean', 'required'],
            'position' => ['nullable', 'integer', 'min:0'],
            'seo_title' => ['nullable', 'string', 'max:100'],
            'seo_description' => ['nullable', 'string'],
            'seo_keywords' => ['nullable', 'string'],
            'seo_image' => [
                'nullable',
                'image',
                'mimes:jpeg,png,jpg,gif,webp',
                'max:2048',
            ],
            'services' => [
                'nullable',
                'array',
            ],
            'services.*.service_id' => [
                'required',
                'integer',
                'exists:beauti_services,id',
            ],
            'services.*.price_from' => [
                'nullable',
                'integer',
                'min:0',
            ],
            'services.*.status' => [
                'boolean',
                'required',
            ],
            'opening_time'     => 'nullable|date_format:H:i',
            'closing_time'     => 'nullable|date_format:H:i|after_or_equal:opening_time',
        ];
    }


    protected function prepareForValidation(): void
    {
        if ($this->filled('opening_time')) {
            $ot = $this->input('opening_time');
            $this->merge(['opening_time' => substr($ot, 0, 5)]);
        }
        if ($this->filled('closing_time')) {
            $ct = $this->input('closing_time');
            $this->merge(['closing_time' => substr($ct, 0, 5)]);
        }
    }


    public function messages(): array
    {
        return [
            'name.required' => 'Tên cơ sở là bắt buộc.',
            'name.unique' => 'Tên cơ sở này đã tồn tại.',
            'name.max' => 'Tên cơ sở không được vượt quá :max ký tự.',
            'beauti_id.exists' => 'Beauti được liên kết không hợp lệ.',
            'slug.unique' => 'Slug này đã tồn tại, vui lòng chọn slug khác hoặc để trống để hệ thống tự tạo.',
            'price_from.integer'  => 'Giá khởi điểm phải là số nguyên.',
            'price_from.min'      => 'Giá khởi điểm không thể âm.',
            'image.image' => 'File tải lên phải là một hình ảnh.',
            'image.mimes' => 'Hình ảnh phải có định dạng: :values.',
            'image.max' => 'Kích thước hình ảnh không được vượt quá :max kilobytes.',
            'description.string' => 'Mô tả phải là chuỗi ký tự.',
            'status.required' => 'Trạng thái cơ sở là bắt buộc.',
            'position.integer' => 'Vị trí phải là một số nguyên.',
            'position.min' => 'Vị trí không được nhỏ hơn :min.',
            'seo_image.image' => 'File tải lên cho ảnh SEO phải là một hình ảnh.',
            'seo_image.mimes' => 'Ảnh SEO phải có định dạng: :values.',
            'seo_image.max' => 'Kích thước ảnh SEO không được vượt quá :max kilobytes.',
            'services.array' => 'Các dịch vụ phải là một mảng.',
            'services.*.service_id.required' => 'ID dịch vụ là bắt buộc cho mỗi dịch vụ.',
            'services.*.service_id.integer' => 'ID dịch vụ phải là số nguyên.',
            'services.*.service_id.exists' => 'Dịch vụ với ID :input không tồn tại.',
            'services.*.price_from.integer' => 'Giá khởi điểm phải là số nguyên.',
            'services.*.price_from.min' => 'Giá khởi điểm không được nhỏ hơn :min.',
            'services.*.status.required' => 'Trạng thái dịch vụ là bắt buộc.',
            'services.*.status.boolean' => 'Trạng thái dịch vụ phải là true/false.',
            'opening_time.date_format'    => 'Giờ mở cửa định dạng HH:MM.',
            'closing_time.date_format'    => 'Giờ đóng cửa định dạng HH:MM.',
            'closing_time.after_or_equal' => 'Giờ đóng phải sau hoặc bằng giờ mở.',
        ];
    }
}
