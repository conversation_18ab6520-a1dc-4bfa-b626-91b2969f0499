<?php

namespace App\Http\Requests\Admin;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class StoreLocationDistrictRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'name' => [
                'required',
                'string',
                'max:50',
            ],
            'code' => [
                'required', 
                'string',
                'max:50',
                Rule::unique('location_district', 'code'),
            ],
            'type' => [
                'required', 
                'string',
                'max:10',
                Rule::in(['Quận', 'Huyện']), 
            ],
            'province_code' => [
                'required',
                'string',
                'max:50',
            ],
          
            'code_district' => ['nullable', 'string', 'max:50'],
            'latitude'      => ['nullable', 'numeric'],
            'longitude'     => ['nullable', 'numeric'], 
        ];
    }

      public function messages(): array
    {
        return [
            'name.required' => 'Tên quận/huyện không được để trống.',
            'name.string' => 'Tên quận/huyện phải là chuỗi ký tự.',
            'name.max' => 'Tên quận/huyện không được vượt quá :max ký tự.',

            'code.required' => 'Mã quận/huyện không được để trống.',
            'code.string' => 'Mã quận/huyện phải là chuỗi ký tự.',
            'code.max' => 'Mã quận/huyện không được vượt quá :max ký tự.',
            'code.unique' => 'Mã quận/huyện đã tồn tại trong hệ thống.',

            'type.required' => 'Loại không được để trống.',
            'type.string' => 'Loại phải là chuỗi ký tự.',
            'type.max' => 'Loại không được vượt quá :max ký tự.',
            'type.in' => 'Loại không hợp lệ. Chỉ chấp nhận "Quận" hoặc "Huyện".',

            'province_code.required' => 'Tỉnh/Thành phố không được để trống.',
            'province_code.string' => 'Mã tỉnh/thành phố phải là chuỗi ký tự.',
            'province_code.max' => 'Mã tỉnh/thành phố không được vượt quá :max ký tự.',


            'latitude.numeric' => 'Vĩ độ phải là số.',
            'longitude.numeric' => 'Kinh độ phải là số.',
        ];
    }
}
