<?php

namespace App\Http\Requests\Admin;

use Illuminate\Foundation\Http\FormRequest;

class StoreUserRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'fullname'      => ['required', 'string', 'max:255'],
            'phone'         => ['nullable', 'string', 'max:30'],
            'email'         => ['nullable', 'email', 'max:45', 'unique:users,email'],
            'address'       => ['nullable', 'string', 'max:255'],
            'date_of_birth' => ['nullable', 'date'],
            'gender'        => ['nullable', 'in:0,1'],
            'status'        => ['required', 'boolean'],
            'supporter_id'  => ['nullable', 'integer', 'exists:erp_members,id'],
        ];
    }

     public function messages(): array
    {
        return [
            'fullname.required'      => '<PERSON>ạn phải nhập họ và tên.',
            'fullname.string'        => '<PERSON>ọ tên phải là chuỗi ký tự.',
            'fullname.max'           => 'Họ tên không được vượt quá 255 ký tự.',

            'phone.string'           => 'Số điện thoại phải là chuỗi.',
            'phone.max'              => 'Số điện thoại tối đa 30 ký tự.',

            'email.email'            => 'Email không đúng định dạng.',
            'email.max'              => 'Email tối đa 45 ký tự.',
            'email.unique'           => 'Email này đã được sử dụng.',

            'address.string'         => 'Địa chỉ phải là chuỗi.',
            'address.max'            => 'Địa chỉ tối đa 255 ký tự.',

            'date_of_birth.date'     => 'Ngày sinh không đúng định dạng ngày tháng.',

            'gender.in'              => 'Giới tính không hợp lệ.',

            'status.required'        => 'Bạn phải chọn trạng thái kích hoạt.',
            'status.boolean'         => 'Trạng thái phải là true hoặc false.',

            'supporter_id.integer'   => 'Id người phụ trách không hợp lệ.',
            'supporter_id.exists'    => 'Tài khoản người phụ trách không tồn tại trong hệ thống.',
        ];
    }

}
