<?php

namespace App\Http\Requests\Admin;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class StoreLocationProvinceRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'name' => [
                'required',
                'string',
                'max:255',
                Rule::unique('location_province', 'name'),
            ],
            'code' => [
                'required',
                'string',
                'max:10',
                Rule::unique('location_province', 'code'),
            ],
            'type' => [
                'required',
                'string',
                'max:50',
            ],
            'status' => [
                'required',
                'boolean',
            ],
        ];
    }

      public function messages(): array
    {
        return [
            'name.required' => 'Tên tỉnh/thành phố không được để trống.',
            'name.string' => 'Tên tỉnh/thành phải là chuỗi ký tự.',
            'name.max' => 'Tên tỉnh/thành không được vượt quá :max ký tự.',
            'name.unique' => 'Tên tỉnh/thành đã tồn tại trong hệ thống.',

            'code.required' => 'Mã tỉnh/thành phố không được để trống.',
            'code.string' => 'Mã tỉnh/thành phải là chuỗi ký tự.',
            'code.max' => 'Mã tỉnh/thành không được vượt quá :max ký tự.',
            'code.unique' => 'Mã tỉnh/thành đã tồn tại trong hệ thống.',

            'type.required' => 'Loại không được để trống.',
            'type.string' => 'Loại phải là chuỗi ký tự.',
            'type.max' => 'Loại không được vượt quá :max ký tự.',

            'status.required' => 'Trạng thái không được để trống.',
            'status.boolean' => 'Trạng thái không hợp lệ.',
        ];
    }
}
