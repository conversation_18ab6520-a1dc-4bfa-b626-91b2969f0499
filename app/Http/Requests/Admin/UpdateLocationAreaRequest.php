<?php

namespace App\Http\Requests\Admin;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdateLocationAreaRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $id = $this->route('location_area');
        return [
           'name'           => ['nullable', 'string', 'max:100', Rule::unique('location_area', 'name')->ignore($id)],
            'code'           => ['nullable', 'string', 'max:100', Rule::unique('location_area', 'code')->ignore($id)],
            'image'          => ['nullable', 'image', 'max:2048'], 
            'address'        => ['nullable', 'string', 'max:20'],
            'street'         => ['nullable', 'string', 'max:100'],
            'ward_code'      => ['nullable', 'string', 'max:20'],
            'district_code'  => ['nullable', 'string', 'max:20'],
            'province_code'  => ['nullable', 'string', 'max:20'],
            'featured'       => ['nullable', 'boolean'],
            'status'         => ['nullable', 'boolean'],
            'position'       => ['nullable', 'integer', 'min:0'],
            'longitude'      => ['nullable', 'numeric'],
            'latitude'       => ['nullable', 'numeric'],
            'full_address'   => ['nullable', 'string', 'max:250'],
        ];
    }

     public function messages(): array
    {
        return [
            'name.string'          => 'Tên khu vực phải là chuỗi ký tự.',
            'name.max'             => 'Tên khu vực không được vượt quá :max ký tự.',
            'name.unique'          => 'Tên khu vực đã tồn tại.',

            'code.string'          => 'Mã khu vực phải là chuỗi ký tự.',
            'code.max'             => 'Mã khu vực không được vượt quá :max ký tự.',
            'code.unique'          => 'Mã khu vực đã tồn tại.',

            'image.image'          => 'Hình ảnh không đúng định dạng.',
            'image.max'            => 'Hình ảnh không được vượt quá 2MB.',

            'address.string'       => 'Địa chỉ phải là chuỗi ký tự.',
            'address.max'          => 'Địa chỉ không được vượt quá :max ký tự.',

            'street.string'        => 'Tên đường phải là chuỗi ký tự.',
            'street.max'           => 'Tên đường không được vượt quá :max ký tự.',

            'ward_code.string'     => 'Phường/xã phải là chuỗi ký tự.',
            'ward_code.max'        => 'Phường/xã không được vượt quá :max ký tự.',

            'district_code.string' => 'Quận/huyện phải là chuỗi ký tự.',
            'district_code.max'    => 'Quận/huyện không được vượt quá :max ký tự.',

            'province_code.string' => 'Tỉnh/thành phải là chuỗi ký tự.',
            'province_code.max'    => 'Tỉnh/thành không được vượt quá :max ký tự.',

            'featured.boolean'     => 'Nổi bật phải là giá trị đúng/sai.',
            'status.boolean'       => 'Trạng thái phải là giá trị đúng/sai.',

            'position.integer'     => 'Vị trí phải là số nguyên.',
            'position.min'         => 'Vị trí không được nhỏ hơn :min.',

            'longitude.numeric'    => 'Kinh độ phải là số.',
            'latitude.numeric'     => 'Vĩ độ phải là số.',

            'full_address.string'  => 'Địa chỉ đầy đủ phải là chuỗi ký tự.',
            'full_address.max'     => 'Địa chỉ đầy đủ không được vượt quá :max ký tự.',
        ];
    }
}
