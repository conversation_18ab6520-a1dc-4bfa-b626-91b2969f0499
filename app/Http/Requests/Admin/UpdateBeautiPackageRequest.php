<?php

namespace App\Http\Requests\Admin;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;
use Illuminate\Support\Str;

class UpdateBeautiPackageRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $packageId = $this->route('beauti_package');
        return [
            'store_id'   => ['required', 'integer', 'exists:beauti_stores,id'],
            'service_id'   => ['required', 'integer', 'exists:beauti_services,id'],
            'name' => [
                'required',
                'string',
                'max:150',
                Rule::unique('beauti_store_packages', 'name')->ignore($packageId),
            ],
            'slug' => [
                'nullable',
                'string',
                'max:150',
                Rule::unique('beauti_store_packages', 'slug')->ignore($packageId),
            ],
            'duration_days'      => ['nullable', 'integer', 'min:0'],
            'warranty_days'      => ['nullable', 'integer', 'min:0'],
            'price_new'              => ['sometimes', 'nullable', 'integer', 'min:0'],
            'price_old'              => ['sometimes', 'nullable', 'integer', 'min:0'],
            'description'        => ['nullable', 'string'],
            'imgs' => [
                'nullable',
                'array',
            ],

            'imgs.*' => [
                'string',
                'url',
                'regex:/^' . $this->getImgRegex() . '$/',
            ],
            'status'             => ['sometimes', 'required', 'boolean'],
            'seo_title'          => ['nullable', 'string', 'max:100'],
            'seo_description'    => ['nullable', 'string'],
            'seo_keywords'       => ['nullable', 'string'],
            'seo_image' => [
                'nullable',
                'string',
                // 'regex:' . $this->getImageRegex(),
            ],
            'seo_image_removed'  => ['nullable', 'boolean'],
        ];
    }


    protected function prepareForValidation(): void
    {
        if ($this->has('imgs') && is_array($this->imgs)) {
            $imgs = array_map(function ($img) {
                if (!Str::startsWith($img, ['http://', 'https://'])) {
                    return config('app.url') . $img;
                }
                return $img;
            }, $this->imgs);

            $this->merge(['imgs' => $imgs]);
        }
    }
    protected function getImageRegex(): string
    {
        return "/^(?:https?:\/\/[^\/]+)?\/uploads\/images\/[\w\-\/]+\/[a-zA-Z0-9._-]+\.(jpg|jpeg|png|gif|webp)$/i";
    }

    protected function getImgRegex(): string
    {
        return "(?:https?:\/\/[^\/]+)?\/uploads\/images\/[\w\-\/]+\/[a-zA-Z0-9._-]+\.(jpg|jpeg|png|gif|webp)";
    }


    public function messages()
    {
        return [
            'store_id.required'   => 'Vui lòng chọn 1 cơ sở.',
            'store_id.exists'   => 'Cơ sở không hợp lệ.',
            'service_id.required'   => 'Vui lòng chọn 1 dịch vụ.',
            'service_id.exists'   => 'Dịch vụ không hợp lệ.',
            'name.required'             => 'Tên gói dịch vụ không được để trống.',
            'name.unique' => 'Tên gói dịch vụ này đã tồn tại.',
            'name.max'                  => 'Tên gói tối đa :max ký tự.',
            'slug.unique' => 'Slug này đã tồn tại, vui lòng chọn slug khác hoặc để trống để hệ thống tự tạo.',
            'price_old.integer'             => 'Giá phải là số nguyên.',
            'price_new.integer'             => 'Giá phải là số nguyên.',
            'price_old.min'                 => 'Giá phải lớn hơn hoặc bằng 0.',
            'price_new.min'                 => 'Giá phải lớn hơn hoặc bằng 0.',
            'duration_days.integer'     => 'Thời hạn phải là số nguyên.',
            'duration_days.min'         => 'Thời hạn không thể âm.',
            'warranty_days.integer'     => 'Bảo hành phải là số nguyên.',
            'warranty_days.min'         => 'Bảo hành không thể âm.',
            'description.string' => 'Mô tả phải là chuỗi ký tự.',
            'imgs.array' => 'Trường hình ảnh liên quan phải là một mảng.',
            'imgs.*.string' => 'Đường dẫn hình ảnh phải là chuỗi.',
            'imgs.*.url' => 'Đường dẫn hình ảnh không hợp lệ.',
            // 'imgs.*.regex' => 'Ảnh không hợp lệ.',
            'status.boolean'            => 'Trạng thái không hợp lệ.',
            'seo_title.max'             => 'Tiêu đề SEO tối đa :max ký tự.',
            'seo_image.string' => 'Trường ảnh phải là một chuỗi đường dẫn.',
            // 'seo_image.regex' => 'Định dạng đường dẫn ảnh không hợp lệ.',
        ];
    }
}
