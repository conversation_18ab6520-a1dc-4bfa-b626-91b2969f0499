<?php

namespace App\Http\Requests\Admin;

use Illuminate\Foundation\Http\FormRequest;

class StoreLocationAreaRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'name'          => ['required', 'string', 'max:100'],
            'province_code' => ['required', 'string', 'max:20'],
            'district_code' => ['required', 'string', 'max:20'],
            'ward_code'     => ['nullable', 'string', 'max:20'],
            'street'        => ['nullable', 'string', 'max:100'],
            'address'       => ['nullable', 'string', 'max:20'],
            'full_address'  => ['required', 'string', 'max:250'],
            'image'         => ['nullable', 'file', 'image', 'max:2048'],
            'longitude'     => ['nullable', 'numeric'],
            'latitude'      => ['nullable', 'numeric'],
            'featured'      => ['nullable', 'boolean'],
            'status'        => ['nullable', 'boolean'],
            'position'      => ['nullable', 'integer'],
        ];
    }

    public function messages(): array
    {
        return [
            'name.required' => 'Tên khu vực là bắt buộc.',
            'name.max'      => 'Tên khu vực không được vượt quá 100 ký tự.',

            'province_code.required' => 'Vui lòng chọn tỉnh/thành phố.',
            'province_code.max'      => 'Mã tỉnh không được vượt quá 20 ký tự.',

            'district_code.required' => 'Vui lòng chọn quận/huyện.',
            'district_code.max'      => 'Mã quận không được vượt quá 20 ký tự.',

            'ward_code.max' => 'Mã phường không được vượt quá 20 ký tự.',
            'street.max'    => 'Tên đường không được vượt quá 100 ký tự.',
            'address.max'   => 'Địa chỉ không được vượt quá 20 ký tự.',
            'full_address.required' => 'Vui lòng nhập địa chỉ đầy đủ.',
            'full_address.max'      => 'Địa chỉ đầy đủ không được vượt quá 250 ký tự.',

            'image.image' => 'Tệp phải là hình ảnh.',
            'image.max'   => 'Kích thước ảnh tối đa là 2MB.',

            'longitude.numeric' => 'Kinh độ phải là số.',
            'latitude.numeric'  => 'Vĩ độ phải là số.',

            'featured.boolean' => 'Trường nổi bật chỉ chấp nhận giá trị đúng hoặc sai.',
            'status.boolean'   => 'Trường trạng thái chỉ chấp nhận giá trị đúng hoặc sai.',

            'position.integer' => 'Thứ tự phải là số nguyên.',
        ];
    }
}
