<?php

namespace App\Http\Requests\Admin;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdateUserRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $userId = $this->route('user');
        return [
            'fullname'      => ['sometimes', 'required', 'string', 'max:255'],
            'phone'         => ['sometimes', 'nullable', 'string', 'max:30'],
            'email'         => [
                'sometimes',
                'nullable',
                'email',
                'max:45',
                Rule::unique('users', 'email')->ignore($userId),
            ],
            'address'       => ['sometimes', 'nullable', 'string', 'max:255'],
            'date_of_birth' => ['sometimes', 'nullable', 'date'],
            'gender' => ['nullable', 'in:male,female,other'],
            'status'        => ['sometimes', 'boolean'],
            'supporter_id'  => ['sometimes', 'nullable', 'integer', 'exists:erp_members,id'],
        ];
    }

    public function messages(): array
    {
        return [
            'fullname.required'      => 'Bạn phải nhập họ và tên.',
            'fullname.string'        => 'Họ tên phải là chuỗi ký tự.',
            'fullname.max'           => 'Họ tên không được vượt quá 255 ký tự.',

            'phone.string'           => 'Số điện thoại phải là chuỗi.',
            'phone.max'              => 'Số điện thoại tối đa 30 ký tự.',

            'email.email'            => 'Email không đúng định dạng.',
            'email.max'              => 'Email tối đa 45 ký tự.',
            'email.unique'           => 'Email này đã được sử dụng bởi người khác.',

            'address.string'         => 'Địa chỉ phải là chuỗi.',
            'address.max'            => 'Địa chỉ tối đa 255 ký tự.',

            'date_of_birth.date'     => 'Ngày sinh không đúng định dạng ngày tháng.',

            'gender.in'              => 'Giới tính không hợp lệ.',

            'status.boolean'         => 'Trạng thái phải là true hoặc false.',

            'supporter_id.integer'   => 'ID người phụ trách không hợp lệ',
            'supporter_id.exists'    => 'Người phụ trách không tồn tại trong hệ thống.',
        ];
    }
}
