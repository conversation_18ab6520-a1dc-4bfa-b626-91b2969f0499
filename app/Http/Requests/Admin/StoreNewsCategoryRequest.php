<?php

namespace App\Http\Requests\Admin;

use Illuminate\Foundation\Http\FormRequest;

class StoreNewsCategoryRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'title'           => ['required', 'string', 'max:100'],
            'parents'         => ['nullable', 'integer', 'exists:news_categories,id'],
            'image'           => ['nullable', 'image', 'max:2048'],
            'description'     => ['nullable', 'string'],
            'code' => ['nullable', 'string', 'max:100', 'unique:news_categories,code'],
            'position'        => ['nullable', 'integer'],
            'status'          => ['required', 'in:0,1'],
            'seo_title'       => ['nullable', 'string', 'max:100'],
            'seo_description' => ['nullable', 'string'],
            'seo_keywords'    => ['nullable', 'string'],
        ];
    }

    public function messages(): array
    {
        return [
            'title.required' => 'Tiêu đề là bắt buộc.',
            'title.max' => 'Tiêu đề không được vượt quá 100 ký tự.',
            'parents.integer' => 'Danh mục cha không hợp lệ.',
            'parents.exists' => 'Danh mục cha không tồn tại.',
            'image.image' => 'Hình ảnh không đúng định dạng.',
            'image.max' => 'Hình ảnh không được vượt quá 2MB.',
            'code.max' => 'Mã code không được vượt quá 100 ký tự.',
            'position.integer' => 'Vị trí phải là số.',
            'status.required' => 'Trạng thái là bắt buộc.',
            'status.in' => 'Trạng thái không hợp lệ.',
            'seo_title.max' => 'Tiêu đề SEO không được vượt quá 100 ký tự.',
        ];
    }
}
