<?php

namespace App\Http\Requests\Admin;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdateSystemConfigRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $systemConfigId = $this->route('system_config');
        return [
            'name' => ['required', 'string', 'max:255'],
            'code' => [
                'required',
                'string',
                'max:255',
                Rule::unique('system_config', 'code')->ignore($systemConfigId),
            ],
            'title' => ['nullable', 'string', 'max:255'],
            'description' => ['nullable', 'string', 'max:255'],
            'data' => ['nullable', 'string'],
            'image'          => ['nullable', 'image', 'max:2048'],
            'link' => ['nullable', 'string'],
            'url_target' => ['nullable', 'string', Rule::in(['_self', '_blank', '_parent', '_top', null])],
            'status' => ['nullable', 'boolean'],

        ];
    }

    public function messages(): array
    {
        return [
            'code.required' => 'Mã cấu hình không được để trống.',
            'code.unique' => 'Mã cấu hình đã tồn tại.',
            'code.string' => 'Mã cấu hình phải là chuỗi.',
            'code.max' => 'Mã cấu hình không được vượt quá :max ký tự.',
            
            'name.required' => 'Tên cấu hình không được để trống.',
            'name.string' => 'Tên cấu hình phải là chuỗi.',
            'name.max' => 'Tên cấu hình không được vượt quá :max ký tự.',

            'title.string' => 'Tiêu đề phải là chuỗi.',
            'title.max' => 'Tiêu đề không được vượt quá :max ký tự.',

            'description.string' => 'Mô tả phải là chuỗi.',
            'description.max' => 'Mô tả không được vượt quá :max ký tự.',

            'data.string' => 'Dữ liệu phải là chuỗi.',

            'image.image'          => 'Hình ảnh không đúng định dạng.',
            'image.max'            => 'Hình ảnh không được vượt quá 2MB.',

            'link.string' => 'Liên kết phải là chuỗi.',

            'url_target.string' => 'Mục tiêu URL phải là chuỗi.',
            'url_target.in' => 'Mục tiêu URL không hợp lệ.',

            'status.boolean' => 'Trạng thái phải là giá trị boolean (true/false hoặc 0/1).',
        ];
    }
}
