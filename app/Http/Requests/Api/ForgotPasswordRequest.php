<?php

namespace App\Http\Requests\Api;

use App\Helpers\ValidationHelper;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Contracts\Validation\Validator;

class ForgotPasswordRequest extends FormRequest
{
  /**
   * Determine if the user is authorized to make this request.
   */
  public function authorize(): bool
  {
    return true;
  }

  /**
   * Get the validation rules that apply to the request.
   */
  public function rules(): array
  {
    return [
      'identifier' => ValidationHelper::getIdentifierValidationRulesForUser(),
    ];
  }

  /**
   * Get custom messages for validator errors.
   */
  public function messages(): array
  {
    return [
      'identifier.required' => 'Email hoặc số điện thoại là bắt buộc',
      'identifier.email'    => 'Email không đúng định dạng',
      'identifier.regex'    => 'Số điện thoại không đúng định dạng',
    ];
  }

  /**
   * Handle a failed validation attempt.
   */
  protected function failedValidation(Validator $validator)
  {
    throw new HttpResponseException(
      response()->json([
        'success' => false,
        'message' => 'D<PERSON> liệu không hợp lệ',
        'errors'  => $validator->errors()
      ], 422)
    );
  }
}
