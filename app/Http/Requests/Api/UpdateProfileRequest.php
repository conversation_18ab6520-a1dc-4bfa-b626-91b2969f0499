<?php

namespace App\Http\Requests\Api;

use App\Helpers\ValidationHelper;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Contracts\Validation\Validator;

class UpdateProfileRequest extends FormRequest
{
  /**
   * Determine if the user is authorized to make this request.
   */
  public function authorize(): bool
  {
    return true;
  }

  /**
   * Get the validation rules that apply to the request.
   */
  public function rules(): array
  {
    return [
      'fullname'       => 'sometimes|string|max:255',
      'email'          => ValidationHelper::getEmailValidationRules(false, $this->user()->id),
      'phone'          => ValidationHelper::getPhoneValidationRules(false, $this->user()->id),
      'address'        => 'sometimes|string|max:500',
      'avatar'         => [
        'nullable',
        'image',
        'mimes:jpeg,png,jpg,gif,svg,webp',
        'max:2048',
      ],
      'avatar_removed' => ['nullable', 'boolean'],
    ];
  }

  /**
   * Get custom validation error messages.
   */
  public function messages(): array
  {
    return [
      'fullname.max'        => 'Họ tên không được vượt quá 255 ký tự',
      'address.max'         => 'Địa chỉ không được vượt quá 500 ký tự',
      'avatar.image'        => 'Tệp được tải lên phải là một hình ảnh.',
      'avatar.mimes'        => 'Hình ảnh phải có định dạng JPEG, PNG, JPG, GIF, SVG hoặc WEBP.',
      'avatar.max'          => 'Kích thước hình ảnh không được vượt quá 2MB.',
      'avatar_removed.boolean' => 'Giá trị avatar_removed phải là boolean.',
    ];
  }

  /**
   * Handle a failed validation attempt.
   *
   * @param Validator $validator
   * @return void
   *
   * @throws HttpResponseException
   */
  protected function failedValidation(Validator $validator)
  {
    throw new HttpResponseException(
      response()->json([
        'success' => false,
        'message' => 'Dữ liệu không hợp lệ',
        'errors'  => $validator->errors()
      ], 422)
    );
  }
}
