<?php

namespace App\Http\Requests\Api;

use Illuminate\Foundation\Http\FormRequest;

class ChangePasswordRequest extends FormRequest
{
  /**
   * Determine if the user is authorized to make this request.
   */
  public function authorize(): bool
  {
    return true;
  }

  /**
   * Get the validation rules that apply to the request.
   */
  public function rules(): array
  {
    return [
      'current_password' => 'required|string',
      'password' => 'required|string|min:6|confirmed',
      'password_confirmation' => 'required|string',
    ];
  }

  /**
   * Get custom messages for validator errors.
   */
  public function messages(): array
  {
    return [
      'current_password.required' => 'Vui lòng nhập mật khẩu hiện tại',
      'password.required' => 'Vui lòng nhập mật khẩu mới',
      'password.min' => 'Mật khẩu phải có ít nhất 6 ký tự',
      'password.confirmed' => 'Xác nhận mật khẩu không khớp',
      'password_confirmation.required' => 'Vui lòng xác nhận mật khẩu mới',
    ];
  }
}
