<?php

namespace App\Http\Requests\Api;

use App\Helpers\ValidationHelper;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Contracts\Validation\Validator;

class VerifyOtpRequest extends FormRequest
{
  /**
   * Determine if the user is authorized to make this request.
   */
  public function authorize(): bool
  {
    return true;
  }

  /**
   * Get the validation rules that apply to the request.
   */
  public function rules(): array
  {
    return [
      'identifier' => [
        'required',
        'string',
        function ($attribute, $value, $fail) {
          if (!ValidationHelper::isValidEmail($value) && !ValidationHelper::isValidVietnamPhone($value)) {
            $fail('Vui lòng nhập email hoặc số điện thoại hợp lệ');
          }
        }
      ],
      'otp'        => 'required|string|size:6',
    ];
  }

  /**
   * Get custom messages for validator errors.
   */
  public function messages(): array
  {
    return [
      'identifier.required' => 'Email hoặc số điện thoại là bắt buộc',
      'identifier.email'    => 'Email không đúng định dạng',
      'identifier.regex'    => 'Số điện thoại không đúng định dạng',
      'otp.required'        => 'Mã OTP là bắt buộc',
      'otp.string'          => 'Mã OTP phải là chuỗi ký tự',
      'otp.size'            => 'Mã OTP phải có 6 chữ số',
    ];
  }

  /**
   * Handle a failed validation attempt.
   */
  protected function failedValidation(Validator $validator)
  {
    throw new HttpResponseException(
      response()->json([
        'success' => false,
        'message' => 'Dữ liệu không hợp lệ',
        'errors'  => $validator->errors()
      ], 422)
    );
  }
}
