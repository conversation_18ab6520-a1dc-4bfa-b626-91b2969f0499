<?php

namespace App\Http\Requests\Api;

use App\Helpers\ValidationHelper;
use App\Rules\CustomEmail;
use App\Rules\VietnamPhone;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Contracts\Validation\Validator;

class RegisterRequest extends FormRequest
{
  /**
   * Determine if the user is authorized to make this request.
   */
  public function authorize(): bool
  {
    return true;
  }

  /**
   * Get the validation rules that apply to the request.
   *
   * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
   */
  public function rules(): array
  {
    return [
      'fullname'              => [
        'required',
        'string',
        'max:255',
        'regex:' . config('validation.regex.fullname')
      ],
      'identifier'            => ValidationHelper::getIdentifierValidationRules(),
      'password'              => [
        'required',
        'string',
        'min:6',
        'confirmed',
        'regex:' . config('validation.regex.password.basic')
      ],
      'password_confirmation' => 'required|string',
      'address'               => 'nullable|string|max:500',
    ];
  }

  /**
   * Get custom messages for validator errors.
   *
   * @return array
   */
  public function messages(): array
  {
    return [
      'fullname.required'              => 'Họ tên là bắt buộc',
      'fullname.max'                   => 'Họ tên không được vượt quá 255 ký tự',
      'fullname.regex'                 => config('validation.messages.fullname'),
      'identifier.required'            => 'Email hoặc số điện thoại là bắt buộc',
      'password.required'              => 'Mật khẩu là bắt buộc',
      'password.min'                   => 'Mật khẩu phải có ít nhất 6 ký tự',
      'password.confirmed'             => 'Xác nhận mật khẩu không khớp',
      'password.regex'                 => config('validation.messages.password.basic'),
      'password_confirmation.required' => 'Xác nhận mật khẩu là bắt buộc',
      'address.max'                    => 'Địa chỉ không được vượt quá 500 ký tự',
    ];
  }

  /**
   * Handle a failed validation attempt.
   *
   * @param Validator $validator
   * @return void
   *
   * @throws HttpResponseException
   */
  protected function failedValidation(Validator $validator)
  {
    throw new HttpResponseException(
      response()->json([
        'success' => false,
        'message' => 'Dữ liệu không hợp lệ',
        'errors'  => $validator->errors()
      ], 422)
    );
  }
}
