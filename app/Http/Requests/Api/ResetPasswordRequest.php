<?php

namespace App\Http\Requests\Api;

use App\Helpers\ValidationHelper;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Contracts\Validation\Validator;

class ResetPasswordRequest extends FormRequest
{
  /**
   * Determine if the user is authorized to make this request.
   */
  public function authorize(): bool
  {
    return true;
  }

  /**
   * Get the validation rules that apply to the request.
   */
  public function rules(): array
  {
    return [
      'token'        => 'required|string|min:64|max:64',
      'password'     => ValidationHelper::getPasswordValidationRules(),
      'password_confirmation' => 'required|string|same:password',
    ];
  }

  /**
   * Get custom messages for validator errors.
   */
  public function messages(): array
  {
    return [
      'token.required'        => 'Token đặt lại mật khẩu là bắt buộc',
      'token.string'          => 'Token phải là chuỗi ký tự',
      'token.min'             => 'Token không hợp lệ',
      'token.max'             => 'Token không hợp lệ',
      'password.required'     => 'Mật khẩu mới là bắt buộc',
      'password.min'          => 'Mật khẩu phải có ít nhất 8 ký tự',
      'password.regex'        => 'Mật khẩu phải chứa ít nhất 1 chữ hoa, 1 chữ thường, 1 số và 1 ký tự đặc biệt',
      'password_confirmation.required' => 'Xác nhận mật khẩu là bắt buộc',
      'password_confirmation.same'     => 'Xác nhận mật khẩu không khớp',
    ];
  }

  /**
   * Handle a failed validation attempt.
   */
  protected function failedValidation(Validator $validator)
  {
    throw new HttpResponseException(
      response()->json([
        'success' => false,
        'message' => 'Dữ liệu không hợp lệ',
        'errors'  => $validator->errors()
      ], 422)
    );
  }
}
