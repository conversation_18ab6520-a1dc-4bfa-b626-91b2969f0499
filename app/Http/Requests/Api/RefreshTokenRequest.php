<?php

namespace App\Http\Requests\Api;

use Illuminate\Foundation\Http\FormRequest;

class RefreshTokenRequest extends FormRequest
{
  /**
   * Determine if the user is authorized to make this request.
   */
  public function authorize(): bool
  {
    return true;
  }

  /**
   * Get the validation rules that apply to the request.
   */
  public function rules(): array
  {
    return [
      'refresh_token' => 'required|string|min:32',
    ];
  }

  /**
   * Get custom error messages.
   */
  public function messages(): array
  {
    return [
      'refresh_token.required' => 'Refresh token là bắt buộc',
      'refresh_token.string'   => 'Refresh token phải là chuỗi',
      'refresh_token.min'      => 'Refresh token không hợp lệ',
    ];
  }
}
