<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class VerifyPhoneRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */

    public function rules(): array
    {
        return [
            'phone' => ['required', 'string'],
            'otp'   => ['required', 'digits:6'],
        ];
    }

    public function phone(): string
    {
        return $this->input('phone');
    }

    public function otp(): string
    {
        return $this->input('otp');
    }

    public function messages()
{
    return [
        'phone.required' => 'Vui lòng nhập số điện thoại.',
        'otp.required' => 'Đã xảy ra lỗi khi gửi mã xác thực',
    ];
}
}
