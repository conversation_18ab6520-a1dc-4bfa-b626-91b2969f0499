<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Validation\ValidationException;


class RegisterRequest extends FormRequest
{
  /**
   * Determine if the user is authorized to make this request.
   */
  public function authorize(): bool
  {
    return true;
  }

  /**
   * Get the validation rules that apply to the request.
   *
   * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
   */
  public function rules(): array
  {
    return [
      'fullname' => 'nullable|string|max:255',
      'email_or_phone'    => 'required|string',
      'email'    => [
        'nullable',
        'email',
        'max:45',
        Rule::unique('users', 'email'),
        'required_without:phone',
      ],
      'phone'    => [
        'nullable',
        'string',
        'max:30',
        Rule::unique('users', 'phone'),
        'required_without:email',
      ],
      'password' => 'required|string|min:8|confirmed',
      'password_confirmation' => 'required|string'
    ];
  }

  public function messages()
  {
    return [
      'email_or_phone.required' => 'Email hoặc số điện thoại không được để trống.',
      'email.email'               => 'Email không đúng định dạng.',
      'email.unique'              => 'Email này đã được đăng ký.',
      'email.required_without'    => 'Email hoặc số điện thoại không hợp lệ.',
      'phone.unique'              => 'Số điện thoại này đã được đăng ký.',
      'phone.required_without'    => 'Email hoặc số điện thoại không hợp lệ.',
      'password.required'      => 'Mật khẩu không được để trống.',
      'password.min'           => 'Mật khẩu phải có ít nhất :min ký tự.',
      'password.confirmed'     => 'Xác nhận mật khẩu không khớp.',
      'password_confirmation.required'      => 'Xác nhận mật khẩu không được để trống.',
    ];
  }

  protected function prepareForValidation()
  {
    $input = $this->input('email_or_phone');
    $email = null;
    $phone = null;

    if (filter_var($input, FILTER_VALIDATE_EMAIL)) {
      $email = $input;
    } else if (preg_match('/^[0-9]{9,15}$/', $input)) {
      $phone = $input;
    }

    $this->merge([
      'email' => $email,
      'phone' => $phone,
    ]);
  }

  protected function failedValidation(Validator $validator)
  {
    $errors = $validator->errors()->toArray();
    $emailOrPhoneErrors = [];

    if (isset($errors['email'])) {
      $emailOrPhoneErrors = array_merge($emailOrPhoneErrors, $errors['email']);
      unset($errors['email']);
    }

    if (isset($errors['phone'])) {
      $emailOrPhoneErrors = array_merge($emailOrPhoneErrors, $errors['phone']);
      unset($errors['phone']);
    }

    if (!empty($emailOrPhoneErrors)) {
      $errors['email_or_phone'] = array_values(array_unique($emailOrPhoneErrors));
    }

    throw new ValidationException($validator, response()->json([
      'message' => 'Dữ liệu không hợp lệ.',
      'errors' => $errors,
    ], 422));
  }
}
