<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;

class BaseController extends Controller
{
  /**
   * Success response method.
   *
   * @param mixed $result
   * @param string $message
   * @param int $code
   * @return JsonResponse
   */
  public function sendResponse($result = null, string $message = 'Success', int $code = 200): JsonResponse
  {
    $response = [
      'success' => true,
      'message' => $message,
      'code'    => $code,
    ];

    if (!is_null($result)) {
      $response['data'] = $result;
    }

    return response()->json($response, $code);
  }

  /**
   * Error response method.
   *
   * @param string $error
   * @param array $errorMessages
   * @param int $code
   * @return JsonResponse
   */
  public function sendError(string $error, array $errorMessages = [], int $code = 400): JsonResponse
  {
    $response = [
      'success' => false,
      'message' => $error,
      'code'    => $code,
    ];

    if (!empty($errorMessages)) {
      $response['errors'] = $errorMessages;
    }

    return response()->json($response, $code);
  }

  /**
   * Validation error response method.
   *
   * @param array $errors
   * @param string $message
   * @return JsonResponse
   */
  public function sendValidationError(array $errors, string $message = 'Dữ liệu không hợp lệ'): JsonResponse
  {
    return $this->sendError($message, $errors, 422);
  }

  /**
   * Unauthorized response method.
   *
   * @param string $message
   * @return JsonResponse
   */
  public function sendUnauthorized(string $message = 'Không có quyền truy cập'): JsonResponse
  {
    return $this->sendError($message, [], 401);
  }

  /**
   * Not found response method.
   *
   * @param string $message
   * @return JsonResponse
   */
  public function sendNotFound(string $message = 'Không tìm thấy dữ liệu'): JsonResponse
  {
    return $this->sendError($message, [], 404);
  }

  /**
   * Server error response method.
   *
   * @param string $message
   * @return JsonResponse
   */
  public function sendServerError(string $message = 'Lỗi hệ thống'): JsonResponse
  {
    return $this->sendError($message, [], 500);
  }

  /**
   * Too many requests response method.
   *
   * @param string $message
   * @return JsonResponse
   */
  public function sendTooManyRequests(string $message = 'Quá nhiều request'): JsonResponse
  {
    return $this->sendError($message, [], 429);
  }

  /**
   * Forbidden response method.
   *
   * @param string $message
   * @return JsonResponse
   */
  public function sendForbidden(string $message = 'Truy cập bị từ chối'): JsonResponse
  {
    return $this->sendError($message, [], 403);
  }
}
