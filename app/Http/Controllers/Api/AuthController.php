<?php

namespace App\Http\Controllers\Api;

use App\DTOs\LoginData;
use App\DTOs\RegisterData;
use App\DTOs\UpdateProfileData;
use App\Helpers\ValidationHelper;
use App\Http\Requests\Api\LoginRequest;
use App\Http\Requests\Api\RegisterRequest;
use App\Http\Requests\Api\RefreshTokenRequest;
use App\Http\Requests\Api\UpdateProfileRequest;
use App\Http\Requests\Api\ForgotPasswordRequest;
use App\Http\Requests\Api\ResetPasswordRequest;
use App\Services\Interfaces\AuthServiceInterface;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Validation\ValidationException;
use Illuminate\Support\Facades\Log;
use App\Http\Requests\Api\ChangePasswordRequest;
use App\Http\Requests\Api\VerifyOtpRequest;

class AuthController extends BaseController
{
  protected AuthServiceInterface $authService;

  public function __construct(AuthServiceInterface $authService)
  {
    $this->authService = $authService;
  }

  /**
   * Register a new user
   */
  public function register(RegisterRequest $request): JsonResponse
  {
    try {
      $data = $request->validated();

      $dto = new RegisterData(
        $data['fullname'],
        $data['identifier'],
        $data['password'],
        $data['password_confirmation'],
        $data['address'] ?? null
      );

      $result = $this->authService->registerWithResponse($dto);

      return $this->sendResponse($result['data'], $result['message'], 201);
    } catch (ValidationException $e) {
      return $this->sendValidationError($e->errors(), 'Thông tin đăng ký không hợp lệ');
    } catch (\InvalidArgumentException $e) {
      return $this->sendValidationError(['identifier' => [$e->getMessage()]], 'Dữ liệu không hợp lệ');
    } catch (\Exception $e) {
      Log::error('Register endpoint error', [
        'ip'          => $request->ip(),
        'user_agent'  => $request->header('User-Agent'),
        'error'       => $e->getMessage()
      ]);
      return $this->sendServerError('Đã xảy ra lỗi hệ thống');
    }
  }

  /**
   * Login user
   */
  public function login(LoginRequest $request): JsonResponse
  {
    try {
      $data = $request->validated();

      $dto = new LoginData(
        $data['identifier'],
        $data['password']
      );

      $result = $this->authService->loginWithTokens($dto, [
        'device_name' => $request->input('device_name'),
        'ip_address'  => $request->ip(),
        'user_agent'  => $request->header('User-Agent')
      ]);

      return $this->sendResponse($result['data'], $result['message']);
    } catch (ValidationException $e) {
      return $this->sendValidationError($e->errors());
    } catch (\Exception $e) {
      Log::error('Login endpoint error', [
        'ip_address' => $request->ip(),
        'user_agent' => $request->header('User-Agent'),
        'error' => $e->getMessage()
      ]);
      return $this->sendServerError('Đăng nhập thất bại');
    }
  }

  /**
   * Logout user
   */
  public function logout(): JsonResponse
  {
    try {
      $this->authService->logout();
      return $this->sendResponse(null, 'Đăng xuất thành công', 204);
    } catch (\Exception $e) {
      Log::error('Logout failed', ['error' => $e->getMessage()]);
      return $this->sendServerError('Đăng xuất thất bại');
    }
  }

  /**
   * Refresh access token
   */
  public function refresh(RefreshTokenRequest $request): JsonResponse
  {
    try {
      $result = $this->authService->refreshToken($request->validated()['refresh_token']);
      return $this->sendResponse($result, 'Token đã được làm mới');
    } catch (\Exception $e) {
      Log::error('Refresh token failed', [
        'ip'          => $request->ip(),
        'user_agent'  => $request->header('User-Agent'),
        'error'       => $e->getMessage()
      ]);
      return $this->sendUnauthorized('Làm mới token thất bại');
    }
  }

  /**
   * Logout from all devices
   */
  public function logoutAll(Request $request): JsonResponse
  {
    try {
      $this->authService->logoutAll($request->user());
      return $this->sendResponse(null, 'Đã đăng xuất khỏi tất cả thiết bị');
    } catch (\Exception $e) {
      Log::error('Logout all failed', [
        'user_id' => $request->user()->id,
        'error' => $e->getMessage()
      ]);
      return $this->sendServerError('Đã xảy ra lỗi khi đăng xuất');
    }
  }

  /**
   * Get user profile
   */
  public function profile(Request $request): JsonResponse
  {
    try {
      $profile = $this->authService->getUserProfile($request->user());
      return $this->sendResponse($profile, 'Lấy thông tin người dùng thành công');
    } catch (\Exception $e) {
      Log::error('Get profile failed', [
        'user_id' => $request->user()->id,
        'error' => $e->getMessage()
      ]);
      return $this->sendServerError('Đã xảy ra lỗi khi lấy thông tin');
    }
  }

  /**
   * Update user profile
   */
  public function updateProfile(UpdateProfileRequest $request): JsonResponse
  {
    dd($request->all());
    try {
      $dto            = UpdateProfileData::fromRequest($request);
      $user           = $request->user();
      $processedData  = $dto->processAvatar($user->id, $user->avatar);
      $result         = $this->authService->updateUserProfile($user, $processedData);

      return $this->sendResponse($result['data'], $result['message']);
    } catch (\Exception $e) {
      Log::error('Update profile failed', [
        'user_id'     => $request->user()->id,
        'error'       => $e->getMessage()
      ]);
      return $this->sendServerError('Đã xảy ra lỗi khi cập nhật thông tin');
    }
  }

  /**
   * Change password
   */
  public function changePassword(ChangePasswordRequest $request): JsonResponse
  {
    try {
      $this->authService->changePassword(
        $request->user(),
        $request->current_password,
        $request->password
      );

      return $this->sendResponse(null, 'Đổi mật khẩu thành công');
    } catch (ValidationException $e) {
      return $this->sendValidationError($e->errors());
    } catch (\Exception $e) {
      Log::error('Change password failed', [
        'user_id' => $request->user()->id,
        'error'   => $e->getMessage(),
      ]);
      return $this->sendServerError('Đổi mật khẩu thất bại. Vui lòng thử lại sau.');
    }
  }

  /**
   * Verify email/phone with OTP
   */
  public function verify(Request $request): JsonResponse
  {
    try {
      $request->validate([
        'identifier' => ValidationHelper::getIdentifierValidationRulesForUser(),
        'otp'        => 'required|string|size:6',
      ]);

      $result = $this->authService->verify($request->identifier, $request->otp);

      return $this->sendResponse($result, 'Email đã được xác thực thành công. Bạn đã được đăng nhập.');
    } catch (ValidationException $e) {
      return $this->sendValidationError($e->errors());
    } catch (\Exception $e) {
      Log::error('Verification failed', [
        'identifier' => $request->identifier,
        'error' => $e->getMessage()
      ]);
      return $this->sendServerError('Xác thực thất bại');
    }
  }

  /**
   * Resend verification code
   */
  public function resendVerification(Request $request): JsonResponse
  {
    try {
      $request->validate([
        'identifier' => ValidationHelper::getIdentifierValidationRulesForUser(),
      ]);

      $result = $this->authService->resendVerification($request->identifier);
      return $this->sendResponse(null, $result['message']);
    } catch (ValidationException $e) {
      return $this->sendValidationError($e->errors());
    } catch (\Exception $e) {
      Log::error('Resend verification failed', [
        'identifier' => $request->identifier,
        'error' => $e->getMessage()
      ]);
      return $this->sendServerError('Gửi lại mã xác thực thất bại');
    }
  }

  /**
   * Forgot password - send OTP
   */
  public function forgotPassword(ForgotPasswordRequest $request)
  {
    $result = $this->authService->forgotPassword($request->identifier);

    return response()->json($result, $result['success'] ? 200 : 400);
  }

  public function verifyOtpForPasswordReset(VerifyOtpRequest $request)
  {
    $result = $this->authService->verifyOtpForPasswordReset(
      $request->identifier,
      $request->otp
    );

    return response()->json($result, $result['success'] ? 200 : 400);
  }

  public function resetPassword(ResetPasswordRequest $request)
  {
    $result = $this->authService->resetPassword(
      $request->token,
      $request->password
    );

    return response()->json($result, $result['success'] ? 200 : 400);
  }
}
