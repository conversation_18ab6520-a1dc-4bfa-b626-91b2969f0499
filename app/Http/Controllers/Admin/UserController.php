<?php

namespace App\Http\Controllers\Admin;

use App\DTOs\Admin\StoreUserData;
use App\DTOs\Admin\UpdateUserData;
use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\StoreUserRequest;
use App\Http\Requests\Admin\UpdateUserRequest;
use App\Models\User;
use App\Services\Admin\Interfaces\UserServiceInterface;
use Illuminate\Http\Request;

class UserController extends BaseController
{
    public function __construct(UserServiceInterface $userService)
    {
        parent::__construct();
        $this->model = User::class;
        $this->service = $userService;
        $this->viewPath = 'admin.users';
        $this->routeName = 'admin.users';
        $this->requestMap = [
            'store'  => StoreUserRequest::class,
            'update' => UpdateUserRequest::class,
        ];
        $this->dtoMap = [
            'store'  => StoreUserData::class,
            'update' => UpdateUserData::class,
        ];
    }

    protected function customizeDataTableBuilder($dataTable, Request $request)
    {
        $this->addStatusToggleColumn($dataTable, 'status');
            $dataTable->editColumn('created_at_formatted', function ($row) {
                return $row->created_at ? $row->created_at->format('d/m/Y') : null;
            })
            ->addColumn('supporter_username', function ($row) {
                return $row->supporter->fullname ?? '';
            })
            ->orderColumn('supporter_username', function ($query, $order) {
                $query->join('erp_members', 'users.supporter_id', '=', 'erp_members.id')
                    ->orderBy('erp_members.fullname', $order)
                    ->select('users.*');
            })
            ->filterColumn('supporter_username', function ($query, $keyword) {
                $query->whereHas('supporter', function ($q) use ($keyword) {
                    $q->where('erp_members.fullname', 'like', "%{$keyword}%");
                });
            })
            ->orderColumn('creator_username', function ($query, $order) {
                $query->join('erp_members as c', 'users.creator_id', '=', 'c.id')
                    ->orderBy('c.username', $order)
                    ->select('users.*');
            })
            ->filterColumn('creator_username', function ($query, $keyword) {
                $query->whereHas('creator', function ($q) use ($keyword) {
                    $q->where('username', 'like', "%{$keyword}%");
                });
            });

        return $dataTable;
    }
}
