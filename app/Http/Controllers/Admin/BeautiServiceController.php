<?php

namespace App\Http\Controllers\Admin;

use App\DTOs\Admin\StoreBeautiServiceData;
use App\DTOs\Admin\UpdateBeautiServiceData;
use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\StoreBeautiServiceRequest;
use App\Http\Requests\Admin\UpdateBeautiServiceRequest;
use App\Models\BeautiService;
use App\Models\BeautiStoreService;
use App\Services\Admin\Interfaces\BeautiServiceServiceInterface;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class BeautiServiceController extends BaseController
{
    public function __construct(BeautiServiceServiceInterface $beautiServiceService)
    {
        parent::__construct();
        $this->model = BeautiService::class;
        $this->service = $beautiServiceService;
        $this->viewPath = 'admin.beauti-services';
        $this->routeName = 'admin.beauti-services';;
        $this->requestMap = [
            'store'  => StoreBeautiServiceRequest::class,
            'update' => UpdateBeautiServiceRequest::class,
        ];
        $this->dtoMap = [
            'store'  => StoreBeautiServiceData::class,
            'update' => UpdateBeautiServiceData::class,
        ];
    }

    protected function customizeDataTableBuilder($dataTable, Request $request)
    {
        $this->addStatusToggleColumn($dataTable, 'status');
        $dataTable->editColumn('created_at_formatted', function ($row) {
            return $row->created_at ? $row->created_at->format('d/m/Y') : null;
        })
            ->orderColumn('creator_username', function ($query, $order) {
                $query->join('erp_members as c', 'beauti_services.creator_id', '=', 'c.id')
                    ->orderBy('c.username', $order)
                    ->select('beauti_services.*');
            })
            ->filterColumn('creator_username', function ($query, $keyword) {
                $query->whereHas('creator', function ($q) use ($keyword) {
                    $q->where('username', 'like', "%{$keyword}%");
                });
            })
            ->addColumn('types_list', function ($row) {
                if ($row->relationLoaded('types')) {
                    return $row->types->map(function ($type) {
                        return $type->name;
                    })->implode(', ');
                }
                return '';
            });
        return $dataTable;
    }


    public function byStore(Request $request)
    {
        $storeId = $request->query('store_id');

        $services = BeautiStoreService::where('store_id', $storeId)
            ->with('service:id,name')
            ->get()
            ->map(fn($pivot) => [
                'id'   => $pivot->service->id,
                'name' => $pivot->service->name,
            ]);

        return response()->json($services);
    }

    public function byType(Request $request)
    {
        $typeIds = explode(',', $request->query('beauti_types', ''));

        $serviceIds = DB::table('beauti_type_service')
            ->whereIn('type_id', $typeIds)
            ->distinct()
            ->pluck('service_id');

        $services = BeautiService::whereIn('id', $serviceIds)
            ->select('id', 'name')
            ->orderBy('name')
            ->get();

        return response()->json($services);
    }
}
