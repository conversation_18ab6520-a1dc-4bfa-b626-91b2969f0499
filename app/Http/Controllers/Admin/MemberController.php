<?php

namespace App\Http\Controllers\Admin;

use App\DTOs\Admin\StoreMemberData;
use App\DTOs\Admin\UpdateMemberData;

use App\Http\Requests\Admin\StoreMemberRequest;
use App\Http\Requests\Admin\UpdateMemberRequest;
use App\Models\ErpMember;
use App\Services\Admin\Interfaces\MemberServiceInterface;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Hash;
use Spatie\Permission\Models\Role;
use Illuminate\Support\Facades\Auth;
use Yajra\DataTables\DataTables;

class MemberController extends BaseController
{

    public function __construct(MemberServiceInterface $memberService)
    {
        parent::__construct();
        $this->model = ErpMember::class;
        $this->service = $memberService;
        $this->viewPath = 'admin.members';
        $this->routeName = 'admin.members';
        $this->requestMap = [
            'store'  => StoreMemberRequest::class,
            'update' => UpdateMemberRequest::class,
        ];
        $this->dtoMap = [
            'store'  => StoreMemberData::class,
            'update' => UpdateMemberData::class,
        ];
    }
    protected function formData(): array
    {
        $roles = Role::pluck('name')->map(fn($name) => [
            'id'   => $name,
            'name' => ucfirst($name),
        ])->values()->all();
        $itemRoles = isset($this->item) ? $this->item->getRoleNames()->toArray() : [];
        return compact('roles', 'itemRoles');
    }

    public function resetPassword($id)
    {
        $member = ErpMember::findOrFail($id);

        $salt = Str::random(16);
        $member->salt = $salt;
        $member->password = Hash::make($member->username . $salt);
        $member->save();

        return redirect()->back()->with('success', 'Đặt lại mật khẩu thành công.');
    }

    protected function customizeDataTableBuilder($dataTable, Request $request)
    {
        $this->addStatusToggleColumn($dataTable, 'status');
        $dataTable->editColumn('created_at_formatted', function ($row) {
            return $row->created_at ? $row->created_at->format('d/m/Y') : null;
        })
            ->addColumn('department_name', function ($row) {
                return $row->department->name ?? '';
            })
            ->orderColumn('department_name', function ($query, $order) {
                $query->join('erp_departments', 'erp_members.department_id', '=', 'erp_departments.id')
                    ->orderBy('erp_departments.name', $order)
                    ->select('erp_members.*');
            })
            ->filterColumn('department_name', function ($query, $keyword) {
                $query->whereHas('department', function ($q) use ($keyword) {
                    $q->where('erp_departments.name', 'like', "%{$keyword}%");
                });
            });

        return $dataTable;
    }
    
    public function toggleStatus(Request $request, $id)
    {
        $loggedInUserId = Auth::guard('admin')->id();

        if ($id == $loggedInUserId) {
            return response()->json([
                'success' => false,
                'message' => 'Bạn không thể thay đổi trạng thái của chính mình.'
            ], 403);
        }

        return parent::toggleStatus($request, $id);
    }
}
