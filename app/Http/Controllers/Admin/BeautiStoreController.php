<?php

namespace App\Http\Controllers\Admin;

use App\DTOs\Admin\StoreBeautiStoreData;
use App\DTOs\Admin\UpdateBeautiStoreData;
use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\StoreBeautiStoreRequest;
use App\Http\Requests\Admin\UpdateBeautiStoreRequest;
use App\Models\BeautiStore;
use App\Models\LocationDistrict;
use App\Models\LocationProvince;
use App\Models\LocationStreet;
use App\Models\LocationWard;
use App\Services\Admin\Interfaces\BeautiStoreServiceInterface;
use Illuminate\Http\Request;

class BeautiStoreController extends BaseController
{
    public function __construct(BeautiStoreServiceInterface $beautiStoreService)
    {
        parent::__construct();
        $this->model = BeautiStore::class;
        $this->service = $beautiStoreService;
        $this->viewPath = 'admin.beauti-stores';
        $this->routeName = 'admin.beauti-stores';;
        $this->requestMap = [
            'store'  => StoreBeautiStoreRequest::class,
            'update' => UpdateBeautiStoreRequest::class,
        ];
        $this->dtoMap = [
            'store'  => StoreBeautiStoreData::class,
            'update' => UpdateBeautiStoreData::class,
        ];
    }

    protected function formData(): array
    {
        $locationProvince = LocationProvince::select('code', 'name')->get();

        $selectedDistrict = collect();
        $selectedWard = collect();
        $selectedStreet = collect();

        if ($this->item) {
            $selectedDistrict = LocationDistrict::where('code', $this->item->district_code)->select('code', 'name')->get();
            $selectedWard = LocationWard::where('code', $this->item->ward_code)->select('code', 'name')->get();
            $selectedStreet = LocationStreet::where('code', $this->item->street)->select('code', 'name')->get();
        }

        return [
            'locationProvince' => $locationProvince,
            'selectedDistrict' => $selectedDistrict,
            'selectedWard' => $selectedWard,
            'selectedStreet' => $selectedStreet,
        ];
    }


    protected function customizeDataTableBuilder($dataTable, Request $request)
    {
        $this->addStatusToggleColumn($dataTable, 'status');
        $dataTable
            ->editColumn('created_at_formatted', function ($row) {
                return $row->created_at ? $row->created_at->format('d/m/Y') : null;
            })
            ->orderColumn('creator_username', function ($query, $order) {
                $query->join('erp_members as c', 'beauti_stores.creator_id', '=', 'c.id')
                    ->orderBy('c.username', $order)
                    ->select('beauti_stores.*');
            })
            ->filterColumn('creator_username', function ($query, $keyword) {
                $query->whereHas('creator', function ($q) use ($keyword) {
                    $q->where('username', 'like', "%{$keyword}%");
                });
            })
            ->orderColumn('province_name', function ($query, $order) {
                $query->join('location_province as c', 'beauti_stores.province_code', '=', 'c.id')
                    ->orderBy('c.name', $order)
                    ->select('beauti_stores.*');
            })
            ->filterColumn('province_name', function ($query, $keyword) {
                $query->whereHas('province', function ($q) use ($keyword) {
                    $q->where('name', 'like', "%{$keyword}%");
                });
            })
            ->orderColumn('beauti_name', function ($query, $order) {
                $query->join('beauties as b', 'beauti_stores.beauti_id', '=', 'b.id')
                    ->orderBy('b.name', $order)
                    ->select('beauti_stores.*');
            })
            ->filterColumn('beauti_name', function ($query, $keyword) {
                $query->whereHas('beauties', function ($q) use ($keyword) {
                    $q->where('name', 'like', "%{$keyword}%");
                });
            });

        return $dataTable;
    }

    public function byBeauti(Request $request)
    {
        $businessId = $request->query('business_id');

        $stores = BeautiStore::where('beauti_id', $businessId)
            ->select(['id', 'name'])
            ->orderBy('name')
            ->get();

        return response()->json($stores);
    }
}
