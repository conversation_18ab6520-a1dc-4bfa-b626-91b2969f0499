<?php

namespace App\Http\Controllers\Admin;

use App\DTOs\Admin\StoreTeamData;
use App\DTOs\Admin\UpdateTeamData;
use App\Http\Requests\Admin\StoreTeamRequest;
use App\Http\Requests\Admin\UpdateTeamRequest;
use App\Models\ErpDepartment;
use App\Services\Admin\Interfaces\TeamServiceInterface;
use Illuminate\Http\Request;

class TeamController extends BaseController
{
    public function __construct(TeamServiceInterface $teamsService)
    {
        parent::__construct();
        $this->model = ErpDepartment::class;
        $this->service = $teamsService;
        $this->viewPath = 'admin.teams';
        $this->routeName = 'admin.teams';
        $this->requestMap = [
            'store'  => StoreTeamRequest::class,
            'update' => UpdateTeamRequest::class,
        ];
        $this->dtoMap = [
            'store'  => StoreTeamData::class,
            'update' => UpdateTeamData::class,
        ];
    }

    protected function customizeDataTableBuilder($dataTable, Request $request)
    {
        $this->addStatusToggleColumn($dataTable, 'status');
        $dataTable->editColumn('created_at_formatted', function ($row) {
            return $row->created_at ? $row->created_at->format('d/m/Y') : null;
        })
            ->addColumn('manager_name', function ($row) {
                return $row->manager->name ?? '';
            })
            ->orderColumn('manager_name', function ($query, $order) {
                $query->join('erp_members', 'erp_departments.manager_id', '=', 'erp_departments.id')
                    ->orderBy('erp_members.fullname', $order)
                    ->select('erp_members.*');
            })
            ->filterColumn('manager_name', function ($query, $keyword) {
                $query->whereHas('manager', function ($q) use ($keyword) {
                    $q->where('erp_members.fullname', 'like', "%{$keyword}%");
                });
            });

        return $dataTable;
    }
}
