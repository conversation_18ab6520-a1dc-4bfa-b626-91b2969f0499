<?php

namespace App\Http\Controllers\Admin;

use App\DTOs\Admin\StoreNewsCategoryData;
use App\DTOs\Admin\UpdateNewsCategoryData;
use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\StoreNewsCategoryRequest;
use App\Http\Requests\Admin\UpdateNewsCategoryRequest;
use App\Models\NewsCategory;
use App\Services\Admin\Interfaces\NewsCategoryServiceInterface;
use Illuminate\Http\Request;

class NewsCategoriesController extends BaseController
{
    public function __construct(NewsCategoryServiceInterface $NewsCategory)
    {
        parent::__construct();
        $this->model = NewsCategory::class;
        $this->service = $NewsCategory;
        $this->viewPath = 'admin.news-categories';
        $this->routeName = 'admin.news-categories';
        $this->requestMap = [
            'store'  => StoreNewsCategoryRequest::class,
            'update' => UpdateNewsCategoryRequest::class,
        ];
        $this->dtoMap = [
            'store'  => StoreNewsCategoryData::class,
            'update' => UpdateNewsCategoryData::class,
        ];
    }

    protected function customizeDataTableBuilder($dataTable, Request $request)
    {
        $this->addStatusToggleColumn($dataTable, 'status');
        $dataTable->addColumn('parent_name', function ($row) {
            return $row->parent?->title ?? '';
        })
            ->orderColumn('parent_name', function ($query, $order) {
                $query->leftJoin('news_categories as p', 'news_categories.parents', '=', 'p.id')
                    ->orderBy('p.title', $order)
                    ->select('news_categories.*');
            })
            ->filterColumn('parent_name', function ($query, $keyword) {
                $query->whereHas('parent', function ($q) use ($keyword) {
                    $q->where('title', 'like', "%{$keyword}%");
                });
            })
            ->addColumn('creator_username', function ($row) {
                return $row->creator->username ?? '';
            })
            ->orderColumn('creator_username', function ($query, $order) {
                $query->join('erp_members as c', 'news_categories.creator_id', '=', 'c.id')
                    ->orderBy('c.username', $order)
                    ->select('news_categories.*');
            })
            ->filterColumn('creator_username', function ($query, $keyword) {
                $query->whereHas('creator', function ($q) use ($keyword) {
                    $q->where('username', 'like', "%{$keyword}%");
                });
            })
        ;

        return $dataTable;
    }
}
