<?php

namespace App\Http\Controllers\Admin;

use App\DTOs\Admin\StoreLocationWardData;
use App\DTOs\Admin\UpdateLocationWardData;
use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\StoreLocationWardRequest;
use App\Http\Requests\Admin\UpdateLocationWardRequest;
use App\Models\LocationWard;
use App\Services\Admin\Interfaces\LocationWardServiceInterface;
use Illuminate\Http\Request;

class LocationWardsController extends BaseController
{
    public function __construct(LocationWardServiceInterface $LocationWard)
    {
        parent::__construct();
        $this->model = LocationWard::class;
        $this->service = $LocationWard;
        $this->viewPath = 'admin.location-wards';
        $this->routeName = 'admin.location-wards';
        $this->requestMap = [
            'store'  => StoreLocationWardRequest::class,
            'update' => UpdateLocationWardRequest::class,
        ];
        $this->dtoMap = [
            'store'  => StoreLocationWardData::class,
            'update' => UpdateLocationWardData::class,
        ];
    }

    protected function customizeDataTableBuilder($dataTable, Request $request)
    {
        $dataTable->addColumn('location_district_name', function ($row) {
            return $row->district->name ?? '';
        })
            ->orderColumn('location_district_name', function ($query, $order) {
                $query->join('location_district', 'location_ward.district_code', '=', 'location_district.code')
                    ->orderBy('location_district.name', $order)
                    ->select('location_ward.*');
            })
            ->filterColumn('location_district_name', function ($query, $keyword) {
                $query->whereHas('district', function ($q) use ($keyword) {
                    $q->where('location_district.name', 'like', "%{$keyword}%");
                });
            });

        return $dataTable;
    }
}
