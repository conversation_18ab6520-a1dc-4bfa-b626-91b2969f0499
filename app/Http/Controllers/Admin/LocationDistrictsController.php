<?php

namespace App\Http\Controllers\Admin;

use App\DTOs\Admin\StoreLocationDistrictData;
use App\DTOs\Admin\UpdateLocationDistrictData;
use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\StoreLocationDistrictRequest;
use App\Http\Requests\Admin\UpdateLocationDistrictRequest;
use App\Models\LocationDistrict;
use App\Services\Admin\Interfaces\LocationDistrictServiceInterface;
use Illuminate\Http\Request;

class LocationDistrictsController extends BaseController
{
    public function __construct(LocationDistrictServiceInterface $locationDistrict)
    {
        parent::__construct();
        $this->model = LocationDistrict::class;
        $this->service = $locationDistrict;
        $this->viewPath = 'admin.location-districts';
        $this->routeName = 'admin.location-districts';
        $this->requestMap = [
            'store'  => StoreLocationDistrictRequest::class,
            'update' => UpdateLocationDistrictRequest::class,
        ];
        $this->dtoMap = [
            'store'  => StoreLocationDistrictData::class,
            'update' => UpdateLocationDistrictData::class,
        ];
    }

    protected function customizeDataTableBuilder($dataTable, Request $request)
    {
        $dataTable->addColumn('location_province_name', function ($row) {
                return $row->province->name ?? '';
            })
            ->orderColumn('location_province_name', function ($query, $order) {
                $query->join('location_province', 'location_district.province_code', '=', 'location_province.code')
                    ->orderBy('location_province.name', $order)
                    ->select('location_district.*');
            })
            ->filterColumn('location_province_name', function ($query, $keyword) {
                $query->whereHas('province', function ($q) use ($keyword) {
                    $q->where('location_province.name', 'like', "%{$keyword}%");
                });
            });

        return $dataTable;
    }
}
