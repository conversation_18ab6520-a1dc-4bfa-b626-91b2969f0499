<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Traits\DataTables\HasImageColumn;
use App\Traits\DataTables\HasStatusToggle;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use Yajra\DataTables\Facades\DataTables;
use Illuminate\Foundation\Http\FormRequest;


class BaseController extends Controller
{
    use HasStatusToggle;
    use HasImageColumn;

    protected $model;
    protected $item;
    protected $service;
    protected $viewPath;
    protected $routeName;
    protected array $requestMap = [];
    protected array $dtoMap = [];
    protected array $rawColumns = [];
    protected array $accumulatedRawColumns = [];

    public function __construct()
    {
        $this->accumulatedRawColumns[] = 'actions';
    }

    protected function formData(): array
    {
        return [];
    }

    protected function customizeDataTableBuilder($dataTable, Request $request)
    {
        return $dataTable;
    }

    protected function addRawColumn($columnNames): void
    {
        $columnNames = is_array($columnNames) ? $columnNames : [$columnNames];
        $this->accumulatedRawColumns = array_merge($this->accumulatedRawColumns, $columnNames);
    }

    public function index()
    {
        return view("{$this->viewPath}.index");
    }


    public function data(Request $request)
    {
        $query = method_exists($this->service, 'getAll')
            ? $this->service->getAll()
            : $this->model::query();
        $dataTable = DataTables::of($query)
            ->addIndexColumn()
            ->addColumn('actions', function ($row) {
                return view("{$this->viewPath}.actions", compact('row'))->render();
            });
        $dataTable = $this->customizeDataTableBuilder($dataTable, $request);
        $dataTable->rawColumns(array_unique($this->accumulatedRawColumns));
        return $dataTable->make(true);
    }

    public function create()
    {
        $data = $this->formData();
        return view("{$this->viewPath}.create", $data);
    }

    public function store(Request $request)
    {
        try {
            $validated = $this->validateRequest('store');

            if ($dtoClass = $this->dtoMap['store'] ?? null) {
                $dto = $dtoClass::fromRequest($request);
                $this->service->create($dto);
            } else {
                $this->service->create($validated);
            }

            return redirect()->route("{$this->routeName}.index")
                ->with('success', $this->successMessage('thêm mới'));
        } catch (\Illuminate\Validation\ValidationException $e) {
            return redirect()->back()
                ->withErrors($e->errors())
                ->withInput();
        } catch (\Exception $e) {
            return redirect()->back()
                ->withErrors('Đã xảy ra lỗi trong quá trình xử lý. Vui lòng thử lại.'.$e->getMessage())
                ->withInput();
        }
    }

    public function edit($id)
    {
        $this->item = $this->service->getById($id);
        $data = array_merge(['item' => $this->item], $this->formData());
        return view("{$this->viewPath}.edit", $data);
    }

    public function update(Request $request, $id)
    {
        try {
            $validated = $this->validateRequest('update');
            if ($dtoClass = $this->dtoMap['update'] ?? null) {
                $dto = $dtoClass::fromRequest($request);
                $this->service->update($id, $dto);
            } else {
                $this->service->update($id, $validated);
            }
            return redirect()->back()->with('success', $this->successMessage('cập nhật'));
        } catch (\Illuminate\Validation\ValidationException $e) {
            return redirect()->back()
                ->withErrors($e->errors())
                ->withInput();
        } catch (\Exception $e) {
            return redirect()->back()
                ->withErrors('Đã xảy ra lỗi trong quá trình xử lý. Vui lòng thử lại.'. ' ' . $e->getMessage())
                ->withInput();
        }
    }

    public function destroy($id)
    {
        try {
            $this->service->delete($id);
            if (request()->ajax() || request()->wantsJson()) {
                return response()->json([
                    'success' => true,
                    'message' => $this->successMessage('xóa')
                ]);
            }
            return redirect()->route("{$this->routeName}.index")
                ->with('success', $this->successMessage('xóa'));
        } catch (\Exception $e) {
            if (request()->ajax() || request()->wantsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Đã xảy ra lỗi khi xóa bản ghi'.$e->getMessage()
                ], 500);
            }
            return redirect()->route("{$this->routeName}.index")
                ->with('error', 'Đã xảy ra lỗi khi xóa bản ghi'.$e->getMessage());
        }
    }

    protected function validateRequest(string $action): array
    {
        if (!$class = $this->requestMap[$action] ?? null) {
            return [];
        }

        $formRequest = app()->make($class);
        $formRequest->setContainer(app())->validateResolved();

        return $formRequest->validated();
    }

    protected function successMessage($action)
    {
        return "Thao tác {$action} thành công!";
    }
}
