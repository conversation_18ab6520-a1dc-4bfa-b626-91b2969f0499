<?php

namespace App\Http\Controllers\Admin;

use App\DTOs\Admin\StoreBeautiPackageData;
use App\DTOs\Admin\UpdateBeautiPackageData;
use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\StoreBeautiPackageRequest;
use App\Http\Requests\Admin\UpdateBeautiPackageRequest;
use App\Models\BeautiStorePackage;
use App\Services\Admin\Interfaces\BeautiPackageServiceInterface;
use Illuminate\Http\Request;

class BeautiPackageController extends BaseController
{
    public function __construct(BeautiPackageServiceInterface $beautiPackageService)
    {
        parent::__construct();
        $this->model = BeautiStorePackage::class;
        $this->service = $beautiPackageService;
        $this->viewPath = 'admin.beauti-packages';
        $this->routeName = 'admin.beauti-packages';;
        $this->requestMap = [
            'store'  => StoreBeautiPackageRequest::class,
            'update' => UpdateBeautiPackageRequest::class,
        ];
        $this->dtoMap = [
            'store'  => StoreBeautiPackageData::class,
            'update' => UpdateBeautiPackageData::class,
        ];
    }

    protected function customizeDataTableBuilder($dataTable, Request $request)
    {
        $this->addStatusToggleColumn($dataTable, 'status');

        $dataTable->orderColumn('creator_username', function ($query, $order) {
            $query->join('erp_members as c', 'beauti_services.creator_id', '=', 'c.id')
                ->orderBy('c.username', $order)
                ->select('beauti_services.*');
        })
            ->filterColumn('creator_username', function ($query, $keyword) {
                $query->whereHas('creator', function ($q) use ($keyword) {
                    $q->where('username', 'like', "%{$keyword}%");
                });
            });

        return $dataTable;
    }
}
