<?php

namespace App\Http\Controllers\Admin;

use App\DTOs\Admin\StoreNewsArticleData;
use App\DTOs\Admin\UpdateNewsArticleData;
use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\StoreNewsArticleRequest;
use App\Http\Requests\Admin\UpdateNewsArticleRequest;
use App\Models\NewsArticle;
use App\Services\Admin\Interfaces\NewsArticleServiceInterface;
use Illuminate\Http\Request;

class NewsArticleController extends BaseController
{
      public function __construct(NewsArticleServiceInterface $newsArticle)
    {
        parent::__construct();
        $this->model = NewsArticle::class;
        $this->service = $newsArticle;
        $this->viewPath = 'admin.news-article';
        $this->routeName = 'admin.news-article';;
        $this->requestMap = [
            'store'  => StoreNewsArticleRequest::class,
            'update' => UpdateNewsArticleRequest::class,
        ];
        $this->dtoMap = [
            'store'  => StoreNewsArticleData::class,
            'update' => UpdateNewsArticleData::class,
        ];
    }

     protected function customizeDataTableBuilder($dataTable, Request $request)
    {
        $this->addStatusToggleColumn($dataTable, 'status');
        $this->addStatusToggleColumn($dataTable, 'featured');
        $dataTable
            ->addColumn('creator_username', function ($row) {
                return $row->creator->username ?? '';
            })
            ->orderColumn('creator_username', function ($query, $order) {
                $query->join('erp_members as c', 'news_articles.creator_id', '=', 'c.id')
                    ->orderBy('c.username', $order)
                    ->select('news_articles.*');
            })
            ->filterColumn('creator_username', function ($query, $keyword) {
                $query->whereHas('creator', function ($q) use ($keyword) {
                    $q->where('username', 'like', "%{$keyword}%");
                });
            })
            ->addColumn('categories_list', function ($row) {
                if ($row->relationLoaded('categories')) {
                    return $row->categories->map(function ($category) {
                        return $category->title;
                    })->implode(', ');
                }
                return ''; 
            });
        ;

        return $dataTable;
    }
}
