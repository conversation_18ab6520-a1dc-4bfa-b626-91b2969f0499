<?php

namespace App\Http\Controllers\Admin;

use App\DTOs\Admin\StoreBeautiData;
use App\DTOs\Admin\UpdateBeautiData;
use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\StoreBeautiRequest;
use App\Http\Requests\Admin\UpdateBeautiRequest;
use App\Models\Beauti;
use App\Services\Admin\Interfaces\BeautiServiceInterface;
use Illuminate\Http\Request;

class BeautiController extends BaseController
{
     public function __construct(BeautiServiceInterface $beautiService)
    {
        parent::__construct();
        $this->model = Beauti::class;
        $this->service = $beautiService;
        $this->viewPath = 'admin.beauties';
        $this->routeName = 'admin.beauties';
        $this->requestMap = [
            'store'  => StoreBeautiRequest::class,
            'update' => UpdateBeautiRequest::class,
        ];
        $this->dtoMap = [
            'store'  => StoreBeautiData::class,
            'update' => UpdateBeautiData::class,
        ];
    }

    protected function customizeDataTableBuilder($dataTable, Request $request)
    {
        $this->addStatusToggleColumn($dataTable, 'status');
        $dataTable
            ->editColumn('created_at_formatted', function ($row) {
                return $row->created_at ? $row->created_at->format('d/m/Y') : null;
            })
            ->orderColumn('creator_username', function ($query, $order) {
                $query->join('erp_members as c', 'beauti_stores.creator_id', '=', 'c.id')
                    ->orderBy('c.username', $order)
                    ->select('beauti_stores.*');
            })
            ->filterColumn('creator_username', function ($query, $keyword) {
                $query->whereHas('creator', function ($q) use ($keyword) {
                    $q->where('username', 'like', "%{$keyword}%");
                });
            })
            ->addColumn('types_list', function ($row) {
                if ($row->relationLoaded('types')) {
                    return $row->types->map(function ($type) {
                        return $type->name;
                    })->implode(', ');
                }
                return '';
            });

        return $dataTable;
    }
}
