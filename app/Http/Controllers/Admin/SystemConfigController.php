<?php

namespace App\Http\Controllers\Admin;

use App\DTOs\Admin\UpdateSystemConfigData;
use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\UpdateSystemConfigRequest;
use App\Models\SystemConfig;
use App\Services\Admin\Interfaces\SystemConfigServiceInterface;
use Illuminate\Http\Request;

class SystemConfigController extends BaseController
{
    public function __construct(SystemConfigServiceInterface $systemConfig)
    {
        parent::__construct();
        $this->model = SystemConfig::class;
        $this->service = $systemConfig;
        $this->viewPath = 'admin.system-config';
        $this->routeName = 'admin.system-config';
        $this->requestMap = [
            'update' => UpdateSystemConfigRequest::class,
        ];
        $this->dtoMap = [
            'update' => UpdateSystemConfigData::class,
        ];
    }

    protected function customizeDataTableBuilder($dataTable, Request $request)
    {
        $this->addStatusToggleColumn($dataTable, 'status');
        $this->addImageColumn($dataTable, 'image', 150);
        return $dataTable;
    }
}
