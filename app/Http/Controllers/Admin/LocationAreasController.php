<?php

namespace App\Http\Controllers\Admin;

use App\DTOs\Admin\StoreLocationAreaData;
use App\DTOs\Admin\UpdateLocationAreaData;
use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\StoreLocationAreaRequest;
use App\Http\Requests\Admin\UpdateLocationAreaRequest;
use App\Models\LocationArea;
use App\Models\LocationDistrict;
use App\Models\LocationProvince;
use App\Models\LocationStreet;
use App\Models\LocationWard;
use App\Services\Admin\Interfaces\LocationAreaServiceInterface;
use Illuminate\Http\Request;

class LocationAreasController extends BaseController
{
    public function __construct(LocationAreaServiceInterface $locationArea)
    {
        parent::__construct();
        $this->model = LocationArea::class;
        $this->service = $locationArea;
        $this->viewPath = 'admin.location-areas';
        $this->routeName = 'admin.location-areas';
        $this->requestMap = [
            'store'  => StoreLocationAreaRequest::class,
            'update' => UpdateLocationAreaRequest::class,
        ];
        $this->dtoMap = [
            'store'  => StoreLocationAreaData::class,
            'update' => UpdateLocationAreaData::class,
        ];
    }

    protected function customizeDataTableBuilder($dataTable, Request $request)
    {
        $dataTable
            ->addColumn('location_province_name', function ($row) {
                return $row->province->name ?? '';
            })
            ->addColumn('location_district_name', function ($row) {
                return $row->district->name ?? '';
            })
            ->orderColumn('location_district_name', function ($query, $order) {
                $query->join('location_district', 'location_area.district_code', '=', 'location_district.code')
                    ->orderBy('location_district.name', $order)->select('location_area.*');
            })
            ->orderColumn('location_province_name', function ($query, $order) {
                $query->join('location_province', 'location_area.province_code', '=', 'location_province.code')
                    ->orderBy('location_province.name', $order)->select('location_area.*');
            })
            ->filterColumn('location_province_name', function ($query, $keyword) {
                $query->whereHas('province', function ($q) use ($keyword) {
                    $q->where('name', 'like', "%{$keyword}%");
                });
            })
            ->filterColumn('location_district_name', function ($query, $keyword) {
                $query->whereHas('district', function ($q) use ($keyword) {
                    $q->where('name', 'like', "%{$keyword}%");
                });
            });

        return $dataTable;
    }

    protected function formData(): array
    {
        $locationProvince = LocationProvince::select('code', 'name')->get();

        $selectedDistrict = collect();
        $selectedWard = collect();
        $selectedStreet = collect();

        if ($this->item) {
            $selectedDistrict = LocationDistrict::where('code', $this->item->district_code)->select('code', 'name')->get();
            $selectedWard = LocationWard::where('code', $this->item->ward_code)->select('code', 'name')->get();
            $selectedStreet = LocationStreet::where('code', $this->item->street)->select('code', 'name')->get();
        }

        return [
            'locationProvince' => $locationProvince,
            'selectedDistrict' => $selectedDistrict,
            'selectedWard' => $selectedWard,
            'selectedStreet' => $selectedStreet,
        ];
    }
}
