<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\DTOs\Admin\LoginData;
use App\Http\Requests\Admin\LoginRequest;
use App\Services\Admin\Interfaces\AuthServiceInterface;

class AuthController extends Controller
{
    /**
     * @var AuthService
     */
    protected $authService;

    public function __construct(AuthServiceInterface $authService)
    {
        $this->authService = $authService;
    }

    public function showLogin(): \Illuminate\View\View
    {
        return view('admin.auth.login');
    }

    public function login(LoginRequest $request)
    {
        $validated = $request->validated();

        $dto = new LoginData(
            $validated['username'],
            $validated['password'],
        );

        if ($this->authService->login($dto)) {
            $request->session()->regenerate();
            return redirect()->intended(route('admin.dashboard'));
        }

        return back()
            ->withErrors(['username' => 'Thông tin đăng nhập không đúng'])
            ->onlyInput('username');
    }

    public function logout(Request $request)
    {
        $this->authService->logout();
        $request->session()->invalidate();
        $request->session()->regenerateToken();
        return redirect()->route('admin.login');
    }
}
