<?php

namespace App\Http\Controllers\Admin;

use App\DTOs\Admin\StoreHostData;
use App\DTOs\Admin\UpdateHostData;
use App\Http\Requests\Admin\StoreHostRequest;
use App\Http\Requests\Admin\UpdateHostRequest;
use App\Models\Host;
use App\Services\Admin\Interfaces\HostServiceInterface;
use Illuminate\Http\Request;

class HostController extends BaseController
{
    public function __construct(HostServiceInterface $host)
    {
        parent::__construct();
        $this->model = Host::class;
        $this->service = $host;
        $this->viewPath = 'admin.host';
        $this->routeName = 'admin.host';;
        $this->requestMap = [
            'store'  => StoreHostRequest::class,
            'update' => UpdateHostRequest::class,
        ];
        $this->dtoMap = [
            'store'  => StoreHostData::class,
            'update' => UpdateHostData::class,
        ];
    }

    protected function customizeDataTableBuilder($dataTable, Request $request)
    {
        $this->addStatusToggleColumn($dataTable, 'status');
        $dataTable
            ->editColumn('created_at_formatted', function ($row) {
                return $row->created_at ? $row->created_at->format('d/m/Y') : null;
            })
            ->addColumn('creator_username', function ($row) {
                return $row->creator->username ?? '';
            })
            ->orderColumn('creator_username', function ($query, $order) {
                $query->join('erp_members as c', 'news_articles.creator_id', '=', 'c.id')
                    ->orderBy('c.username', $order)
                    ->select('news_articles.*');
            })
            ->filterColumn('creator_username', function ($query, $keyword) {
                $query->whereHas('creator', function ($q) use ($keyword) {
                    $q->where('username', 'like', "%{$keyword}%");
                });
            });

        return $dataTable;
    }
}
