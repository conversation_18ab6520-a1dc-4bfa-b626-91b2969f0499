<?php

namespace App\Http\Controllers\Admin;

use App\DTOs\Admin\StoreLocationProvinceData;
use App\DTOs\Admin\UpdateLocationProvinceData;
use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\StoreLocationProvinceRequest;
use App\Http\Requests\Admin\UpdateLocationProvinceRequest;
use App\Models\LocationProvince;
use App\Services\Admin\Interfaces\LocationProvinceServiceInterface;
use Illuminate\Http\Request;

class LocationProvinceController extends BaseController
{
      public function __construct(LocationProvinceServiceInterface $locationProvince)
    {
        parent::__construct();
        $this->model = LocationProvince::class;
        $this->service = $locationProvince;
        $this->viewPath = 'admin.location-provinces';
        $this->routeName = 'admin.location-provinces';
        $this->requestMap = [
            'store'  => StoreLocationProvinceRequest::class,
            'update' => UpdateLocationProvinceRequest::class,
        ];
        $this->dtoMap = [
            'store'  => StoreLocationProvinceData::class,
            'update' => UpdateLocationProvinceData::class,
        ];
    }
   
    protected function customizeDataTableBuilder($dataTable, Request $request)
    {
        $this->addStatusToggleColumn($dataTable, 'status');
    
        return $dataTable;
    }
    
}
