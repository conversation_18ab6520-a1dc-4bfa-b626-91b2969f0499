<?php

namespace App\Http\Controllers\Admin;

use App\DTOs\Admin\StoreLocationStreetData;
use App\DTOs\Admin\UpdateLocationStreetData;
use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\StoreLocationStreetRequest;
use App\Http\Requests\Admin\UpdateLocationStreetRequest;
use App\Models\LocationDistrict;
use App\Models\LocationProvince;
use App\Models\LocationStreet;
use App\Models\LocationWard;
use App\Services\Admin\Interfaces\LocationStreetServiceInterface;
use Illuminate\Http\Request;

class LocationStreetsController extends BaseController
{
    public function __construct(LocationStreetServiceInterface $locationstreet)
    {
        parent::__construct();
        $this->model = LocationStreet::class;
        $this->service = $locationstreet;
        $this->viewPath = 'admin.location-streets';
        $this->routeName = 'admin.location-streets';
        $this->requestMap = [
            'store'  => StoreLocationStreetRequest::class,
            'update' => UpdateLocationStreetRequest::class,
        ];
        $this->dtoMap = [
            'store'  => StoreLocationStreetData::class,
            'update' => UpdateLocationStreetData::class,
        ];
    }

    protected function formData(): array
    {
        $locationProvince = LocationProvince::select('code', 'name')->get();

        $selectedDistrict = collect();
        $selectedWard = collect();

        if ($this->item) {
            $selectedDistrict = LocationDistrict::where('code', $this->item->district_code)->select('code', 'name')->get();
            $selectedWard = LocationWard::where('code', $this->item->ward_code)->select('code', 'name')->get();
        }

        return [
            'locationProvince' => $locationProvince,
            'selectedDistrict' => $selectedDistrict,
            'selectedWard' => $selectedWard,
        ];
    }

    protected function customizeDataTableBuilder($dataTable, Request $request)
    {
        $dataTable
            ->addColumn('location_province_name', function ($row) {
                return $row->province->name ?? '';
            })
            ->addColumn('location_district_name', function ($row) {
                return $row->district->name ?? '';
            })
            ->orderColumn('location_province_name', function ($query, $order) {
                $query->join('location_province', 'location_street.province_code', '=', 'location_province.code')
                    ->orderBy('location_province.name', $order)->select('location_street.*');;
            })
            ->orderColumn('location_district_name', function ($query, $order) {
                $query->join('location_district', 'location_street.district_code', '=', 'location_district.code')
                    ->orderBy('location_district.name', $order)->select('location_street.*');
            })
            ->filterColumn('location_province_name', function ($query, $keyword) {
                $query->whereHas('province', function ($q) use ($keyword) {
                    $q->where('name', 'like', "%{$keyword}%");
                });
            })
            ->filterColumn('location_district_name', function ($query, $keyword) {
                $query->whereHas('district', function ($q) use ($keyword) {
                    $q->where('name', 'like', "%{$keyword}%");
                });
            });

        return $dataTable;
    }
}
