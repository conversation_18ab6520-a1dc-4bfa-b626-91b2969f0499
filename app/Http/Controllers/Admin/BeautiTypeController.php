<?php

namespace App\Http\Controllers\Admin;

use App\DTOs\Admin\StoreBeautiTypeData;
use App\DTOs\Admin\UpdateBeautiTypeData;
use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\StoreBeautiTypeRequest;
use App\Http\Requests\Admin\UpdateBeautiTypeRequest;
use App\Models\BeautiType;
use App\Services\Admin\Interfaces\BeautiTypeServiceInterface;
use Illuminate\Http\Request;

class BeautiTypeController extends BaseController
{
    public function __construct(BeautiTypeServiceInterface $beautiType)
    {
        parent::__construct();
        $this->model = BeautiType::class;
        $this->service = $beautiType;
        $this->viewPath = 'admin.beauti-types';
        $this->routeName = 'admin.beauti-types';;
        $this->requestMap = [
            'store'  => StoreBeautiTypeRequest::class,
            'update' => UpdateBeautiTypeRequest::class,
        ];
        $this->dtoMap = [
            'store'  => StoreBeautiTypeData::class,
            'update' => UpdateBeautiTypeData::class,
        ];
    }

    protected function customizeDataTableBuilder($dataTable, Request $request)
    {
        $this->addStatusToggleColumn($dataTable, 'status');
        $dataTable->editColumn('created_at_formatted', function ($row) {
            return $row->created_at ? $row->created_at->format('d/m/Y') : null;
        })
            ->orderColumn('creator_username', function ($query, $order) {
                $query->join('erp_members as c', 'beauti_types.creator_id', '=', 'c.id')
                    ->orderBy('c.username', $order)
                    ->select('beauti_types.*');
            })
            ->filterColumn('creator_username', function ($query, $keyword) {
                $query->whereHas('creator', function ($q) use ($keyword) {
                    $q->where('username', 'like', "%{$keyword}%");
                });
            });
        return $dataTable;
    }
}
