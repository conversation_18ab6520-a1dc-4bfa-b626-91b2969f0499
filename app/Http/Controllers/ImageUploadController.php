<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Http\Requests\UploadImageRequest;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class ImageUploadController extends Controller
{

    public function uploadImage(UploadImageRequest $request)
    {
        try {
            $file = $request->file('file');

            $name = $this->_getNameFile() . '.' . $file->getClientOriginalExtension();

            $subFolder = now()->format('Y/m/d');
            $uploadPath = public_path("uploads/images/{$subFolder}");

            \Illuminate\Support\Facades\File::ensureDirectoryExists($uploadPath);

            $file->move($uploadPath, $name);

            $url = asset("uploads/images/{$subFolder}/{$name}");

            return response()->json([
                'location' => $url
            ]);
        } catch (\Throwable $th) {
            report($th);
            return response()->json([
                'error' => [
                    'message' => 'Upload thất bại. Vui lòng thử lại.'
                ]
            ], 500);
        }
    }



    private function _getNameFile()
    {
        $md5 = strtoupper(md5(uniqid() . microtime()));
        return substr($md5, 0, 8) . '-' . substr($md5, 8, 4) . '-' . substr($md5, 12, 4) . '-' . substr($md5, 16, 4) . '-' . substr($md5, 20);
    }
}
