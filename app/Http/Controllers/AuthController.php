<?php

namespace App\Http\Controllers;

use App\DTOs\LoginData;
use App\DTOs\RegisterData;
use App\Http\Requests\LoginRequest;
use App\Http\Requests\RegisterRequest;
use App\Http\Requests\ResendVerificationRequest;
use App\Http\Requests\VerifyPhoneRequest;
use App\Services\Interfaces\AuthServiceInterface;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Validation\ValidationException;

class AuthController extends Controller
{
  protected $authService;

  public function __construct(AuthServiceInterface $authService)
  {
    $this->authService = $authService;
  }


  public function register(RegisterRequest $request): JsonResponse
  {
    $data = $request->validated();

    $dto = new RegisterData(
      $data['fullname'],
      $data['identifier'],
      $data['password'],
      $data['password_confirmation']
    );

    try {
      $user = $this->authService->register($dto);

      return response()->json([
        'success' => true,
        'message' => 'Đăng ký thành công, vui lòng kiểm tra ' . ($data['email'] ? 'email' : 'tin nhắn') . ' để xác thực',
        'data'    => [
          'id'       => $user->id,
          'fullname' => $user->fullname,
          'email'    => $user->email,
          'phone'    => $user->phone,
        ],
      ], 201);
    } catch (ValidationException $e) {
      return response()->json([
        'success' => false,
        'message' => 'Thông tin đăng ký không hợp lệ.',
        'errors'  => $e->errors(),
      ], 422);
    } catch (\Exception $e) {
      return response()->json([
        'success' => false,
        'message' => "Đã xảy ra lỗi hệ thống: " . $e->getMessage(),
      ], 500);
    }
  }

  public function login(LoginRequest $request): JsonResponse
  {
    $data = $request->validated();
    $dto = new LoginData(
      $data['identifier'],
      $data['password']
    );

    try {
      $result = $this->authService->login($dto);

      if ($result['status'] === 'pending_verification') {
        return response()->json([
          'status'      => 'pending_verification',
          'message'     => $result['message'],
          'identifier'  => $data['identifier'],
          'method'      => $result['method'],
        ], 200);
      }
      return response()->json([
        'status'      => 'success',
        'message'     => 'Đăng nhập thành công!',
        'redirect'    => route('home'),
      ], 200);
    } catch (ValidationException $e) {
      return response()->json([
        'success' => false,
        'errors'  => $e->errors(),
      ], 422);
    } catch (\Exception $e) {
      return response()->json([
        'success' => false,
        'message' => "Đã xảy ra lỗi" . $e->getMessage(),
      ], 500);
    }
  }


  public function logout(Request $request)
  {
    $this->authService->logout();
    $request->session()->invalidate();
    $request->session()->regenerateToken();
    return redirect()->route('home');
  }

  public function verifyEmail(Request $request)
  {
    $otp = $request->query('otp');

    if (!$otp) {
      return redirect()->route('home')
        ->with('toast_error', 'Mã OTP xác thực không được cung cấp.');
    }

    if ($this->authService->verifyEmail($otp)) {
      return redirect()->route('home')
        ->with('toast_success', 'Email của bạn đã được xác thực thành công!');
    }

    return redirect()->route('home')
      ->with('toast_error', 'Mã OTP không hợp lệ hoặc đã hết hạn.');
  }


  public function resendVerification(ResendVerificationRequest $request): JsonResponse
  {
    try {
      $response = $this->authService->resendVerificationCode(
        $request->identifier()
      );
      return match ($response['status']) {
        'already_verified' => response()->json([
          'message' => $response['message'] ?? 'tài khoản này đã được xác thực',
        ], 200),

        default => response()->json([
          'message' => $response['message'] ?? null,
          'method'  => $response['method'] ?? null,
          'value'   => $response['value'] ?? null,
        ], 200),
      };
    } catch (Throwable $exception) {
      Log::error('Resend verification failed', [
        'identifier' => $request->identifier(),
        'error'      => $exception->getMessage(),
        'trace'      => $exception->getTraceAsString(),
      ]);

      return response()->json([
        'message' => 'Không thể gửi lại mã xác thực. Vui lòng thử lại sau.',
      ], 500);
    }
  }


  public function verifyPhone(VerifyPhoneRequest $request)
  {
    try {
      $phone = $request->phone();
      $otp   = $request->otp();

      if ($this->authService->verifyPhone($phone, $otp)) {
        return response()->json([
          'message' => 'Số điện thoại của bạn đã được xác thực thành công!',
        ], 200);
      }

      return response()->json([
        'message' => 'Mã OTP không hợp lệ hoặc đã hết hạn.',
        'errors'  => ['otp' => ['OTP không hợp lệ hoặc đã hết hạn.']],
      ], 422);
    } catch (Throwable $exception) {
      Log::error('Phone verification failed', [
        'phone' => $request->phone(),
        'error' => $exception->getMessage(),
      ]);

      return response()->json([
        'message' => 'Đã có lỗi xảy ra. Vui lòng thử lại sau.',
      ], 500);
    }
  }
}
