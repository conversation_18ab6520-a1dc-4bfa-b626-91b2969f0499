<?php

namespace App\Http\Controllers;

use App\Models\BeautiStore;
use App\Models\BeautiStorePackage;
use App\Models\BeautiType;
use App\Models\LocationProvince;
use App\Services\Interfaces\BeautiStoreServiceInterface;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class BeautiStoreController extends Controller
{
    protected $storeService;

    public function __construct(BeautiStoreServiceInterface $storeService)
    {
        $this->storeService = $storeService;
    }
    public function showStores(string $typeSlug, string $provinceCode)
    {
        $stores = $this->storeService->getStoresByTypeAndProvince($typeSlug, $provinceCode);

        $type = BeautiType::where('slug', $typeSlug)->firstOrFail();

        $provinceName = LocationProvince::where('code', $provinceCode)
            ->value('name')
            ?? $provinceCode;

        return view('beauti-stores.index', [
            'type'         => $type,
            'provinceCode' => $provinceCode,
            'provinceName' => $provinceName,
            'stores'       => $stores,
        ]);
    }

    public function showServices(string $slug)
    {
        $store = BeautiStore::where('slug', $slug)
            ->where('status', 1)
            ->firstOrFail();

        $storeServices = $this->storeService->getStoreServices($store->id);
        $services = $storeServices->pluck('service');
        $storesRelated = $this->storeService->getStoresByTypeAndProvince(
            optional($store->beauties->types->first())->slug,
            $store->province_code
        )->where('id', '!=', $store->id);

        $provinceName = LocationProvince::where('code', $store->province_code)
            ->value('name')
            ?? $store->province_code;


        $storeServiceIds = $storeServices->pluck('id');
        $packages = BeautiStorePackage::whereIn('store_service_id', $storeServiceIds)
            ->where('status', 1)
            ->with(['storeService.service']) 
            ->latest()
            ->get();
        // dd(json_encode($packages));
        return view('beauti-services.index', compact('store', 'services', 'storesRelated', 'provinceName', 'packages'));
    }
}
