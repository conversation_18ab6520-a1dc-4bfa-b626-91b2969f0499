<?php

namespace App\Http\Controllers;

use App\Services\Interfaces\NewsArticleServiceInterface;
use Illuminate\Http\Request;

class NewsArticleController extends Controller
{
    protected $newsService;

    public function __construct(NewsArticleServiceInterface $newsService)
    {
        $this->newsService = $newsService;
    }

    public function index()
    {
        $articles = $this->newsService->listPublishedArticles();

        return view('news-articles.index', compact('articles'));
    }
}
