<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Models\LocationDistrict;
use App\Models\LocationStreet;
use App\Models\LocationWard;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class LocationLookupController extends Controller
{
    public function getDistricts($provinceCode)
    {
        return response()->json(
            LocationDistrict::where('province_code', $provinceCode)
                ->select('code', 'name')
                ->orderBy('name')->get()
        );
    }

    public function getWards($districtCode)
    {
        return response()->json(
            LocationWard::where('district_code', $districtCode)
                ->select('code', 'name')
                ->orderBy('name')->get()
        );
    }

    public function getStreets($provinceCode, $districtCode, $wardCode)
    {
        return response()->json(
            LocationStreet::where('province_code', $provinceCode)
                ->where('district_code', $districtCode)
                ->where('ward_code', $wardCode)
                ->select('code', 'name')
                ->orderBy('name')->get()
        );
    }

    public function getFullLocation($province_code)
    {
        $districts = LocationDistrict::with([
            'wards' => function ($query) {
                $query->select('name', 'code', 'district_code');
            },
            'streets' => function ($query) {
                $query->select('name', 'code', 'district_code', 'province_code');
            }
        ])
            ->where('province_code', $province_code)
            ->select('name', 'code', 'province_code')
            ->get();

        $wards = $districts->flatMap->wards;

        $streets = $districts->flatMap->streets;

        return response()->json([
            'districts' => $districts,
            'wards' => $wards,
            'streets' => $streets,
        ]);
    }

    public function getWardsAndStreets($provinceCode, $districtCode)
    {
        $district = LocationDistrict::with(['wards', 'streets'])
            ->where('province_code', $provinceCode)
            ->where('code', $districtCode)
            ->first();

        if (!$district) {
            return response()->json([
                'wards' => collect(),
                'streets' => collect(),
            ]);
        }

        return response()->json([
            'wards' => $district->wards,
            'streets' => $district->streets,
        ]);
    }
}
