<?php

namespace App\Traits\DataTables;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\Request;
use Ya<PERSON>ra\DataTables\EloquentDataTable;
use Illuminate\Support\Facades\Auth;

trait HasStatusToggle
{
    protected function addStatusToggleColumn(EloquentDataTable $dataTable, string $columnName = 'status'): EloquentDataTable
    {
        $dataTable->editColumn($columnName, function ($row) use ($columnName) {
            $statusValue = (bool) $row->{$columnName};
            $statusIcon = $statusValue ? 'fa-toggle-on text-success' : 'fa-toggle-off text-danger';
            $toggleUrl = route(
                $this->getToggleStatusRouteName(),
                ['id' => $row->id, 'field' => $columnName]
            );
            return '<a href="javascript:void(0);" class="toggle-status-btn"
                        data-id="' . $row->id . '"
                        data-field="' . $columnName . '"
                        data-status="' . (int)$statusValue . '"
                        data-toggle-url="' . $toggleUrl . '">
                        <i class="fa ' . $statusIcon . ' fa-2x"></i>
                    </a>';
        });

        if (method_exists($this, 'addRawColumn')) {
            $this->addRawColumn($columnName);
        };
        return $dataTable;
    }

    protected function getToggleStatusRouteName(): string
    {
        $routeNamePrefix = strtolower(str_replace('Controller', '', class_basename($this)));
        return "admin.{$routeNamePrefix}.toggle-status";
    }

    public function toggleStatus(Request $request, $id)
    {
        $field = $request->input('field', 'status');
        $modelClass = $this->model;
        $model = $modelClass::find($id);
        if (!$model) {
            return response()->json(['success' => false, 'message' => 'Bản ghi không tồn tại.'], 404);
        }
        if (!isset($model->{$field})) {
            return response()->json(['success' => false, 'message' => "Trường '{$field}' không tồn tại hoặc không thể truy cập trong Model."], 400);
        }
        $model->{$field} = !$model->{$field};
        $saved = $model->save();
        if (!$saved) {
            return response()->json([
                'success' => false,
                'message' => 'Không thể lưu trạng thái.'
            ], 500);
        }

        return response()->json(['success' => true, 'message' => 'Cập nhật trạng thái thành công.']);
    }
}
