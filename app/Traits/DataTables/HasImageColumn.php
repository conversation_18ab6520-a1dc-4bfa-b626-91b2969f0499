<?php

namespace App\Traits\DataTables;

use Ya<PERSON>ra\DataTables\EloquentDataTable;

trait HasImageColumn
{
    protected function addImageColumn(EloquentDataTable $dataTable, string $columnName = 'image', int $width = 50): EloquentDataTable
    {
        $dataTable->editColumn($columnName, function ($row) use ($columnName,  $width) {
            $imagePath = $row->{$columnName};
            if (!$imagePath) {
                return '';
            }
            $url = asset(ltrim($imagePath, '/'));
            return "<img src='{$url}' alt='image' style='max-width:{$width}px; max-height:{$width}px;' />";
        });

        if (method_exists($this, 'addRawColumn')) {
            $this->addRawColumn($columnName);
        }

        return $dataTable;
    }
}
