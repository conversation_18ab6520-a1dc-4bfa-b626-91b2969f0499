<?php
namespace App\Traits\Admin;

use Illuminate\Support\Facades\Auth;

trait HasBlameable
{
    public static function bootHasBlameable()
    {
        static::creating(function ($model) {
            if (Auth::guard('admin')->check()) {
                $model->creator_id = Auth::guard('admin')->id();
            }
        });

        static::updating(function ($model) {
            if (Auth::guard('admin')->check()) {
                $model->editor_id = Auth::guard('admin')->id();
            }
        });
    }
}
