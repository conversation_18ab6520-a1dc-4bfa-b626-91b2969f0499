<?php

namespace App\Models;

use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Spatie\Permission\Traits\HasRoles;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Hash;

class ErpMember extends Authenticatable
{
    use Notifiable, HasRoles;

    protected $table = 'erp_members';
    protected $guard_name = 'admin';

    protected $fillable = [
        'fullname',
        'firstname',
        'lastname',
        'username',
        'password',
        'salt',
        'department_id',
        'email',
        'phone',
        'status',
        'gender',
        'code',
        'is_leader',
        'code_position',
        'title',
        'staff_position',
        'telegram_id',
        'avatar',
        'email',
        'phone',
        'gender',
        'content',
        'color',
        'status',
        'visited',
        'creator_id',
        'editor_id'
    ];

    protected $hidden = [
        'password',
        'salt',
    ];

    protected $casts = [
        'status' => 'boolean',
        'visited' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    public function department()
    {
        return $this->belongsTo(ErpDepartment::class, 'department_id');
    }

    protected static function booted()
    {
        static::creating(function ($member) {
            $member->fullname = trim(($member->lastname ?? '') . ' ' . ($member->firstname ?? ''));
            $salt = Str::random(16);
            $member->salt = $salt;
            $member->password = Hash::make($member->username . $salt);
        });

        static::updating(function ($member) {
            $member->fullname = trim(($member->lastname ?? '') . ' ' . ($member->firstname ?? ''));
        });
    }
}
