<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class LocationDistrict extends Model
{
    // location_district
    use HasFactory;
    protected $table = 'location_district';

    protected $fillable = [
        'name',
        'code',
        'code_district',
        'type',
        'latitude',
        'province_code',
    ];
    public $timestamps = false;
    protected $casts = [
        'latitude' => 'float',
    ];

    public function province(): BelongsTo
    {
        return $this->belongsTo(LocationProvince::class, 'province_code', 'code');
    }

       public function wards()
    {
        return $this->hasMany(LocationWard::class, 'district_code', 'code');
    }

    public function streets()
    {
        return $this->hasMany(LocationStreet::class, 'district_code', 'code');
    }
}
