<?php

namespace App\Models;

use App\Traits\Admin\HasBlameable;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Host extends Model
{
    use HasFactory, HasBlameable;

    protected $table = 'host';

    protected $fillable = [
        'user_id',
        'name',
        'alias_name',
        'email',
        'phone',
        'zalo',
        'address',
        'status',
        'in_charge_id',
        'description',
        'creator_id',
        'editor_id',
    ];

     public function inCharge()
    {
        return $this->belongsTo(ErpMember::class, 'in_charge_id');
    }

      public function creator()
    {
        return $this->belongsTo(ErpMember::class, 'creator_id');
    }

      public function editor()
    {
        return $this->belongsTo(ErpMember::class, 'editor_id');
    }
}
