<?php

namespace App\Models;

use App\Traits\Admin\HasBlameable;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class ErpDepartment extends Model
{
    use HasFactory, HasBlameable;

    protected $table = 'erp_departments';

    protected $fillable = [
        'name',
        'code',
        'manager_id',
        'content',
        'color',
        'telegram',
        'status',
        'creator_id',
        'editor_id',
        'position',
    ];

    protected $casts = [
        'status' => 'boolean',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    public function manager()
    {
        return $this->belongsTo(ErpMember::class, 'manager_id');
    }

    public function creator()
    {
        return $this->belongsTo(ErpMember::class, 'creator_id');
    }

    public function editor()
    {
        return $this->belongsTo(ErpMember::class, 'editor_id');
    }

    public function members()
    {
        return $this->hasMany(ErpMember::class, 'department_id');
    }
}
