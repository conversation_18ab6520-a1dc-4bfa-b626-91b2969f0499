<?php

namespace App\Models;

use App\Traits\Admin\HasBlameable;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class BeautiStorePackage extends Model
{
    use HasFactory, HasBlameable;

    protected $fillable = [
        'store_service_id',
        'name',
        'slug',
        'duration_days',
        'warranty_days',
        'price_old',
        'price_new',
        'description',
        'imgs',
        'status',
        'seo_title',
        'seo_description',
        'seo_keywords',
        'seo_image',
        'creator_id',
        'editor_id',
    ];

        protected $casts = [
        'status' => 'boolean',
        'imgs' => 'array',
    ];

    public function storeService()
    {
        return $this->belongsTo(BeautiStoreService::class, 'store_service_id');
    }

    public function creator()
    {
        return $this->belongsTo(ErpMember::class, 'creator_id', 'id')
            ->withDefault();
    }


    public function store()
    {
        return $this->hasOneThrough(
            BeautiStore::class,
            BeautiStoreService::class,
            'id',
            'id',
            'store_service_id',
            'store_id'
        );
    }

    public function service()
    {
        return $this->hasOneThrough(
            BeautiService::class,
            BeautiStoreService::class,
            'id',
            'id',
            'store_service_id',
            'service_id'
        );
    }


    public function scopeActive($query)
    {
        return $query->where('status', 1);
    }
}
