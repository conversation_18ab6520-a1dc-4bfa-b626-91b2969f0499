<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;

use App\Traits\Admin\HasBlameable;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Illuminate\Support\Str;
use Tymon\JWTAuth\Contracts\JWTSubject;

class User extends Authenticatable implements JWTSubject
{
  /** @use HasFactory<\Database\Factories\UserFactory> */
  use HasFactory, Notifiable, HasBlameable;

  /**
   * The attributes that are mass assignable.
   *
   * @var list<string>
   */
  protected $fillable = [
    'fullname',
    'supporter_id',
    'date_of_birth',
    'email',
    'password',
    'phone',
    'salt',
    'address',
    'status',
    'gender',
    'avatar'
  ];

  /**
   * The attributes that should be hidden for serialization.
   *
   * @var list<string>
   */
  protected $hidden = [
    'password',
    'salt',
  ];

  /**
   * Get the attributes that should be cast.
   *
   * @return array<string, string>
   */
  protected $casts = [
    'status'     => 'boolean',
    'created_at' => 'datetime',
    'updated_at' => 'datetime',
  ];

  public function supporter()
  {
    return $this->belongsTo(ErpMember::class, 'supporter_id');
  }

  public function creator()
  {
    return $this->belongsTo(ErpMember::class, 'creator_id');
  }

  public function editor()
  {
    return $this->belongsTo(ErpMember::class, 'editor_id');
  }

  public function setPasswordAttribute($value)
  {
    $this->attributes['password'] = $value;
  }

  public static function generateSalt(int $length = 16): string
  {
    return Str::random($length);
  }

  /**
   * Get the identifier that will be stored in the subject claim of the JWT.
   *
   * @return mixed
   */
  public function getJWTIdentifier()
  {
    return $this->getKey();
  }

  /**
   * Return a key value array, containing any custom claims to be added to the JWT.
   *
   * @return array
   */
  public function getJWTCustomClaims()
  {
    return [
      'fullname' => $this->fullname,
      'email'    => $this->email,
      'phone'    => $this->phone,
    ];
  }

  /**
   * Get all tokens for the user.
   */
  public function tokens(): HasMany
  {
    return $this->hasMany(UserToken::class);
  }

  /**
   * Get refresh tokens for the user.
   */
  public function refreshTokens(): HasMany
  {
    return $this->tokens()->where('token_type', 'refresh_token');
  }

  /**
   * Get all verifications for the user.
   */
  public function verifications(): HasMany
  {
    return $this->hasMany(UserVerification::class);
  }

  /**
   * Get email verification for the user.
   */
  public function emailVerification(): HasMany
  {
    return $this->verifications()
      ->where('type', UserVerification::TYPE_EMAIL)
      ->where('verified_at', '<', now());
  }

  /**
   * Get phone verification for the user.
   */
  public function phoneVerification(): HasMany
  {
    return $this->verifications()
      ->where('type', UserVerification::TYPE_PHONE)
      ->where('verified_at', '<', now());
  }

  /**
   * Check if email is verified
   */
  public function hasVerifiedEmail(): bool
  {
    return $this->emailVerification()->verified()->exists();
  }

  /**
   * Check if phone is verified
   */
  public function hasVerifiedPhone(): bool
  {
    return $this->phoneVerification()->verified()->exists();
  }

  /**
   * Get the avatar URL attribute.
   */
  public function getAvatarUrlAttribute(): ?string
  {
    return \App\Helpers\ImageHelper::getUserAvatarUrl($this->avatar);
  }
}
