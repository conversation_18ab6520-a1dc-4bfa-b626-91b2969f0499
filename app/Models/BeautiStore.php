<?php

namespace App\Models;

use App\Traits\Admin\HasBlameable;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class BeautiStore extends Model
{
    use HasFactory, HasBlameable;

    protected $fillable = [
        'name',
        'beauti_id',
        'slug',
        'logo',
        'image',
        'street',
        'ward_code',
        'district_code',
        'province_code',
        'full_address',
        'description',
        'price_from',
        'status',
        'opening_time',
        'closing_time',
        'position',
        'seo_title',
        'seo_description',
        'seo_keywords',
        'seo_image',
        'creator_id',
        'editor_id',
    ];

    protected $casts = [
        'status' => 'boolean',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'price_from' => 'integer',
    ];

    public function beauties()
    {
        return $this->belongsTo(Beauti::class, 'beauti_id', 'id')
            ->withDefault();
    }

    public function types()
    {
        return $this->belongsToMany(
            BeautiType::class,
            'beauti_store_type',
            'store_id',
            'type_id'
        )
            ->withTimestamps();
    }

    public function creator()
    {
        return $this->belongsTo(ErpMember::class, 'creator_id', 'id')
            ->withDefault();
    }

    public function editor()
    {
        return $this->belongsTo(ErpMember::class, 'editor_id', 'id')
            ->withDefault();
    }

    public function storeServices()
    {
        return $this->hasMany(BeautiStoreService::class, 'store_id', 'id');
    }

    public function services()
    {
        return $this->belongsToMany(
            BeautiService::class,
            'beauti_store_services',
            'store_id',
            'service_id'
        )->withPivot(['price_from', 'status']);
    }

    public function packages()
    {
        return $this->hasManyThrough(
            BeautiServicePackage::class,
            BeautiStoreService::class,
            'store_id',
            'store_service_id',
            'id',
            'id'
        );
    }

    public function province()
    {
        return $this->belongsTo(
            LocationProvince::class,
            'province_code',
            'code'
        );
    }

    public function scopeActive($query)
    {
        return $query->where('status', 1);
    }
}
