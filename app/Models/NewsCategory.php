<?php

namespace App\Models;

use App\Traits\Admin\HasBlameable;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;

class NewsCategory extends Model
{
    use HasFactory, HasBlameable;

    protected $table = 'news_categories';

    protected $fillable = [
        'parents',
        'title',
        'code',
        'image',
        'description',
        'seo_title',
        'seo_description',
        'seo_keywords',
        'seo_image',
        'status',
        'creator_id',
        'editor_id',
        'position',
    ];

    protected $casts = [
        'parents' => 'integer',
        'status' => 'boolean',
    ];

    public function articles(): BelongsToMany
    {
        return $this->belongsToMany(
            NewsArticle::class,
            'news_article_category',
            'category_id',
            'news_id'
        );
    }

    public function parent(): BelongsTo
    {
        return $this->belongsTo(self::class, 'parents');
    }

    public function children(): HasMany
    {
        return $this->hasMany(self::class, 'parents');
    }

    public function creator(): BelongsTo
    {
        return $this->belongsTo(ErpMember::class, 'creator_id');
    }


    public function editor(): BelongsTo
    {
        return $this->belongsTo(ErpMember::class, 'editor_id');
    }


     public static function getFormattedForSelect($parentId = null, $prefix = '')
    {
        $allCategories = self::orderBy('id')->get(); 

        $formattedList = self::buildCategoryTree($allCategories, $parentId, $prefix);

        return collect($formattedList); 
    }

     private static function buildCategoryTree($categories, $parentId = null, $prefix = '')
    {
        $branch = [];

        foreach ($categories as $category) {
            if (($parentId === null && ($category->parents === null || $category->parents === 0)) || $category->parents == $parentId) {
                $formattedCategory = clone $category;
                $formattedCategory->title_prefixed = $prefix . $category->title;
                $branch[] = $formattedCategory;

                $children = self::buildCategoryTree($categories, $category->id, $prefix . '-');
                foreach ($children as $child) {
                    $branch[] = $child;
                }
            }
        }
        return $branch;
    }
    
}
