<?php

namespace App\Models;

use App\Traits\Admin\HasBlameable;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class SystemConfig extends Model
{
    use HasFactory, HasBlameable;
    protected $table = 'system_config';

    protected $fillable = [
        'name',
        'code',
        'title',
        'description',
        'data',
        'image',
        'link',
        'url_target',
        'status',
        'creator_id',
        'editor_id'
    ];

    protected $casts = [
        'status' => 'boolean',
        'creator_id' => 'integer',
        'editor_id' => 'integer',
    ];
    public $timestamps = true;

    public function creator()
    {
        return $this->belongsTo(ErpMember::class, 'creator_id');
    }

    public function editor()
    {
        return $this->belongsTo(ErpMember::class, 'editor_id');
    }
}
