<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class LocationArea extends Model
{
    use HasFactory;

    protected $table = 'location_area';
    protected $fillable = [
        'name',
        'code',
        'latitude',
        'address',
        'image',
        'street',
        'longitude',
        'coordinates',
        'ward_code',
        'district_code',
        'province_code',
        'full_address'
    ];
    public $timestamps = false;
    protected $casts = [
        'latitude' => 'float',
        'longitude' => 'float',
        'coordinates' => 'array',
    ];

    public function province()
    {
        return $this->belongsTo(LocationProvince::class, 'province_code', 'code');
    }
    public function district()
    {
        return $this->belongsTo(LocationDistrict::class, 'district_code', 'code');
    }
    public function ward()
    {
        return $this->belongsTo(LocationWard::class, 'ward_code', 'code');
    }
    public function street()
    {
        return $this->belongsTo(LocationStreet::class, 'street', 'code');
    }
}
