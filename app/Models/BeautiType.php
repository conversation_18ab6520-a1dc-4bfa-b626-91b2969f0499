<?php

namespace App\Models;

use App\Traits\Admin\HasBlameable;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class BeautiType extends Model
{
    use HasFactory, HasBlameable;

    protected $fillable = [
        'name',
        'slug',
        'image',
        'status',
        'description',
        'featured',
        'position',
        'seo_title',
        'seo_description',
        'seo_keywords',
        'seo_image',
        'creator_id',
        'editor_id',
    ];

    protected $casts = [
        'status' => 'boolean',
        'featured' => 'boolean',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    public function creator()
    {
        return $this->belongsTo(ErpMember::class, 'creator_id', 'id')
            ->withDefault();
    }

    public function editor()
    {
        return $this->belongsTo(ErpMember::class, 'editor_id', 'id')
            ->withDefault();
    }

    public function services()
    {
        return $this->belongsToMany(
            BeautiService::class,
            'beauti_type_service',
            'type_id',
            'service_id'
        )->withTimestamps();
    }

    public function beauties()
    {
        return $this->belongsToMany(Beauti::class, 'beauti_beauti_type', 'type_id', 'beauti_id');
    }

    public function stores()
    {
        return $this->belongsToMany(
            BeautiStore::class,
            'beauti_store_type',
            'type_id',
            'store_id'
        )
            ->withTimestamps();
    }

    public function scopeActive($query)
    {
        return $query->where('status', 1);
    }

     
    public function getRouteKeyName()
    {
        return 'slug';
    }
}
