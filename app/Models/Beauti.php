<?php

namespace App\Models;

use App\Traits\Admin\HasBlameable;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Beauti extends Model
{
    use HasFactory, HasBlameable;
    protected $table = 'beauties';
    protected $fillable = [
        'name',
        'host_id',
        'address',
        'phone',
        'fanpage',
        'tiktok',
        'website',
        'email',
        'description',
        'status',
        'creator_id',
        'editor_id',
    ];

    protected $casts = [
        'status' => 'boolean',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];


    public function host()
    {
        return $this->belongsTo(Host::class, 'host_id', 'id')
            ->withDefault();
    }


    public function creator()
    {
        return $this->belongsTo(ErpMember::class, 'creator_id', 'id')
            ->withDefault();
    }

    public function editor()
    {
        return $this->belongsTo(ErpMember::class, 'editor_id', 'id')
            ->withDefault();
    }

    public function types()
    {
        return $this->belongsToMany(
            BeautiType::class,
            'beauti_beauti_type',
            'beauti_id',
            'type_id'
        );
    }

    public function stores(): HasMany
    {
        return $this->hasMany(BeautiStore::class, 'beauti_id');
    }
}
