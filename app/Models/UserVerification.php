<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Carbon\Carbon;

class UserVerification extends Model
{
  protected $fillable = [
    'user_id',
    'type',
    'token',
    'expires_at',
    'verified_at',
  ];

  protected $casts = [
    'expires_at'  => 'datetime',
    'verified_at' => 'datetime',
    'created_at'  => 'datetime',
    'updated_at'  => 'datetime',
  ];

  // Constants cho verification types
  const TYPE_EMAIL = 'email';
  const TYPE_PHONE = 'phone';

  /**
   * Get the user that owns the verification.
   */
  public function user(): BelongsTo
  {
    return $this->belongsTo(User::class);
  }

  /**
   * Check if verification is expired
   */
  public function isExpired(): bool
  {
    return $this->expires_at && $this->expires_at->isPast();
  }

  /**
   * Check if verification is valid (not expired and not verified yet)
   */
  public function isValid(): bool
  {
    return !$this->isExpired() && !$this->isVerified();
  }

  /**
   * Check if verification is already verified
   */
  public function isVerified(): bool
  {
    return !is_null($this->verified_at);
  }

  /**
   * Mark verification as verified
   */
  public function markAsVerified(): void
  {
    $this->verified_at = now();
    $this->expires_at  = null;
    $this->token       = null;
    $this->save();
  }

  /**
   * Scope for email verifications
   */
  public function scopeEmail($query)
  {
    return $query->where('type', self::TYPE_EMAIL);
  }

  /**
   * Scope for phone verifications
   */
  public function scopePhone($query)
  {
    return $query->where('type', self::TYPE_PHONE);
  }

  /**
   * Scope for unverified
   */
  public function scopeUnverified($query)
  {
    return $query->whereNull('verified_at');
  }

  /**
   * Scope for verified
   */
  public function scopeVerified($query)
  {
    return $query->whereNotNull('verified_at');
  }

  /**
   * Scope for valid (not expired and not verified)
   */
  public function scopeValid($query)
  {
    return $query->unverified()
      ->where(function ($q) {
        $q->whereNull('expires_at')
          ->orWhere('expires_at', '>', now());
      });
  }

  /**
   * Generate a 6-digit OTP
   */
  public static function generateOTP(): string
  {
    return str_pad((string) random_int(100000, 999999), 6, '0', STR_PAD_LEFT);
  }

  /**
   * Generate a secure 4-digit OTP (for high security)
   */
  public static function generateShortOTP(): string
  {
    return str_pad((string) random_int(1000, 9999), 4, '0', STR_PAD_LEFT);
  }

  /**
   * Generate a 8-digit OTP (for extra security)
   */
  public static function generateLongOTP(): string
  {
    return str_pad((string) random_int(10000000, 99999999), 8, '0', STR_PAD_LEFT);
  }

  /**
   * Check if token is a valid OTP format (6 digits)
   */
  public function isValidOTPFormat(): bool
  {
    return preg_match('/^\d{6}$/', $this->token);
  }
}
