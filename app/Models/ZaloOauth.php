<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class ZaloOauth extends Model
{
  protected $table = 'zalo_oauth';

  protected $fillable = [
    'access_token',
    'refresh_token',
    'expired_access_token',
    'expired_refresh_token',
    'created',
  ];

  protected $casts = [
    'expired_access_token'  => 'datetime',
    'expired_refresh_token' => 'datetime',
    'created'               => 'datetime',
  ];

  public $timestamps = false;
}
