<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class LocationWard extends Model
{
    use HasFactory;
    protected $table = 'location_ward';

    protected $fillable = [
        'name',
        'code',
        'type',
        'latitude',
        'longitude',
        'coordinates',
        'district_code',
    ];

    public $timestamps = false;

    protected $casts = [
        'latitude' => 'float',
        'longitude' => 'float',
        'coordinates' => 'array',
    ];

    public function district()
    {
        return $this->belongsTo(LocationDistrict::class, 'district_code', 'code');
    }

      public function streets()
    {
        return $this->hasMany(LocationStreet::class, 'ward_code', 'code');
    }
}
