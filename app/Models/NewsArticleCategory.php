<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class NewsArticleCategory extends Model
{
    use HasFactory;

    protected $table = 'news_article_category';

    protected $fillable = [
        'news_id',
        'category_id',
    ];

     public function article(): BelongsTo
    {
        return $this->belongsTo(NewsArticle::class, 'news_id');
    }

    
    public function category(): BelongsTo
    {
        return $this->belongsTo(NewsCategory::class, 'category_id');
    }
}
