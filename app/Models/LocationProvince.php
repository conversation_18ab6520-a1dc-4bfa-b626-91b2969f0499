<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class LocationProvince extends Model
{
  use HasFactory;

  protected $table = 'location_province';

  protected $fillable = [
    'name',
    'code',
    'code_province',
    'type',
    'latitude',
    'longitude',
    'status',
  ];
  public $timestamps = false;
  protected $casts = [
    'latitude' => 'float',
    'longitude' => 'float',
    'status' => 'boolean',
  ];
}
