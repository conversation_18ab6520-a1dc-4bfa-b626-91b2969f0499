<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class NewsArticle extends Model
{
    use HasFactory;

    protected $table = 'news_articles';

    protected $fillable = [
        'title',
        'code',
        'description',
        'content',
        'hits',
        'image',
        'embed',
        'featured',
        'link',
        'url_target',
        'status',
        'seo_title',
        'seo_description',
        'seo_keywords',
        'seo_image',
        'tags',
        'published',
        'creator_id',
        'editor_id',
        'category_id',
        'position',
    ];

     protected $casts = [
        'hits' => 'integer',
        'featured' => 'boolean',
        'status' => 'boolean',
        'published' => 'datetime',
    ];


     public function categories(): BelongsToMany
    {
        return $this->belongsToMany(
            NewsCategory::class,
            'news_article_category',
            'news_id',
            'category_id'
        );
    }

     public function primaryCategory(): BelongsTo
    {
        return $this->belongsTo(NewsCategory::class, 'category_id');
    }

     public function creator(): BelongsTo
    {
        return $this->belongsTo(ErpMember::class, 'creator_id');
    }

     public function editor(): BelongsTo
    {
        return $this->belongsTo(ErpMember::class, 'editor_id');
    }

}
