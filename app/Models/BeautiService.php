<?php

namespace App\Models;

use App\Traits\Admin\HasBlameable;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class BeautiService extends Model
{
    use HasFactory, HasBlameable;

    protected $fillable = [
        'name',
        'slug',
        'description',
        'image',
        'status',
        'position',
        'seo_title',
        'seo_description',
        'seo_keywords',
        'seo_image',
        'creator_id',
        'editor_id',
    ];

    protected $casts = [
        'status' => 'boolean',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    public function types()
    {
        return $this->belongsToMany(
            BeautiType::class,
            'beauti_type_service',
            'service_id',
            'type_id'
        )->withTimestamps();
    }

    public function creator()
    {
        return $this->belongsTo(ErpMember::class, 'creator_id', 'id')
            ->withDefault();
    }

    public function editor()
    {
        return $this->belongsTo(ErpMember::class, 'editor_id', 'id')
            ->withDefault();
    }


    public function stores()
    {
        return $this->belongsToMany(
            BeautiStore::class,
            'beauti_store_services',
            'service_id',
            'store_id'
        )->withPivot(['price_from', 'status'])
            ->withTimestamps();
    }

    public function storeServices()
    {
        return $this->hasMany(BeautiStoreService::class, 'service_id', 'id');
    }

    public function scopeActive($query)
    {
        return $query->where('status', 1);
    }
}
