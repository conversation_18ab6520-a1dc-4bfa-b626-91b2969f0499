<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Carbon\Carbon;

class PasswordResetToken extends Model
{
  protected $fillable = [
    'email',
    'token',
    'expires_at',
    'used_at',
  ];

  protected $casts = [
    'expires_at' => 'datetime',
    'used_at'    => 'datetime',
    'created_at' => 'datetime',
    'updated_at' => 'datetime',
  ];

  /**
   * Check if token is expired
   */
  public function isExpired(): bool
  {
    return $this->expires_at && $this->expires_at->isPast();
  }

  /**
   * Check if token is valid (not expired and not used)
   */
  public function isValid(): bool
  {
    return !$this->isExpired() && !$this->isUsed();
  }

  /**
   * Check if token is already used
   */
  public function isUsed(): bool
  {
    return !is_null($this->used_at);
  }

  /**
   * Mark token as used
   */
  public function markAsUsed(): void
  {
    $this->used_at = now();
    $this->save();
  }

  /**
   * Generate a secure reset token
   */
  public static function generateToken(): string
  {
    return bin2hex(random_bytes(32)); // 64 characters
  }

  /**
   * Create or update reset token for email
   */
  public static function createOrUpdate(string $email, int $expireMinutes = 60): self
  {
    return self::updateOrCreate(
      ['email' => $email],
      [
        'token'      => self::generateToken(),
        'expires_at' => now()->addMinutes($expireMinutes),
        'used_at'    => null,
      ]
    );
  }

  /**
   * Find valid token by token string
   */
  public static function findValidToken(string $token): ?self
  {
    return self::where('token', $token)
      ->whereNull('used_at')
      ->where('expires_at', '>', now())
      ->first();
  }

  /**
   * Invalidate all tokens for email
   */
  public static function invalidateAllForEmail(string $email): void
  {
    self::where('email', $email)->update(['used_at' => now()]);
  }
}
