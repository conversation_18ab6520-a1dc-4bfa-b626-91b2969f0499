<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class BeautiStoreService extends Model
{
    protected $fillable = [
        'store_id',
        'service_id',
        'price_from',
        'status',
    ];

    public $timestamps = true;

    protected $casts = [
        'price_from' => 'integer',
        'status' => 'boolean',
    ];

    public function store()
    {
        return $this->belongsTo(BeautiStore::class, 'store_id', 'id');
    }

    public function service()
    {
        return $this->belongsTo(BeautiService::class, 'service_id', 'id');
    }

    public function packages()
    {
        return $this->hasMany(BeautiStorePackage::class, 'store_service_id', 'id');
    }

    public function scopeActive($query)
    {
        return $query->where('status', 1);
    }
}
