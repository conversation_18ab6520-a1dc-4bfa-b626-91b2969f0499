<?php

use App\Helpers\ValidationHelper;

if (!function_exists('is_valid_email')) {
    /**
     * Check if email is valid
     */
    function is_valid_email(?string $email): bool
    {
        return ValidationHelper::isValidEmail($email);
    }
}

if (!function_exists('is_valid_vietnam_phone')) {
    /**
     * Check if Vietnam phone number is valid
     */
    function is_valid_vietnam_phone(?string $phone, string $type = 'all'): bool
    {
        return ValidationHelper::isValidVietnamPhone($phone, $type);
    }
}

if (!function_exists('is_valid_mobile_phone')) {
    /**
     * Check if mobile phone number is valid
     */
    function is_valid_mobile_phone(?string $phone): bool
    {
        return ValidationHelper::isValidMobilePhone($phone);
    }
}

if (!function_exists('detect_identifier_type')) {
    /**
     * Detect if identifier is email or phone
     */
    function detect_identifier_type(?string $identifier): ?string
    {
        return ValidationHelper::detectIdentifierType($identifier);
    }
}

if (!function_exists('format_phone_number')) {
    /**
     * Format phone number to standard format
     */
    function format_phone_number(?string $phone): ?string
    {
        return ValidationHelper::formatPhoneNumber($phone);
    }
}

if (!function_exists('get_phone_carrier')) {
    /**
     * Get phone carrier from phone number
     */
    function get_phone_carrier(?string $phone): ?string
    {
        return ValidationHelper::getPhoneCarrier($phone);
    }
}

if (!function_exists('validate_password_strength')) {
    /**
     * Validate password strength
     */
    function validate_password_strength(?string $password, string $level = 'basic'): bool
    {
        return ValidationHelper::validatePasswordStrength($password, $level);
    }
}
