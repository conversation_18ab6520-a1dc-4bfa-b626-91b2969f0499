<?php

namespace App\Providers;

use App\Repositories\Admin\BeautiPackageRepository;
use App\Repositories\Admin\BeautiRepository;
use App\Repositories\Admin\BeautiServiceRepository;
use App\Repositories\Admin\BeautiStoreRepository;
use App\Repositories\Admin\BeautiTypeRepository;
use App\Repositories\Admin\HostRepository;
use App\Repositories\Admin\Interfaces\BeautiPackageRepositoryInterface;
use App\Repositories\Admin\Interfaces\BeautiRepositoryInterface;
use App\Repositories\Admin\Interfaces\BeautiServiceRepositoryInterface;
use App\Repositories\Admin\Interfaces\BeautiStoreRepositoryInterface;
use App\Repositories\Admin\Interfaces\BeautiTypeRepositoryInterface;
use App\Repositories\Admin\Interfaces\HostRepositoryInterface;
use App\Repositories\Admin\Interfaces\LocationAreaRepositoryInterface;
use App\Repositories\Admin\Interfaces\LocationDistrictRepositoryInterface;
use App\Repositories\Admin\Interfaces\LocationProvinceRepositoryInterface;
use App\Repositories\Admin\Interfaces\LocationStreetRepositoryInterface;
use App\Repositories\Admin\Interfaces\LocationWardRepositoryInterface;
use App\Repositories\Admin\Interfaces\MemberRepositoryInterface;
use App\Repositories\Admin\Interfaces\NewsArticleRepositoryInterface;
use App\Repositories\Admin\Interfaces\NewsCategoryRepositoryInterface;
use App\Repositories\Admin\Interfaces\SystemConfigRepositoryInterface;
use App\Repositories\Admin\Interfaces\TeamRepositoryInterface;
use App\Repositories\Admin\Interfaces\UserRepositoryInterface as AdminUserRepositoryInterface;
use App\Repositories\Admin\LocationAreaRepository;
use App\Repositories\Admin\LocationDistrictRepository;
use App\Repositories\Admin\LocationProvinceRepository;
use App\Repositories\Admin\LocationStreetRepository;
use App\Repositories\Admin\LocationWardRepository;
use App\Repositories\Admin\MemberRepository;
use App\Repositories\Admin\NewsArticleRepository;
use App\Repositories\Admin\NewsCategoryRepository;
use App\Repositories\Admin\SystemConfigRepository;
use App\Repositories\Admin\TeamRepository;
use App\Repositories\Admin\UserRepository as AdminUserRepository;
use App\Services\Admin\AuthService;
use App\Services\Admin\BeautiPackageService;
use App\Services\Admin\BeautiService;
use App\Services\Admin\BeautiServiceService;
use App\Services\Admin\BeautiStoreService;
use App\Services\Admin\BeautiTypeService;
use App\Services\Admin\HostService;
use App\Services\Admin\Interfaces\AuthServiceInterface;
use App\Services\Admin\Interfaces\BeautiPackageServiceInterface;
use App\Services\Admin\Interfaces\BeautiServiceInterface;
use App\Services\Admin\Interfaces\BeautiServiceServiceInterface;
use App\Services\Admin\Interfaces\BeautiStoreServiceInterface;
use App\Services\Admin\Interfaces\BeautiTypeServiceInterface;
use App\Services\Admin\Interfaces\HostServiceInterface;
use App\Services\Admin\Interfaces\LocationAreaServiceInterface;
use App\Services\Admin\Interfaces\LocationDistrictServiceInterface;
use App\Services\Admin\Interfaces\LocationProvinceServiceInterface;
use App\Services\Admin\Interfaces\LocationStreetServiceInterface;
use App\Services\Admin\Interfaces\LocationWardServiceInterface;
use App\Services\Admin\Interfaces\MemberServiceInterface;
use App\Services\Admin\Interfaces\NewsArticleServiceInterface;
use App\Services\Admin\Interfaces\NewsCategoryServiceInterface;
use App\Services\Admin\Interfaces\SystemConfigServiceInterface;
use App\Services\Admin\Interfaces\TeamServiceInterface;
use App\Services\Admin\Interfaces\UserServiceInterface;
use App\Services\Admin\LocationAreaService;
use App\Services\Admin\LocationDistrictService;
use App\Services\Admin\LocationProvinceService;
use App\Services\Admin\LocationStreetService;
use App\Services\Admin\LocationWardService;
use App\Services\Admin\MemberService;
use App\Services\Admin\NewsArticleService;
use App\Services\Admin\NewsCategoryService;
use App\Services\Admin\SystemConfigService;
use App\Services\Admin\TeamService;
use App\Services\Admin\UserService;
use App\Services\External\Interfaces\ZaloServiceInterface;
use App\Services\External\ZaloService;
use Illuminate\Support\Facades\URL;
use Illuminate\Support\ServiceProvider;
use App\Repositories\Interfaces\PasswordResetTokenRepositoryInterface;
use App\Repositories\PasswordResetTokenRepository;

class AppServiceProvider extends ServiceProvider
{
  /**
   * Register any application services.
   */
  public function register(): void
  {
    $this->app->bind(
      MemberRepositoryInterface::class,
      MemberRepository::class
    );
    $this->app->bind(
      MemberServiceInterface::class,
      MemberService::class
    );
    $this->app->bind(
      AuthServiceInterface::class,
      AuthService::class
    );
    $this->app->bind(
      TeamRepositoryInterface::class,
      TeamRepository::class
    );
    $this->app->bind(
      TeamServiceInterface::class,
      TeamService::class
    );
    $this->app->bind(
      LocationProvinceRepositoryInterface::class,
      LocationProvinceRepository::class
    );
    $this->app->bind(
      LocationProvinceServiceInterface::class,
      LocationProvinceService::class
    );
    $this->app->bind(
      LocationDistrictRepositoryInterface::class,
      LocationDistrictRepository::class
    );
    $this->app->bind(
      LocationDistrictServiceInterface::class,
      LocationDistrictService::class
    );
    $this->app->bind(
      LocationWardRepositoryInterface::class,
      LocationWardRepository::class
    );
    $this->app->bind(
      LocationWardServiceInterface::class,
      LocationWardService::class
    );
    $this->app->bind(
      LocationStreetRepositoryInterface::class,
      LocationStreetRepository::class
    );
    $this->app->bind(
      LocationStreetServiceInterface::class,
      LocationStreetService::class
    );
    $this->app->bind(
      LocationAreaRepositoryInterface::class,
      LocationAreaRepository::class
    );
    $this->app->bind(
      LocationAreaServiceInterface::class,
      LocationAreaService::class
    );
    $this->app->bind(
      SystemConfigRepositoryInterface::class,
      SystemConfigRepository::class
    );
    $this->app->bind(
      SystemConfigServiceInterface::class,
      SystemConfigService::class
    );
    $this->app->bind(
      NewsCategoryRepositoryInterface::class,
      NewsCategoryRepository::class
    );
    $this->app->bind(
      NewsCategoryServiceInterface::class,
      NewsCategoryService::class
    );
    $this->app->bind(
      NewsArticleRepositoryInterface::class,
      NewsArticleRepository::class
    );
    $this->app->bind(
      NewsArticleServiceInterface::class,
      NewsArticleService::class
    );
    $this->app->bind(
      AdminUserRepositoryInterface::class,
      AdminUserRepository::class
    );
    $this->app->bind(
      UserServiceInterface::class,
      UserService::class
    );
    $this->app->bind(
      HostRepositoryInterface::class,
      HostRepository::class
    );
    $this->app->bind(
      HostServiceInterface::class,
      HostService::class
    );
    $this->app->bind(
      BeautiTypeRepositoryInterface::class,
      BeautiTypeRepository::class
    );
    $this->app->bind(
      BeautiTypeServiceInterface::class,
      BeautiTypeService::class
    );
    $this->app->bind(
      BeautiServiceRepositoryInterface::class,
      BeautiServiceRepository::class
    );
    $this->app->bind(
      BeautiServiceServiceInterface::class,
      BeautiServiceService::class
    );
    $this->app->bind(
      BeautiStoreRepositoryInterface::class,
      BeautiStoreRepository::class
    );
    $this->app->bind(
      BeautiStoreServiceInterface::class,
      BeautiStoreService::class
    );
    $this->app->bind(
      BeautiRepositoryInterface::class,
      BeautiRepository::class
    );
    $this->app->bind(
      BeautiServiceInterface::class,
      BeautiService::class
    );
    $this->app->bind(
      BeautiPackageRepositoryInterface::class,
      BeautiPackageRepository::class
    );
    $this->app->bind(
      BeautiPackageServiceInterface::class,
      BeautiPackageService::class
    );

    // External Services
    $this->app->bind(
      ZaloServiceInterface::class,
      ZaloService::class
    );

    // Password Reset Token Repository
    $this->app->bind(
      PasswordResetTokenRepositoryInterface::class,
      PasswordResetTokenRepository::class
    );
  }

  /**
   * Bootstrap any application services.
   */
  public function boot(): void
  {
    if (env('APP_ENV') === 'production') {
      URL::forceScheme('https');
    }
  }
}
