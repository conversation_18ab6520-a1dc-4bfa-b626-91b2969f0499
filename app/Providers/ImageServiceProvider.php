<?php

namespace App\Providers;

use App\Services\ImageService;
use App\Services\Interfaces\ImageServiceInterface;
use Illuminate\Support\ServiceProvider;

class ImageServiceProvider extends ServiceProvider
{
  /**
   * Register services.
   */
  public function register(): void
  {
    $this->app->singleton(ImageServiceInterface::class, function ($app) {
      return new ImageService();
    });

    $this->app->alias(ImageServiceInterface::class, 'image.service');
  }

  /**
   * Bootstrap services.
   */
  public function boot(): void
  {
    //
  }
}
