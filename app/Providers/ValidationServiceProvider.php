<?php

namespace App\Providers;

use App\Helpers\ValidationHelper;
use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Validator;

class ValidationServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // Đăng ký custom validation rules
        $this->registerCustomValidationRules();
    }

    /**
     * Register custom validation rules
     */
    protected function registerCustomValidationRules(): void
    {
        // Vietnam phone validation
        Validator::extend('vietnam_phone', function ($attribute, $value, $parameters, $validator) {
            $type = $parameters[0] ?? 'all';
            return ValidationHelper::isValidVietnamPhone($value, $type);
        });

        Validator::replacer('vietnam_phone', function ($message, $attribute, $rule, $parameters) {
            $type = $parameters[0] ?? 'all';
            return config("validation.messages.phone.{$type}") ?: 'Số điện thoại không đúng định dạng';
        });

        // Custom email validation
        Validator::extend('custom_email', function ($attribute, $value, $parameters, $validator) {
            return ValidationHelper::isValidEmail($value);
        });

        Validator::replacer('custom_email', function ($message, $attribute, $rule, $parameters) {
            return config('validation.messages.email') ?: 'Email không đúng định dạng';
        });

        // Identifier validation (email or phone)
        Validator::extend('identifier', function ($attribute, $value, $parameters, $validator) {
            return ValidationHelper::detectIdentifierType($value) !== null;
        });

        Validator::replacer('identifier', function ($message, $attribute, $rule, $parameters) {
            return 'Vui lòng nhập email hoặc số điện thoại hợp lệ';
        });

        // Password strength validation
        Validator::extend('password_strength', function ($attribute, $value, $parameters, $validator) {
            $level = $parameters[0] ?? 'basic';
            return ValidationHelper::validatePasswordStrength($value, $level);
        });

        Validator::replacer('password_strength', function ($message, $attribute, $rule, $parameters) {
            $level = $parameters[0] ?? 'basic';
            return config("validation.messages.password.{$level}") ?: 'Mật khẩu không đủ mạnh';
        });
    }
}
