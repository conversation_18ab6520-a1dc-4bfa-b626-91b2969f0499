<?php

namespace App\Providers;

use App\Models\BeautiType;
use App\Models\LocationProvince;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\View;
use Illuminate\Support\ServiceProvider;

class ViewServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        View::composer('partials.nav-menu', function ($view) {
            $beautyTypes = BeautiType::with(['beauties.stores' => function ($query) {
                $query->select('id', 'beauti_id', 'province_code')
                    ->where('status', 1)
                    ->distinct();
            }])->get();

            $provinceMap = DB::table('location_province')->pluck('name', 'code')->toArray(); 

            $view->with([
                'beautyTypes' => $beautyTypes,
                'provinceMap' => $provinceMap,
            ]);
        });
    }
}
