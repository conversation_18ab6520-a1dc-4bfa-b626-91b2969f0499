<?php

namespace App\Providers;

use App\Repositories\BeautiStoreRepository;
use App\Repositories\BeautiStoreServiceRepository;
use App\Repositories\Interfaces\BeautiStoreRepositoryInterface;
use App\Repositories\Interfaces\BeautiStoreServiceRepositoryInterface;
use App\Repositories\Interfaces\NewsArticleRepositoryInterface;
use App\Repositories\Interfaces\UserRepositoryInterface;
use App\Repositories\Interfaces\UserVerificationRepositoryInterface;
use App\Repositories\NewsArticleRepository;
use App\Repositories\UserRepository;
use App\Repositories\UserVerificationRepository;
use App\Services\AuthService;
use App\Services\BeautiStoreService;
use App\Services\Interfaces\AuthServiceInterface;
use App\Services\Interfaces\BeautiStoreServiceInterface;
use App\Services\Interfaces\NewsArticleServiceInterface;
use App\Services\NewsArticleService;
use Illuminate\Support\ServiceProvider;
use Illuminate\Pagination\Paginator;

class ClientServiceProvider extends ServiceProvider
{
  /**
   * Register services.
   */
  public function register(): void
  {
    $this->app->bind(
      UserRepositoryInterface::class,
      UserRepository::class
    );

    $this->app->bind(
      UserVerificationRepositoryInterface::class,
      UserVerificationRepository::class
    );

    $this->app->bind(
      AuthServiceInterface::class,
      AuthService::class
    );

    $this->app->bind(
      BeautiStoreRepositoryInterface::class,
      BeautiStoreRepository::class
    );

    $this->app->bind(
      BeautiStoreServiceRepositoryInterface::class,
      BeautiStoreServiceRepository::class
    );

    $this->app->bind(
      BeautiStoreServiceInterface::class,
      BeautiStoreService::class
    );

    $this->app->bind(
      NewsArticleRepositoryInterface::class,
      NewsArticleRepository::class
    );

    $this->app->bind(
      NewsArticleServiceInterface::class,
      NewsArticleService::class
    );
  }

  /**
   * Bootstrap services.
   */
  public function boot(): void
  {
    Paginator::useBootstrapFive();
  }
}
