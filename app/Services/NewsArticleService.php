<?php
namespace App\Services;

use App\Repositories\Interfaces\NewsArticleRepositoryInterface;
use App\Services\Interfaces\NewsArticleServiceInterface;

class NewsArticleService implements NewsArticleServiceInterface
{
    protected $newsRepo;

    public function __construct(NewsArticleRepositoryInterface $newsRepo)
    {
        $this->newsRepo = $newsRepo;
    }

    public function listPublishedArticles()
    {
        return $this->newsRepo->getAllPublishedWithCategory();
    }
}
