<?php

namespace App\Services\External\Interfaces;

use App\Models\ZaloOauth;

interface ZaloServiceInterface
{
  public function getAccessToken(string $authenticationCode): array;

  public function refreshAccessToken(string $refreshToken): array;

  public function saveTokens(array $oauthData): ZaloOauth;

  public function sendZns(
    string $accessToken,
    string $templateId,
    array $data,
    string $phone,
    string $trackingId
  ): array;

  public function getErrorMessage(int $errorCode): array;

  public function getLatestToken(): ?ZaloOauth;

  public function ensureValidToken(): ?ZaloOauth;
}
