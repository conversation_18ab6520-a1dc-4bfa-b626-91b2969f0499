<?php

namespace App\Services\External;

use App\Models\ZaloOauth;
use App\Services\External\Interfaces\ZaloServiceInterface;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Exception;

class ZaloService implements ZaloServiceInterface
{
  protected string $appId;
  protected string $appSecret;
  protected string $oauthUrl  = 'https://oauth.zaloapp.com/v4/oa/access_token';
  protected string $znsUrl    = 'https://business.openapi.zalo.me/message/template';

  public function __construct()
  {
    $this->appId     = config('services.zalo.app_id');
    $this->appSecret = config('services.zalo.app_secret');

    if (!$this->appId || !$this->appSecret) {
      throw new Exception('Zalo credentials not configured properly');
    }
  }

  private function requestAccessToken(array $data): array
  {
    try {
      $response = Http::asForm()
        ->withHeaders([
          'secret_key' => $this->appSecret,
        ])
        ->post($this->oauthUrl, $data);

      if (!$response->successful()) {
        Log::error('Zalo OAuth request failed', [
          'status'  => $response->status(),
          'body'    => $response->body()
        ]);
        throw new Exception('Failed to request access token from Zalo');
      }

      $oauthData = $response->json();

      if (isset($oauthData['access_token']) && isset($oauthData['refresh_token'])) {
        $this->saveTokens($oauthData);
      }

      return $oauthData;
    } catch (Exception $e) {
      Log::error('Zalo OAuth error: ' . $e->getMessage());
      throw $e;
    }
  }

  public function getAccessToken(string $authenticationCode): array
  {
    $data = [
      'app_id'     => $this->appId,
      'app_secret' => $this->appSecret,
      'code'       => $authenticationCode,
      'grant_type' => 'authorization_code',
    ];

    return $this->requestAccessToken($data);
  }

  public function refreshAccessToken(string $refreshToken): array
  {
    $data = [
      'app_id'        => $this->appId,
      'app_secret'    => $this->appSecret,
      'refresh_token' => $refreshToken,
      'grant_type'    => 'refresh_token',
    ];

    return $this->requestAccessToken($data);
  }

  public function saveTokens(array $oauthData): ZaloOauth
  {
    try {
      DB::beginTransaction();

      $oauth = ZaloOauth::create([
        'access_token'          => $oauthData['access_token'],
        'refresh_token'         => $oauthData['refresh_token'],
        'expired_access_token'  => Carbon::now()->addSeconds($oauthData['expires_in']),
        'expired_refresh_token' => Carbon::now()->addMonths(3),
        'created'               => Carbon::now(),
      ]);

      DB::commit();

      Log::info('Zalo tokens saved successfully', ['oauth_id' => $oauth->id]);

      return $oauth;
    } catch (Exception $e) {
      DB::rollBack();
      Log::error('Failed to save Zalo tokens: ' . $e->getMessage());
      throw $e;
    }
  }

  public function sendZns(
    string $accessToken,
    string $templateId,
    array $data,
    string $phone,
    string $trackingId
  ): array {
    try {
      $postData = [
        'phone'         => $phone,
        'template_id'   => $templateId,
        'template_data' => $data,
        'tracking_id'   => $trackingId
      ];

      $response = Http::withHeaders([
        'Content-Type' => 'application/json',
        'access_token' => $accessToken,
        'secret_key'   => $this->appSecret
      ])->post($this->znsUrl, $postData);

      $responseData = $response->json();

      Log::info('Zalo ZNS sent', [
        'phone'       => $phone,
        'template_id' => $templateId,
        'tracking_id' => $trackingId,
        'response'    => $responseData
      ]);

      return $responseData;
    } catch (Exception $e) {
      Log::error('Zalo ZNS send failed: ' . $e->getMessage(), [
        'phone'         => $phone,
        'template_id'   => $templateId,
        'tracking_id'   => $trackingId
      ]);
      throw $e;
    }
  }

  public function getErrorMessage(int $errorCode): array
  {
    $errorMessages = config('zalo.error_messages', []);

    return $errorMessages[$errorCode] ?? [
      'description'   => 'Unknown error code',
      'message'       => 'Không xác định được mã lỗi'
    ];
  }

  /**
   * Get the latest valid access token
   */
  public function getLatestToken(): ?ZaloOauth
  {
    return ZaloOauth::where('expired_access_token', '>', Carbon::now())
      ->orderBy('created', 'desc')
      ->first();
  }

  /**
   * Check if access token is expired and refresh if needed
   */
  public function ensureValidToken(): ?ZaloOauth
  {
    $token = $this->getLatestToken();

    if (!$token) {
      return null;
    }

    // If access token is expired but refresh token is still valid
    if (
      $token->expired_access_token <= Carbon::now() &&
      $token->expired_refresh_token > Carbon::now()
    ) {
      try {
        $this->refreshAccessToken($token->refresh_token);
        return $this->getLatestToken();
      } catch (Exception $e) {
        Log::error('Failed to refresh Zalo token: ' . $e->getMessage());
        return null;
      }
    }

    return $token;
  }
}
