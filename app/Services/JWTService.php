<?php

namespace App\Services;

use App\Models\User;
use <PERSON><PERSON>\JWTAuth\Facades\JWTAuth;
use <PERSON>mon\JWTAuth\Exceptions\JWTException;

class JWTService
{
  /**
   * Generate JWT token for user
   */
  public function generateToken(User $user): array
  {
    $token = JWTAuth::fromUser($user);
    $payload = JWTAuth::setToken($token)->getPayload();

    return [
      'token' => $token,
      'jti'   => $payload->get('jti'),
      'exp'   => $payload->get('exp'),
    ];
  }

  /**
   * Get JWT payload from token
   */
  public function getPayload(?string $token = null): ?\Tymon\JWTAuth\Payload
  {
    try {
      if ($token) {
        return JWTAuth::setToken($token)->getPayload();
      }
      return JWTAuth::getPayload();
    } catch (JWTException $e) {
      return null;
    }
  }

  /**
   * Get JTI from current token
   */
  public function getCurrentJti(): ?string
  {
    try {
      $payload = JWTAuth::getPayload();
      return $payload->get('jti');
    } catch (JWTException $e) {
      return null;
    }
  }

  /**
   * Invalidate JWT token
   */
  public function invalidateToken(?string $token = null): bool
  {
    try {
      if ($token) {
        JWTAuth::setToken($token)->invalidate();
      } else {
        JWTAuth::invalidate();
      }
      return true;
    } catch (JWTException $e) {
      return false;
    }
  }

  /**
   * Refresh JWT token
   */
  public function refreshToken(?string $token = null): ?array
  {
    try {
      if ($token) {
        $newToken = JWTAuth::setToken($token)->refresh();
      } else {
        $newToken = JWTAuth::refresh();
      }

      $payload = JWTAuth::setToken($newToken)->getPayload();

      return [
        'token' => $newToken,
        'jti'   => $payload->get('jti'),
        'exp'   => $payload->get('exp'),
      ];
    } catch (JWTException $e) {
      return null;
    }
  }

  /**
   * Get token TTL in seconds
   */
  public function getTokenTtl(): int
  {
    return config('jwt.ttl', 60) * 60; // Convert minutes to seconds
  }

  /**
   * Get refresh token TTL in seconds
   */
  public function getRefreshTokenTtl(): int
  {
    return config('jwt.refresh_ttl', 20160) * 60; // Convert minutes to seconds
  }

  /**
   * Check if token is valid
   */
  public function isTokenValid(?string $token = null): bool
  {
    try {
      if ($token) {
        JWTAuth::setToken($token)->checkOrFail();
      } else {
        JWTAuth::checkOrFail();
      }
      return true;
    } catch (JWTException $e) {
      return false;
    }
  }

  /**
   * Get user from token
   */
  public function getUserFromToken(?string $token = null): ?User
  {
    try {
      if ($token) {
        return JWTAuth::setToken($token)->authenticate();
      }
      return JWTAuth::authenticate();
    } catch (JWTException $e) {
      return null;
    }
  }
}
