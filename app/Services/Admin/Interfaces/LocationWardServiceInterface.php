<?php
namespace App\Services\Admin\Interfaces;

use App\DTOs\Admin\StoreLocationWardData;
use App\DTOs\Admin\UpdateLocationWardData;

interface LocationWardServiceInterface
{
    public function getAll();
    public function getById(int $id);
    public function create(StoreLocationWardData $data);
    public function update(int $id, UpdateLocationWardData $data);
    public function delete(int $id);
}
