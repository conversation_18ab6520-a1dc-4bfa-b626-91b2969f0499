<?php
namespace App\Services\Admin\Interfaces;

use App\DTOs\Admin\StoreLocationStreetData;
use App\DTOs\Admin\UpdateLocationStreetData;

interface LocationStreetServiceInterface
{
    public function getAll();
    public function getById(int $id);
    public function create(StoreLocationStreetData $data);
    public function update(int $id, UpdateLocationStreetData $data);
    public function delete(int $id);
}
