<?php
namespace App\Services\Admin\Interfaces;

use App\DTOs\Admin\StoreLocationDistrictData;
use App\DTOs\Admin\UpdateLocationDistrictData;

interface LocationDistrictServiceInterface
{
    public function getAll();
    public function getById(int $id);
    public function create(StoreLocationDistrictData $data);
    public function update(int $id, UpdateLocationDistrictData $data);
    public function delete(int $id);
}
