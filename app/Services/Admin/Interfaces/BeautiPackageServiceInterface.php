<?php
namespace App\Services\Admin\Interfaces;

use App\DTOs\Admin\StoreBeautiPackageData;
use App\DTOs\Admin\UpdateBeautiPackageData;

interface BeautiPackageServiceInterface
{
    public function getAll();
    public function getById(int $id);
    public function create(StoreBeautiPackageData $data);
    public function update(int $id, UpdateBeautiPackageData $data);
    public function delete(int $id);
}
