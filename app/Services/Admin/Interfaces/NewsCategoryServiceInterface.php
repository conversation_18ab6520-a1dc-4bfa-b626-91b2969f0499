<?php
namespace App\Services\Admin\Interfaces;

use App\DTOs\Admin\StoreNewsCategoryData;
use App\DTOs\Admin\UpdateNewsCategoryData;

interface NewsCategoryServiceInterface
{
    public function getAll();
    public function getById(int $id);
    public function create(StoreNewsCategoryData $data);
    public function update(int $id, UpdateNewsCategoryData $data);
    public function delete(int $id);
}
