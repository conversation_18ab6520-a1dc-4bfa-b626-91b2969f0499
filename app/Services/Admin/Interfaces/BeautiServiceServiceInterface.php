<?php
namespace App\Services\Admin\Interfaces;

use App\DTOs\Admin\StoreBeautiServiceData;
use App\DTOs\Admin\UpdateBeautiServiceData;

interface BeautiServiceServiceInterface
{
    public function getAll();
    public function getById(int $id);
    public function create(StoreBeautiServiceData $data);
    public function update(int $id, UpdateBeautiServiceData $data);
    public function delete(int $id);
}
