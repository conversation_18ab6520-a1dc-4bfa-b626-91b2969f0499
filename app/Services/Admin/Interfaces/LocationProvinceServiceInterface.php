<?php
namespace App\Services\Admin\Interfaces;

use App\DTOs\Admin\StoreLocationProvinceData;
use App\DTOs\Admin\UpdateLocationProvinceData;

interface LocationProvinceServiceInterface
{
    public function getAll();
    public function getById(int $id);
    public function create(StoreLocationProvinceData $data);
    public function update(int $id, UpdateLocationProvinceData $data);
    public function delete(int $id);
}
