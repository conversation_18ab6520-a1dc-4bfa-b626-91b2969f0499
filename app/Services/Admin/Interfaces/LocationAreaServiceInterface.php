<?php
namespace App\Services\Admin\Interfaces;

use App\DTOs\Admin\StoreLocationAreaData;
use App\DTOs\Admin\UpdateLocationAreaData;

interface LocationAreaServiceInterface
{
    public function getAll();
    public function getById(int $id);
    public function create(StoreLocationAreaData $data);
    public function update(int $id, UpdateLocationAreaData $data);
    public function delete(int $id);
}
