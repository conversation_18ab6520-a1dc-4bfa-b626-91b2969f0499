<?php

namespace App\Services\Admin;

use App\DTOs\Admin\StoreBeautiPackageData;
use App\DTOs\Admin\UpdateBeautiPackageData;
use App\Repositories\Admin\Interfaces\BeautiPackageRepositoryInterface;
use App\Services\Admin\Interfaces\BeautiPackageServiceInterface;
use App\Services\Admin\Interfaces\BeautiServiceInterface;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;

class BeautiPackageService implements BeautiPackageServiceInterface
{
    protected $repo;

    public function __construct(BeautiPackageRepositoryInterface $repo)
    {
        $this->repo = $repo;
    }

    public function getAll()
    {
        return $this->repo->all();
    }

    public function getById($id)
    {
        return $this->repo->findOrFail($id);
    }

    public function create(StoreBeautiPackageData $dto)
    {
        return DB::transaction(function () use ($dto) {
            $data = $dto->toCreateArray();
            if ($data['name']) {
                $data['slug'] = Str::slug($data['name']);
            }
            $storeService = DB::table('beauti_store_services')
                ->where('store_id', $dto->storeId)
                ->where('service_id', $dto->serviceId)
                ->first();

            if (!$storeService) {
                throw new \Exception('Không tìm thấy dịch vụ của cơ sở n phù hợp.');
            }

            $data['store_service_id'] = $storeService->id;

            $item = $this->repo->create($data);

            return $item;
        });
    }

    public function update($id, UpdateBeautiPackageData $dto)
    {
        return DB::transaction(function () use ($id, $dto) {
            $item = $this->repo->findOrFail($id);

            $dataToUpdate = $dto->toUpdateArray();
            if ($dataToUpdate['name']) {
                $dataToUpdate['slug'] = Str::slug($dataToUpdate['name']);
            }
            $updated = $this->repo->update($item, $dataToUpdate);


            return $updated;
        });
    }


    public function delete($id)
    {
        $deleteItem = $this->repo->findOrFail($id);

        return $this->repo->delete($deleteItem);
    }
}
