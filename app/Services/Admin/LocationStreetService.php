<?php

namespace App\Services\Admin;

use App\DTOs\Admin\StoreLocationStreetData;
use App\DTOs\Admin\UpdateLocationStreetData;
use App\Repositories\Admin\Interfaces\LocationStreetRepositoryInterface;
use App\Services\Admin\Interfaces\LocationStreetServiceInterface;
use Illuminate\Support\Facades\DB;

class LocationStreetService implements LocationStreetServiceInterface
{
    protected $repo;

    public function __construct(LocationStreetRepositoryInterface $repo)
    {
        $this->repo = $repo;
    }

    public function getAll()
    {
        return $this->repo->all();
    }

    public function getById($id)
    {
        return $this->repo->findOrFail($id);
    }

    public function create(StoreLocationStreetData $dto)
    {
        return DB::transaction(function () use ($dto) {
            $data = $dto->toCreateArray();
            $item = $this->repo->create($data);
            return $item;
        });
    }

    public function update($id, UpdateLocationStreetData $dto)
    {
        return DB::transaction(function () use ($id, $dto) {
            $item = $this->repo->findOrFail($id);

            $dataToUpdate = $dto->toUpdateArray();

            $updated = $this->repo->update($item, $dataToUpdate);
            return $updated;
        });
    }


    public function delete($id)
    {
       $deleteItem = $this->repo->findOrFail($id);
       return $this->repo->delete($deleteItem);
    }
}
