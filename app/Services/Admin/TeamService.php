<?php

namespace App\Services\Admin;

use App\DTOs\Admin\StoreTeamData;
use App\DTOs\Admin\UpdateTeamData;
use App\Repositories\Admin\Interfaces\TeamRepositoryInterface;
use App\Services\Admin\Interfaces\TeamServiceInterface;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;

class TeamService implements TeamServiceInterface 
{
    protected $repo;

    public function __construct(TeamRepositoryInterface $repo)
    {
        $this->repo = $repo;
    }

    public function getAll()
    {
        return $this->repo->all();
    }

    public function getById($id)
    {
        return $this->repo->findOrFail($id);
    }

    public function create(StoreTeamData $dto)
    {
        return DB::transaction(function () use ($dto) {
            $data = $dto->toCreateArray();
            $item = $this->repo->create($data);
            return $item;
        });
    }

    public function update($id, UpdateTeamData $dto)
    {
        return DB::transaction(function () use ($id, $dto) {
            $item = $this->repo->findOrFail($id);
            $dataToUpdate = $dto->toUpdateArray();
            $updated = $this->repo->update($item, $dataToUpdate);
            return $updated;
        });
    }


    public function delete($id)
    {
       $deleteItem = $this->repo->findOrFail($id);
       return $this->repo->delete($deleteItem);
    }
}
