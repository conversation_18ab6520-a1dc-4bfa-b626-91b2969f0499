<?php

namespace App\Services\Admin;

use App\DTOs\Admin\StoreLocationAreaData;
use App\DTOs\Admin\UpdateLocationAreaData;
use App\Repositories\Admin\Interfaces\LocationAreaRepositoryInterface;
use App\Services\Admin\Interfaces\LocationAreaServiceInterface;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Illuminate\Http\UploadedFile;

class LocationAreaService implements LocationAreaServiceInterface
{
    protected $repo;

    protected const UPLOAD_SUBDIR = 'uploads/areas';

    public function __construct(LocationAreaRepositoryInterface $repo, ImageService $imageService)
    {
        $this->repo = $repo;
        $this->imageService = $imageService;
    }

    public function getAll()
    {
        return $this->repo->all();
    }

    public function getById($id)
    {
        return $this->repo->findOrFail($id);
    }

    public function create(StoreLocationAreaData $dto)
    {
        return DB::transaction(function () use ($dto) {
            $data = $dto->toCreateArray();

            if ($data['name']) {
                $data['code'] = Str::slug($data['name']);
            }
            if (isset($data['image']) && $data['image'] instanceof UploadedFile) {
                $data['image'] = $this->imageService->upload(
                    $data['image'],
                    self::UPLOAD_SUBDIR,
                    $dto->name
                );
            }

            $item = $this->repo->create($data);
            return $item;
        });
    }

    public function update($id, UpdateLocationAreaData $dto)
    {
        return DB::transaction(function () use ($id, $dto) {
            $item = $this->repo->findOrFail($id);
            $dataToUpdate = $dto->toUpdateArray();
            if ($dataToUpdate['name']) {
                $dataToUpdate['code'] = Str::slug($dataToUpdate['name']);
            }

            if (array_key_exists('image', $dataToUpdate)) {
                if (!empty($item->image)) {
                    $this->imageService->delete($item->image);
                }
                if ($dataToUpdate['image'] instanceof UploadedFile) {
                    $dataToUpdate['image'] = $this->imageService->upload(
                        $dataToUpdate['image'],
                        self::UPLOAD_SUBDIR,
                        $dto->name
                    );
                }
            }

            $updated = $this->repo->update($item, $dataToUpdate);
            return $updated;
        });
    }


    public function delete($id)
    {
        $deleteItem = $this->repo->findOrFail($id);

        if (! empty($deleteItem->image)) {
            $this->imageService->delete($deleteItem->image);
        }

        return $this->repo->delete($deleteItem);
    }
}
