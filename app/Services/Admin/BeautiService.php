<?php

namespace App\Services\Admin;

use App\DTOs\Admin\StoreBeautiData;
use App\DTOs\Admin\UpdateBeautiData;
use App\Repositories\Admin\Interfaces\BeautiRepositoryInterface;
use App\Repositories\Admin\Interfaces\BeautiStoreRepositoryInterface;
use App\Services\Admin\Interfaces\BeautiServiceInterface;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;

class BeautiService implements BeautiServiceInterface
{
    protected $repo;

    public function __construct(BeautiRepositoryInterface $repo)
    {
        $this->repo = $repo;
    }

    public function getAll()
    {
        return $this->repo->all();
    }

    public function getById($id)
    {
        return $this->repo->findOrFail($id);
    }

    public function create(StoreBeautiData $dto)
    {
        return DB::transaction(function () use ($dto) {
            $data = $dto->toCreateArray();
            if ($data['name']) {
                $data['slug'] = Str::slug($data['name']);
            }

            $item = $this->repo->create($data);

            if (!empty($dto->getBeautiTypes())) {
                $item->types()->sync($dto->getBeautiTypes());
            }

            return $item;
        });
    }

    public function update($id, UpdateBeautiData $dto)
    {
        return DB::transaction(function () use ($id, $dto) {
            $item = $this->repo->findOrFail($id);

            $dataToUpdate = $dto->toUpdateArray();
            if (empty($dataToUpdate['slug']) && !empty($dataToUpdate['name'])) {
                $dataToUpdate['slug'] = Str::slug($dataToUpdate['name']);
            }
            $updated = $this->repo->update($item, $dataToUpdate);

            $updated->types()->sync($dto->getBeautiTypes());

            return $updated;
        });
    }


    public function delete($id)
    {
        $deleteItem = $this->repo->findOrFail($id);

        return $this->repo->delete($deleteItem);
    }
}
