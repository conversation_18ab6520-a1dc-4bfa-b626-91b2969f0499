<?php

namespace App\Services\Admin;

use App\DTOs\Admin\StoreLocationDistrictData;
use App\DTOs\Admin\UpdateLocationDistrictData;
use App\Repositories\Admin\Interfaces\LocationDistrictRepositoryInterface;
use App\Services\Admin\Interfaces\LocationDistrictServiceInterface;
use Illuminate\Support\Facades\DB;

class LocationDistrictService implements LocationDistrictServiceInterface
{
    protected $repo;

    public function __construct(LocationDistrictRepositoryInterface $repo)
    {
        $this->repo = $repo;
    }

    public function getAll()
    {
        return $this->repo->all();
    }

    public function getById($id)
    {
        return $this->repo->findOrFail($id);
    }

    public function create(StoreLocationDistrictData $dto)
    {
        return DB::transaction(function () use ($dto) {
            $data = $dto->toCreateArray();
            $item = $this->repo->create($data);
            return $item;
        });
    }

    public function update($id, UpdateLocationDistrictData $dto)
    {
        return DB::transaction(function () use ($id, $dto) {
            $item = $this->repo->findOrFail($id);

            $dataToUpdate = $dto->toUpdateArray();

            $updated = $this->repo->update($item, $dataToUpdate);
            return $updated;
        });
    }


    public function delete($id)
    {
       $deleteItem = $this->repo->findOrFail($id);
       return $this->repo->delete($deleteItem);
    }
}
