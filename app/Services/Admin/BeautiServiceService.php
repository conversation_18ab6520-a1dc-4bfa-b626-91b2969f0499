<?php

namespace App\Services\Admin;

use App\DTOs\Admin\StoreBeautiServiceData;
use App\DTOs\Admin\UpdateBeautiServiceData;
use App\Repositories\Admin\Interfaces\BeautiServiceRepositoryInterface;
use App\Services\Admin\Interfaces\BeautiServiceServiceInterface;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class BeautiServiceService implements BeautiServiceServiceInterface
{
    protected const UPLOAD_SUBDIR = 'uploads/beauti-services';
    protected $repo;

    public function __construct(BeautiServiceRepositoryInterface $repo, ImageService $imageService)
    {
        $this->repo = $repo;
        $this->imageService = $imageService;
    }

    public function getAll()
    {
        return $this->repo->all();
    }

    public function getById($id)
    {
        return $this->repo->findOrFail($id);
    }

    public function create(StoreBeautiServiceData $dto)
    {
        return DB::transaction(function () use ($dto) {
            $data = $dto->toCreateArray();
            if ($data['name']) {
                $data['slug'] = Str::slug($data['name']);
            }

            if (isset($data['image']) && $data['image'] instanceof UploadedFile) {
                $data['image'] = $this->imageService->upload(
                    $data['image'],
                    self::UPLOAD_SUBDIR,
                    $dto->name
                );
            }

            if (isset($data['seo_image']) && $data['seo_image'] instanceof UploadedFile) {
                $data['seo_image'] = $this->imageService->upload(
                    $data['seo_image'],
                    self::UPLOAD_SUBDIR,
                    $dto->name
                );
            }


            $item = $this->repo->create($data);

            if (!empty($dto->getTypes())) {
                $item->types()->sync($dto->getTypes());
            }
            return $item;
        });
    }

    public function update($id, UpdateBeautiServiceData $dto)
    {
        return DB::transaction(function () use ($id, $dto) {
            $item = $this->repo->findOrFail($id);

            $dataToUpdate = $dto->toUpdateArray();
             if ($dataToUpdate['name']) {
                $dataToUpdate['slug'] = Str::slug($dataToUpdate['name']);
            }
            if (array_key_exists('image', $dataToUpdate)) {
                if (!empty($item->image)) {
                    $this->imageService->delete($item->image);
                }
                if ($dataToUpdate['image'] instanceof UploadedFile) {
                    $dataToUpdate['image'] = $this->imageService->upload(
                        $dataToUpdate['image'],
                        self::UPLOAD_SUBDIR,
                        $dto->name
                    );
                }
            }

            if (array_key_exists('seo_image', $dataToUpdate)) {
                if (!empty($item->seo_image)) {
                    $this->imageService->delete($item->seo_image);
                }
                if ($dataToUpdate['seo_image'] instanceof UploadedFile) {
                    $dataToUpdate['seo_image'] = $this->imageService->upload(
                        $dataToUpdate['seo_image'],
                        self::UPLOAD_SUBDIR,
                        $dto->name
                    );
                }
            }

            $updated = $this->repo->update($item, $dataToUpdate);
            $updated->types()->sync($dto->getTypes());
            return $updated;
        });
    }


    public function delete($id)
    {
        $deleteItem = $this->repo->findOrFail($id);


        if (! empty($deleteItem->image)) {
            $this->imageService->delete($deleteItem->image);
        }

        if (! empty($deleteItem->seo_image)) {
            $this->imageService->delete($deleteItem->seo_image);
        }


        return $this->repo->delete($deleteItem);
    }
}
