<?php

namespace App\Services\Admin;

use App\DTOs\Admin\StoreMemberData;
use App\DTOs\Admin\UpdateMemberData;
use App\Repositories\Admin\Interfaces\MemberRepositoryInterface;
use App\Services\Admin\Interfaces\MemberServiceInterface;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use App\Services\Admin\ImageService;

class MemberService implements MemberServiceInterface
{
    protected $repo;
    protected const UPLOAD_SUBDIR = 'uploads/avatars';

    public function __construct(MemberRepositoryInterface $repo, ImageService $imageService)
    {
        $this->repo = $repo;
        $this->imageService = $imageService;
    }

    public function getAll()
    {
        return $this->repo->all();
    }

    public function getById($id)
    {
        return $this->repo->findOrFail($id);
    }

    public function create(StoreMemberData $dto)
    {
        return DB::transaction(function () use ($dto) {
            $data = $dto->toCreateArray();
            if (isset($data['avatar']) && $data['avatar'] instanceof UploadedFile) {
                $data['avatar'] = $this->imageService->upload(
                    $data['avatar'],
                    self::UPLOAD_SUBDIR,
                    $dto->username
                );
            }
            $member = $this->repo->create($data);
            if (is_array($dto->roles)) {
                $member->syncRoles($dto->roles);
            }
            return $member;
        });
    }

    public function update($id, UpdateMemberData $dto)
    {
        return DB::transaction(function () use ($id, $dto) {
            $member = $this->repo->findOrFail($id);
            $dataToUpdate = $dto->toUpdateArray($member);
            if (array_key_exists('avatar', $dataToUpdate)) {
                if (!empty($member->avatar)) {
                    $this->imageService->delete($member->avatar);
                }
                if ($dataToUpdate['avatar'] instanceof UploadedFile) {
                    $dataToUpdate['avatar'] = $this->imageService->upload(
                        $dataToUpdate['avatar'],
                        self::UPLOAD_SUBDIR,
                        $dto->username
                    );
                }
            }
            $member = $this->repo->update($member->id, $dataToUpdate);
            if (is_array($dto->roles)) {
                $member->syncRoles($dto->roles);
            }
            return $member;
        });
    }


    public function delete($id)
    {
        $member = $this->repo->findOrFail($id);
        if (! empty($member->avatar)) {
            $this->imageService->delete($member->avatar);
        }
        return $this->repo->delete($member);
    }
}
