<?php

namespace App\Services\Admin;

use App\DTOs\Admin\StoreLocationWardData;
use App\DTOs\Admin\UpdateLocationWardData;
use App\Repositories\Admin\Interfaces\LocationWardRepositoryInterface;
use App\Services\Admin\Interfaces\LocationWardServiceInterface;
use Illuminate\Support\Facades\DB;

class LocationWardService implements LocationWardServiceInterface
{
    protected $repo;

    public function __construct(LocationWardRepositoryInterface $repo)
    {
        $this->repo = $repo;
    }

    public function getAll()
    {
        return $this->repo->all();
    }

    public function getById($id)
    {
        return $this->repo->findOrFail($id);
    }

    public function create(StoreLocationWardData $dto)
    {
        return DB::transaction(function () use ($dto) {
            $data = $dto->toCreateArray();
            $item = $this->repo->create($data);
            return $item;
        });
    }

    public function update($id, UpdateLocationWardData $dto)
    {
        return DB::transaction(function () use ($id, $dto) {
            $item = $this->repo->findOrFail($id);

            $dataToUpdate = $dto->toUpdateArray();

            $updated = $this->repo->update($item, $dataToUpdate);
            return $updated;
        });
    }


    public function delete($id)
    {
       $deleteItem = $this->repo->findOrFail($id);
       return $this->repo->delete($deleteItem);
    }
}
