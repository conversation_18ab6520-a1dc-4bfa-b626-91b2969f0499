<?php

namespace App\Services\Admin;

use App\DTOs\Admin\LoginData;
use App\Repositories\Admin\Interfaces\MemberRepositoryInterface;
use App\Services\Admin\Interfaces\AuthServiceInterface;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;

class AuthService implements AuthServiceInterface
{
    protected $repo;

    public function __construct(MemberRepositoryInterface $repo)
    {
        $this->repo = $repo;
    }

    public function login(LoginData $data): bool
    {
        $user = $this->repo->findByUsername($data->username);
        if (!$user) {
            return false;
        }

        if (!Hash::check($data->password . $user->salt, $user->password)) {
            return false;
        }

        if (!$user->status) {
            return false;
        }
        
        if (!$user->hasRole('admin')) {
            return false;
        }

        return Auth::guard('admin')->loginUsingId($user->id) !== null;
    }

    public function logout(): void
    {
        Auth::guard('admin')->logout();
    }
}
