<?php

namespace App\Services\Admin;

use App\DTOs\Admin\StoreBeautiStoreData;
use App\DTOs\Admin\UpdateBeautiStoreData;
use App\Repositories\Admin\Interfaces\BeautiStoreRepositoryInterface;
use App\Services\Admin\Interfaces\BeautiStoreServiceInterface;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;

class BeautiStoreService implements BeautiStoreServiceInterface
{
    protected $repo;

    public function __construct(BeautiStoreRepositoryInterface $repo, ImageService $imageService)
    {
        $this->repo = $repo;
        $this->imageService = $imageService;
    }

    public function getAll()
    {
        return $this->repo->all();
    }

    public function getById($id)
    {
        return $this->repo->findOrFail($id);
    }

    public function create(StoreBeautiStoreData $dto)
    {
        return DB::transaction(function () use ($dto) {
            $data = $dto->toCreateArray();
            if ($data['name']) {
                $data['slug'] = Str::slug($data['name']);
            }

            if (!empty($data['image'])) {
                $data['image'] = $this->imageService->normalizeImagePath($data['image']);
            }
            if (!empty($data['logo'])) {
                $data['logo'] = $this->imageService->normalizeImagePath($data['logo']);
            }
            if (!empty($data['seo_image'])) {
                $data['seo_image'] = $this->imageService->normalizeImagePath($data['seo_image']);
            }

            if (!empty($data['imgs']) && is_array($data['imgs'])) {
                $data['imgs'] = array_map([$this->imageService, 'normalizeImagePath'], $data['imgs']);
                $data['imgs'] =$data['imgs'];
            }


            $item = $this->repo->create($data);

            if (!empty($dto->getServices())) {
                $servicesToSync = [];
                foreach ($dto->getServices() as $serviceData) {
                    $servicesToSync[$serviceData['service_id']] = [
                        'price_from' => $serviceData['price_from'] ?? null,
                        'status' => $serviceData['status'] ?? true,
                    ];
                }
                $item->services()->syncWithoutDetaching($servicesToSync);
            }

            if (!empty($dto->getBeautiTypes())) {
                $item->types()->sync($dto->getBeautiTypes());
            }

            return $item;
        });
    }

    public function update($id, UpdateBeautiStoreData $dto)
    {
        return DB::transaction(function () use ($id, $dto) {
            $item = $this->repo->findOrFail($id);

            $dataToUpdate = $dto->toUpdateArray();
            if ($dataToUpdate['name']) {
                $dataToUpdate['slug'] = Str::slug($dataToUpdate['name']);
            }
            if (!empty($dataToUpdate['image'])) {
                $dataToUpdate['image'] = $this->imageService->normalizeImagePath($dataToUpdate['image']);
            }

            if (!empty($dataToUpdate['logo'])) {
                $dataToUpdate['logo'] = $this->imageService->normalizeImagePath($dataToUpdate['logo']);
            }

            if (!empty($dataToUpdate['seo_image'])) {
                $dataToUpdate['seo_image'] = $this->imageService->normalizeImagePath($dataToUpdate['seo_image']);
            }

            if (!empty($dataToUpdate['imgs']) && is_array($dataToUpdate['imgs'])) {
                $dataToUpdate['imgs'] = array_map([$this->imageService, 'normalizeImagePath'], $dataToUpdate['imgs']);
                $dataToUpdate['imgs'] = json_encode($dataToUpdate['imgs']);
            }

            $updated = $this->repo->update($item, $dataToUpdate);

            $newServices = [];
            foreach ($dto->getServices() as $serviceData) {
                $newServices[$serviceData['service_id']] = [
                    'price_from' => $serviceData['price_from'] ?? null,
                    'status' => $serviceData['status'] ?? true,
                ];
            }
            $updated->services()->sync($newServices);

            $updated->types()->sync($dto->getBeautiTypes());

            return $updated;
        });
    }


    public function delete($id)
    {
        $deleteItem = $this->repo->findOrFail($id);

        return $this->repo->delete($deleteItem);
    }
}
