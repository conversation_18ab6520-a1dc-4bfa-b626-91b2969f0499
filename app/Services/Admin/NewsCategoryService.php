<?php

namespace App\Services\Admin;

use App\DTOs\Admin\StoreNewsCategoryData;
use App\DTOs\Admin\UpdateNewsCategoryData;
use App\Repositories\Admin\Interfaces\NewsCategoryRepositoryInterface;
use App\Services\Admin\Interfaces\NewsCategoryServiceInterface;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class NewsCategoryService implements NewsCategoryServiceInterface
{
    protected const UPLOAD_SUBDIR = 'uploads/news-categories';
    protected $repo;

    public function __construct(NewsCategoryRepositoryInterface $repo, ImageService $imageService)
    {
        $this->repo = $repo;
        $this->imageService = $imageService;
    }

    public function getAll()
    {
        return $this->repo->all();
    }

    public function getById($id)
    {
        return $this->repo->findOrFail($id);
    }

    public function create(StoreNewsCategoryData $dto)
    {
        return DB::transaction(function () use ($dto) {
            $data = $dto->toCreateArray();
            if ($data['title']) {
                $data['code'] = Str::slug($data['title']);
            }


            if (isset($data['image']) && $data['image'] instanceof UploadedFile) {
                $data['image'] = $this->imageService->upload(
                    $data['image'],
                    self::UPLOAD_SUBDIR,
                    $dto->title
                );
            }

            $item = $this->repo->create($data);
            return $item;
        });
    }

    public function update($id, UpdateNewsCategoryData $dto)
    {
        return DB::transaction(function () use ($id, $dto) {
            $item = $this->repo->findOrFail($id);

            $dataToUpdate = $dto->toUpdateArray();
            if ($dataToUpdate['title']) {
                $dataToUpdate['code'] = Str::slug($dataToUpdate['title']);
            }

            if (array_key_exists('image', $dataToUpdate)) {
                if (!empty($item->image)) {
                    $this->imageService->delete($item->image);
                }
                if ($dataToUpdate['image'] instanceof UploadedFile) {
                    $dataToUpdate['image'] = $this->imageService->upload(
                        $dataToUpdate['image'],
                        self::UPLOAD_SUBDIR,
                        $dto->title
                    );
                }
            }

            $updated = $this->repo->update($item, $dataToUpdate);
            return $updated;
        });
    }


    public function delete($id)
    {
        $deleteItem = $this->repo->findOrFail($id);

        if (! empty($deleteItem->image)) {
            $this->imageService->delete($deleteItem->image);
        }

        return $this->repo->delete($deleteItem);
    }
}
