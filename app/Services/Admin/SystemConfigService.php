<?php

namespace App\Services\Admin;

use App\DTOs\Admin\UpdateSystemConfigData;
use App\Repositories\Admin\Interfaces\SystemConfigRepositoryInterface;
use App\Services\Admin\Interfaces\SystemConfigServiceInterface;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Illuminate\Http\UploadedFile;

class SystemConfigService implements SystemConfigServiceInterface
{
    protected $repo;

    protected const UPLOAD_SUBDIR = 'uploads/systemConfigs';

    public function __construct(SystemConfigRepositoryInterface $repo, ImageService $imageService)
    {
        $this->repo = $repo;
        $this->imageService = $imageService;
    }

    public function getAll()
    {
        return $this->repo->all();
    }

    public function getById($id)
    {
        return $this->repo->findOrFail($id);
    }

    public function update($id, UpdateSystemConfigData $dto)
    {
        return DB::transaction(function () use ($id, $dto) {
            $item = $this->repo->findOrFail($id);

            $dataToUpdate = $dto->toUpdateArray();
           if (array_key_exists('image', $dataToUpdate)) {
                if (!empty($item->image)) {
                    $this->imageService->delete($item->image);
                }
                if ($dataToUpdate['image'] instanceof UploadedFile) {
                    $dataToUpdate['image'] = $this->imageService->upload(
                        $dataToUpdate['image'],
                        self::UPLOAD_SUBDIR,
                        $dto->name
                    );
                }
            }
            $updated = $this->repo->update($item, $dataToUpdate);
            return $updated;
        });
    }

   
}
