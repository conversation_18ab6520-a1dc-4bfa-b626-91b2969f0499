<?php

namespace App\Services\Admin;

use App\DTOs\Admin\StoreTeamData;
use App\DTOs\Admin\StoreUserData;
use App\DTOs\Admin\UpdateTeamData;
use App\DTOs\Admin\UpdateUserData;
use App\Repositories\Admin\Interfaces\UserRepositoryInterface;
use App\Services\Admin\Interfaces\UserServiceInterface;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;

class UserService implements UserServiceInterface 
{
    protected $repo;

    public function __construct(UserRepositoryInterface $repo)
    {
        $this->repo = $repo;
    }

    public function getAll()
    {
        return $this->repo->all();
    }

    public function getById($id)
    {
        return $this->repo->findOrFail($id);
    }

    public function create(StoreUserData $dto)
    {
        return DB::transaction(function () use ($dto) {
            $data = $dto->toCreateArray();
            $item = $this->repo->create($data);
            return $item;
        });
    }

    public function update($id, UpdateUserData $dto)
    {
        return DB::transaction(function () use ($id, $dto) {
            $item = $this->repo->findOrFail($id);
            $dataToUpdate = $dto->toUpdateArray();
            $updated = $this->repo->update($item, $dataToUpdate);
            return $updated;
        });
    }


    public function delete($id)
    {
       $deleteItem = $this->repo->findOrFail($id);
       return $this->repo->delete($deleteItem);
    }
}
