<?php

namespace App\Services\Admin;

use Illuminate\Http\UploadedFile;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Log;
use RuntimeException;
use Exception;


class ImageService
{

    public function upload(UploadedFile $file, string $directory, ?string $filenamePrefix = null): string
    {
        $extension = $file->getClientOriginalExtension();
        $baseName = $filenamePrefix
            ? Str::slug($filenamePrefix) . '-' . Str::random(6)
            : Str::uuid()->toString();
        $filename = $baseName . '.' . $extension;

        $relativeDir = trim($directory, '/');
        $destinationPath = public_path($relativeDir);

        if (! File::exists($destinationPath)) {
            if (! File::makeDirectory($destinationPath, 0755, true)) {
                throw new RuntimeException("Không thể tạo thư mục: {$destinationPath}");
            }
        }

        try {
            $file->move($destinationPath, $filename);
        } catch (Exception $e) {
            throw new RuntimeException("Lỗi khi di chuyển file ảnh: " . $e->getMessage());
        }

        return $relativeDir . '/' . $filename;
    }

    public function delete(?string $relativePath): void
    {
        if (empty($relativePath)) {
            return;
        }

        $fullPath = public_path(ltrim($relativePath, '/'));

        if (File::exists($fullPath)) {
            try {
                File::delete($fullPath);
            } catch (Exception $e) {
                Log::error("Xóa file ảnh thất bại: {$fullPath}. Lỗi: " . $e->getMessage());
            }
        }
    }
}
