<?php

namespace App\Services\Admin;

use App\DTOs\Admin\StoreNewsArticleData;
use App\DTOs\Admin\UpdateNewsArticleData;
use App\Repositories\Admin\Interfaces\NewsArticleRepositoryInterface;
use App\Services\Admin\Interfaces\NewsArticleServiceInterface;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class NewsArticleService implements NewsArticleServiceInterface
{

    protected const UPLOAD_SUBDIR = 'uploads/news-articles';
    protected $repo;

    public function __construct(NewsArticleRepositoryInterface $repo, ImageService $imageService)
    {
        $this->repo = $repo;
        $this->imageService = $imageService;
    }

    public function getAll()
    {
        return $this->repo->all();
    }

    public function getById($id)
    {
        return $this->repo->findOrFail($id);
    }

    public function create(StoreNewsArticleData $dto)
    {
        return DB::transaction(function () use ($dto) {
            $data = $dto->toCreateArray();

            if ($data['title']) {
                $data['code'] = Str::slug($data['title']);
            }

            if (isset($data['image']) && $data['image'] instanceof UploadedFile) {
                $data['image'] = $this->imageService->upload(
                    $data['image'],
                    self::UPLOAD_SUBDIR,
                    $dto->title
                );
            }

            if (isset($data['seo_image']) && $data['seo_image'] instanceof UploadedFile) {
                $data['seo_image'] = $this->imageService->upload(
                    $data['seo_image'],
                    self::UPLOAD_SUBDIR,
                    $dto->title
                );
            }

            $item = $this->repo->create($data);
            $categoryIds = $dto->getCategoryIds();
            $item->categories()->sync($categoryIds);
            return $item;
        });
    }

    public function update($id, UpdateNewsArticleData $dto)
    {
        return DB::transaction(function () use ($id, $dto) {
            $item = $this->repo->findOrFail($id);

            $dataToUpdate = $dto->toUpdateArray();
            if ($dataToUpdate['title']) {
                $dataToUpdate['code'] = Str::slug($dataToUpdate['title']);
            }

            if (array_key_exists('image', $dataToUpdate)) {
                if (!empty($item->image)) {
                    $this->imageService->delete($item->image);
                }
                if ($dataToUpdate['image'] instanceof UploadedFile) {
                    $dataToUpdate['image'] = $this->imageService->upload(
                        $dataToUpdate['image'],
                        self::UPLOAD_SUBDIR,
                        $dto->title
                    );
                }
            }

            if (array_key_exists('seo_image', $dataToUpdate)) {
                if (!empty($item->seo_image)) {
                    $this->imageService->delete($item->seo_image);
                }
                if ($dataToUpdate['seo_image'] instanceof UploadedFile) {
                    $dataToUpdate['seo_image'] = $this->imageService->upload(
                        $dataToUpdate['seo_image'],
                        self::UPLOAD_SUBDIR,
                        $dto->title
                    );
                }
            }

            $updated = $this->repo->update($item, $dataToUpdate);
            $categoryIds = $dto->getCategoryIds();
            $updated->categories()->sync($categoryIds);
            return $updated;
        });
    }


    public function delete($id)
    {
        $deleteItem = $this->repo->findOrFail($id);

        if (! empty($deleteItem->image)) {
            $this->imageService->delete($deleteItem->image);
        }

        if (! empty($deleteItem->seo_image)) {
            $this->imageService->delete($deleteItem->seo_image);
        }

        return $this->repo->delete($deleteItem);
    }
}
