<?php

namespace App\Services\Admin;

use App\DTOs\Admin\StoreHostData;
use App\DTOs\Admin\UpdateHostData;
use App\Repositories\Admin\Interfaces\HostRepositoryInterface;
use App\Services\Admin\Interfaces\HostServiceInterface;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;

class HostService implements HostServiceInterface
{
    protected $repo;

    public function __construct(HostRepositoryInterface $repo)
    {
        $this->repo = $repo;
    }

    public function getAll()
    {
        return $this->repo->all();
    }

    public function getById($id)
    {
        return $this->repo->findOrFail($id);
    }

    public function create(StoreHostData $dto)
    {
        return DB::transaction(function () use ($dto) {
            $data = $dto->toCreateArray();
            
            $item = $this->repo->create($data);
            
            return $item;
        });
    }

    public function update($id, UpdateHostData $dto)
    {
        return DB::transaction(function () use ($id, $dto) {
            $item = $this->repo->findOrFail($id);

            $dataToUpdate = $dto->toUpdateArray();

            $updated = $this->repo->update($item, $dataToUpdate);
            return $updated;
        });
    }


    public function delete($id)
    {
        $deleteItem = $this->repo->findOrFail($id);
        return $this->repo->delete($deleteItem);
    }

}
