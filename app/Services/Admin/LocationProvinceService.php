<?php

namespace App\Services\Admin;

use App\DTOs\Admin\StoreLocationProvinceData;
use App\DTOs\Admin\UpdateLocationProvinceData;
use App\Repositories\Admin\LocationProvinceRepository;
use App\Services\Admin\Interfaces\LocationProvinceServiceInterface;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;

class LocationProvinceService implements LocationProvinceServiceInterface 
{
    protected $repo;

    public function __construct(LocationProvinceRepository $repo)
    {
        $this->repo = $repo;
    }

    public function getAll()
    {
        return $this->repo->all();
    }

    public function getById($id)
    {
        return $this->repo->findOrFail($id);
    }

    public function create(StoreLocationProvinceData $dto)
    {
        return DB::transaction(function () use ($dto) {
            $data = $dto->toCreateArray();
            $item = $this->repo->create($data);
            return $item;
        });
    }

    public function update($id, UpdateLocationProvinceData $dto)
    {
        return DB::transaction(function () use ($id, $dto) {
            $item = $this->repo->findOrFail($id);

            $dataToUpdate = $dto->toUpdateArray();

            $updated = $this->repo->update($item, $dataToUpdate);
            return $updated;
        });
    }


    public function delete($id)
    {
       $deleteItem = $this->repo->findOrFail($id);
       return $this->repo->delete($deleteItem);
    }
}
