<?php

namespace App\Services;

use App\DTOs\LoginData;
use App\DTOs\RegisterData;
use App\Helpers\ValidationHelper;
use App\Http\Resources\UserResource;
use App\Mail\VerifyEmail; 
use App\Services\External\Interfaces\ZaloServiceInterface;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use App\Models\User;
use App\Models\UserVerification;
use App\Repositories\Interfaces\UserRepositoryInterface;
use App\Repositories\Interfaces\UserVerificationRepositoryInterface;
use App\Repositories\Interfaces\PasswordResetTokenRepositoryInterface;
use App\Services\Interfaces\AuthServiceInterface;
use Illuminate\Validation\ValidationException;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Str;
use App\Mail\PasswordResetOTP;

class AuthService implements AuthServiceInterface
{
  protected UserRepositoryInterface $userRepo;
  protected UserVerificationRepositoryInterface $verificationRepo;
  protected PasswordResetTokenRepositoryInterface $passwordResetTokenRepository;
  protected ZaloServiceInterface $zaloService;
  protected JWTService $jwtService;
  protected TokenService $tokenService;

  public function __construct(
    UserRepositoryInterface $userRepo,
    UserVerificationRepositoryInterface $verificationRepo,
    PasswordResetTokenRepositoryInterface $passwordResetTokenRepository,
    ZaloServiceInterface $zaloService,
    JWTService $jwtService,
    TokenService $tokenService
  ) {
    $this->userRepo = $userRepo;
    $this->verificationRepo = $verificationRepo;
    $this->passwordResetTokenRepository = $passwordResetTokenRepository;
    $this->zaloService = $zaloService;
    $this->jwtService = $jwtService;
    $this->tokenService = $tokenService;
  }

  // ==========================================
  // AUTHENTICATION METHODS
  // ==========================================

  /**
   * Register user and return tokens
   */
  public function registerWithTokens(RegisterData $data, array $deviceInfo = []): array
  {
    $user = $this->register($data);

    return $this->createTokenResponse(
      $user,
      $deviceInfo['device_name'] ?? null,
      $deviceInfo['ip_address'] ?? null,
      $deviceInfo['user_agent'] ?? null
    );
  }

  /**
   * Register user and return response data with verification message
   */
  public function registerWithResponse(RegisterData $data): array
  {
    $user = $this->register($data);

    $identifier          = $data->identifier;
    $verificationMessage = 'Đăng ký thành công! ';

    $method = ValidationHelper::detectIdentifierType($identifier);

    if ($method === UserVerification::TYPE_EMAIL) {
      $verificationMessage .= 'Vui lòng kiểm tra email để xác thực tài khoản.';
    } elseif ($method === UserVerification::TYPE_PHONE) {
      $verificationMessage .= 'Mã OTP đã được gửi qua Zalo. Vui lòng xác thực tài khoản.';
    }

    return [
      'data' => [
        'status'     => 'pending_verification',
        'identifier' => $identifier,
        'method'     => $method,
        'user_id'    => $user->id,
      ],
      'message' => $verificationMessage
    ];
  }

  /**
   * Register a new user
   */
  public function register(RegisterData $data): User
  {
    DB::beginTransaction();

    try {
      $dataArr = $data->toArray();
      unset($dataArr['password_confirmation']);

      if ($dataArr['password'] !== $data->passwordConfirmation) {
        throw ValidationException::withMessages([
          'password_confirmation' => ['Mật khẩu xác nhận không khớp'],
        ]);
      }

      $user = $this->userRepo->create($dataArr);
      DB::commit();

      $this->sendVerifications($user);

      return $user;
    } catch (ValidationException $e) {
      DB::rollBack();
      throw $e;
    } catch (\Exception $e) {
      DB::rollBack();
      Log::error('User registration failed', [
        'error'      => $e->getMessage(),
        'trace'      => $e->getTraceAsString(),
        'identifier' => $data->identifier,
        'has_email'  => !empty($data->getEmail()),
        'has_phone'  => !empty($data->getPhone())
      ]);

      throw new \Exception('Đăng ký thất bại. Vui lòng thử lại sau.');
    }
  }

  /**
   * Send all required verifications for user
   */
  private function sendVerifications(User $user): void
  {
    if ($user->email) {
      $this->createAndSendEmailOTP($user);
    }

    if ($user->phone) {
      $this->createAndSendPhoneOTP($user);
    }
  }

  /**
   * Create and send email OTP
   */
  private function createAndSendEmailOTP(User $user): void
  {
    $otp        = UserVerification::generateOTP();
    $expiresAt  = now()->addMinutes(config('auth.email_verification.expire_minutes', 60));

    $this->userRepo->updateEmailVerificationOTP($user, $otp, $expiresAt);
    $this->sendEmailVerification($user, $otp);
  }

  /**
   * Create and send phone OTP
   */
  private function createAndSendPhoneOTP(User $user): void
  {
    $otp        = UserVerification::generateOTP();
    $expiresAt  = now()->addMinutes(config('auth.phone_verification.expire_minutes', 10));

    $this->userRepo->updatePhoneVerificationCode($user, $otp, $expiresAt);
    $this->sendPhoneVerification($user);
  }

  /**
   * Login user and return tokens
   */
  public function loginWithTokens(LoginData $data, array $deviceInfo = []): array
  {
    $result = $this->login($data);

    if ($result['status'] === 'pending_verification') {
      return [
        'data' => [
          'status'     => 'pending_verification',
          'identifier' => $data->identifier,
          'method'     => $result['method'],
        ],
        'message' => $result['message']
      ];
    }

    // Get authenticated user
    $user = Auth::guard('web')->user();
    if (!$user) {
      throw new \Exception('Thông tin đăng nhập không đúng');
    }

    return [
      'data' => $this->createTokenResponse(
        $user,
        $deviceInfo['device_name'] ?? null,
        $deviceInfo['ip_address'] ?? null,
        $deviceInfo['user_agent'] ?? null
      ),
      'message' => 'Đăng nhập thành công'
    ];
  }

  /**
   * Login user (basic authentication)
   */
  public function login(LoginData $data): array
  {
    try {
      $identifier       = $data->identifier;
      $plainPassword    = $data->password;
      $identifierType   = ValidationHelper::detectIdentifierType($identifier);

      // Find user by identifier
      $user = $this->userRepo->findByEmailOrPhone($identifier);

      if (!$user) {
        throw ValidationException::withMessages([
          'identifier' => ['Tài khoản không tồn tại.'],
        ]);
      }

      if (!$user->status) {
        throw ValidationException::withMessages([
          'identifier' => ['Tài khoản của bạn tạm thời đang bị khóa.'],
        ]);
      }

      if (!Hash::check($plainPassword, $user->password)) {
        throw ValidationException::withMessages([
          'password' => ['Mật khẩu đăng nhập không đúng.'],
        ]);
      }

      $needsVerification = false;
      if ($identifierType === UserVerification::TYPE_EMAIL) {
        $needsVerification = !$user->hasVerifiedEmail();
      } elseif ($identifierType === UserVerification::TYPE_PHONE) {
        $needsVerification = !$user->hasVerifiedPhone();
      }

      if ($needsVerification) {

        if ($identifierType === UserVerification::TYPE_EMAIL) {
          $this->createAndSendEmailOTP($user);
          $message = 'Tài khoản chưa được xác thực. Mã OTP đã được gửi qua email để xác thực.';
        } elseif ($identifierType === UserVerification::TYPE_PHONE) {
          $this->createAndSendPhoneOTP($user);
          $message = 'Tài khoản chưa được xác thực. Mã OTP đã được gửi qua Zalo để xác thực.';
        } else {
          $message = 'Tài khoản chưa được xác thực. Vui lòng xác thực để đăng nhập.';
        }

        return [
          'status'    => 'pending_verification',
          'user'      => $user,
          'method'    => $identifierType,
          'message'   => $message,
        ];
      }

      Auth::login($user);
      return [
        'status'  => 'success',
        'message' => 'Đăng nhập thành công',
        'user'    => $user,
        'token'   => $this->createTokenResponse($user),
      ];
    } catch (ValidationException $e) {
      throw $e;
    } catch (\Exception $e) {
      Log::error('User login failed', [
        'error'       => $e->getMessage(),
        'identifier'  => $data->identifier,
        'has_user'    => isset($user)
      ]);

      throw new \Exception('Đăng nhập thất bại. Vui lòng thử lại sau.');
    }
  }

  /**
   * Resend verification code
   */
  public function resendVerification(string $identifier): array
  {
    try {
      $user = $this->userRepo->findByEmailOrPhone($identifier);
      $method = ValidationHelper::detectIdentifierType($identifier);

      if (!$user) {
        throw new \Exception('Tài khoản không tồn tại');
      }

      if ($method === UserVerification::TYPE_EMAIL) {
        if (!$user->email) {
          throw new \Exception('Tài khoản này không có email');
        }

        $otp            = UserVerification::generateOTP();
        $expireMinutes  = config('auth.email_verification.expire_minutes', 60);
        $expiresAt      = now()->addMinutes($expireMinutes);
        $this->userRepo->updateEmailVerificationOTP($user, $otp, $expiresAt);
        $this->sendEmailVerification($user, $otp);

        return [
          'status'  => 'sent',
          'method'  => 'email',
          'message' => 'Mã xác thực email đã được gửi lại. Vui lòng kiểm tra email của bạn.'
        ];
      } elseif ($method === UserVerification::TYPE_PHONE) {
        if (!$user->phone) {
          throw new \Exception('Tài khoản này không có số điện thoại');
        }

        $otp            = UserVerification::generateOTP();
        $expireMinutes  = config('auth.phone_verification.expire_minutes', 10);
        $expiresAt      = now()->addMinutes($expireMinutes);

        $this->userRepo->updatePhoneVerificationCode($user, $otp, $expiresAt);
        $this->sendPhoneVerification($user);

        return [
          'status'    => 'sent',
          'method'    => 'phone',
          'message'   => 'Mã xác thực OTP đã được gửi qua Zalo.'
        ];
      } else {
        throw new \Exception('Loại xác thực không hợp lệ');
      }
    } catch (\Exception $e) {
      Log::error('Resend verification failed', [
        'error'         => $e->getMessage(),
        'trace'         => $e->getTraceAsString(),
        'identifier'    => $identifier,
        'method'        => $method
      ]);

      throw new \Exception('Gửi lại mã xác thực thất bại. Vui lòng thử lại sau.');
    }
  }

  /**
   * Legacy method for backward compatibility
   */
  public function resendVerificationCode(string $identifier): array
  {
    try {
      $user = $this->userRepo->findByEmailOrPhone($identifier);

      if (!$user) {
        throw ValidationException::withMessages([
          'identifier' => ['Tài khoản không tồn tại.'],
        ]);
      }

      $method = null;
      if (filter_var($identifier, FILTER_VALIDATE_EMAIL)) {
        if (!$user->email_verified_at) {
          $method = 'email';
        }
      } else {
        if (!$user->phone_verified_at) {
          $method = 'phone';
          $value = $identifier;
        }
      }

      if (!$method) {
        return ['status' => 'already_verified', 'message' => 'Tài khoản này đã được xác thực.'];
      }

      if ($method === 'email') {
        $otp = UserVerification::generateOTP();
        $expireMinutes = config('auth.email_verification.expire_minutes', 60);
        $expiresAt = now()->addMinutes($expireMinutes);
        $this->userRepo->updateEmailVerificationOTP($user, $otp, $expiresAt);
        $this->sendEmailVerification($user, $otp);
        return ['status' => 'sent', 'method' => 'email', 'message' => 'Mã xác thực email đã được gửi lại. Vui lòng kiểm tra email của bạn.'];
      } else {
        $otp = \App\Models\UserVerification::generateOTP();
        $expireMinutes = config('auth.phone_verification.expire_minutes', 10);
        $expiresAt = now()->addMinutes($expireMinutes);
        $this->userRepo->updatePhoneVerificationCode($user, $otp, $expiresAt);

        // Gửi OTP qua Zalo ZNS
        $this->sendPhoneVerification($user);

        return ['status' => 'sent', 'method' => 'phone', 'value' => $value, 'message' => 'Mã xác thực OTP đã được gửi qua Zalo.'];
      }
    } catch (ValidationException $e) {
      throw $e;
    } catch (\Exception $e) {
      Log::error('Resend verification failed', [
        'error' => $e->getMessage(),
        'trace' => $e->getTraceAsString(),
        'identifier' => $identifier
      ]);

      throw new \Exception('Gửi lại mã xác thực thất bại. Vui lòng thử lại sau.');
    }
  }

  // ==========================================
  // TOKEN MANAGEMENT METHODS
  // ==========================================

  /**
   * Logout current user 
   */
  public function logout(): void
  {
    try {
      $jti = $this->jwtService->getCurrentJti();

      DB::transaction(function () use ($jti) {
        if ($jti) {
          $this->tokenService->revokeByJti($jti);
        }

        $this->jwtService->invalidateToken();
      });

      Auth::logout();
    } catch (\Exception $e) {
      Log::error('Logout failed', [
        'error' => $e->getMessage(),
        'trace' => $e->getTraceAsString(),
        'jti'   => $jti ?? 'unknown'
      ]);
      throw new \Exception('Đăng xuất thất bại');
    }
  }

  /**
   * Logout from all devices (Optimized with DB transaction)
   */
  public function logoutAll(User $user): void
  {
    try {
      // Parallel execution trong transaction
      DB::transaction(function () use ($user) {
        // 1. Xóa tất cả refresh tokens của user
        $this->tokenService->revokeAllForUser($user->id);

        // 2. Invalidate JWT token hiện tại
        $this->jwtService->invalidateToken();
      });
    } catch (\Exception $e) {
      Log::error('Logout all failed', [
        'user_id' => $user->id,
        'error'   => $e->getMessage(),
        'trace'   => $e->getTraceAsString()
      ]);
      throw new \Exception('Đăng xuất khỏi tất cả thiết bị thất bại');
    }
  }

  /**
   * Refresh JWT token
   */
  public function refreshToken(string $refreshToken): array
  {
    // Verify refresh token
    $tokenModel = $this->tokenService->verifyRefreshToken($refreshToken);
    if (!$tokenModel) {
      throw new ValidationException('Refresh token không hợp lệ hoặc đã hết hạn');
    }

    // Update last used & Generate new JWT - optimize DB calls
    DB::transaction(function () use ($tokenModel) {
      $this->tokenService->updateLastUsed($tokenModel);

      $jwtData = $this->jwtService->generateToken($tokenModel->user);
      $this->tokenService->updateJti($tokenModel, $jwtData['jti']);

      return $jwtData;
    });

    $user = $tokenModel->user()->select(['id', 'fullname', 'email', 'phone'])->first();
    $jwtData = $this->jwtService->generateToken($user);

    return [
      'user'          => new UserResource($user),
      'access_token'  => $jwtData['token'],
      'refresh_token' => $refreshToken,
      'token_type'    => 'bearer',
      'expires_in'    => $this->jwtService->getTokenTtl(),
    ];
  }

  // ==========================================
  // USER PROFILE MANAGEMENT
  // ==========================================

  /**
   * Get user profile
   */
  public function getUserProfile(User $user): array
  {
    return (new UserResource($user))->toArray(request());
  }

  /**
   * Update user profile
   */
  public function updateUserProfile(User $user, array $data): array
  {
    DB::beginTransaction();
    try {
      $needEmailVerification = isset($data['email']) && $data['email'] !== $user->email;
      $needPhoneVerification = isset($data['phone']) && $data['phone'] !== $user->phone;

      // Update thông tin user (avatar đã được xử lý trong DTO)
      $updatedUser = $this->userRepo->update($user, $data);

      // Nếu không cần verify gì
      if (!$needEmailVerification && !$needPhoneVerification) {
        DB::commit();
        return $this->makeProfileResponse($updatedUser);
      }

      // Ưu tiên verify phone nếu có thay đổi phone
      $verificationType = $needPhoneVerification ? 'phone' : 'email';

      // Gửi OTP
      if ($verificationType === 'phone') {
        $this->createAndSendPhoneOTP($updatedUser);
      } else {
        $this->createAndSendEmailOTP($updatedUser);
      }

      DB::commit();
      return $this->makeProfileResponse($updatedUser, $verificationType);
    } catch (\Exception $e) {
      DB::rollback();
      Log::error('Update profile failed', [
        'user_id' => $user->id,
        'data' => $data,
        'error' => $e->getMessage()
      ]);
      throw new \Exception('Cập nhật thông tin thất bại');
    }
  }

  private function makeProfileResponse(User $user, ?string $verificationType = null): array
  {
    $response = [
      'data' => [
        'user' => (new UserResource($user))->toArray(request())
      ]
    ];

    if ($verificationType) {
      $response['data']['verification'] = [
        'type' => $verificationType,
        'identifier' => $verificationType === 'phone' ? $user->phone : $user->email
      ];
      $response['message'] = $verificationType === 'phone'
        ? 'Cập nhật thành công. Vui lòng xác thực số điện thoại mới của bạn qua Zalo.'
        : 'Cập nhật thành công. Vui lòng xác thực email mới của bạn.';
    } else {
      $response['message'] = 'Cập nhật thông tin thành công';
    }

    return $response;
  }

  /**
   * Change user password
   */
  public function changePassword(User $user, string $currentPassword, string $newPassword): void
  {
    // Kiểm tra mật khẩu hiện tại
    if (!Hash::check($currentPassword, $user->password)) {
      throw ValidationException::withMessages([
        'current_password' => ['Mật khẩu hiện tại không đúng'],
      ]);
    }

    // Kiểm tra mật khẩu mới trùng cũ
    if (Hash::check($newPassword, $user->password)) {
      throw ValidationException::withMessages([
        'password' => ['Mật khẩu mới không được trùng với mật khẩu hiện tại'],
      ]);
    }

    // Kiểm tra độ mạnh mật khẩu
    if (!ValidationHelper::validatePasswordStrength($newPassword)) {
      throw ValidationException::withMessages([
        'password' => ['Mật khẩu mới không đủ mạnh'],
      ]);
    }

    $user->password = Hash::make($newPassword);
    $user->save();
    Log::info('Password changed successfully', ['user_id' => $user->id]);
  }

  // ==========================================
  // VERIFICATION METHODS
  // ==========================================

  public function sendEmailVerification(User $user, string $otp): void
  {
    try {
      Log::info('Sending email verification', [
        'user_id' => $user->id,
        'email' => $user->email,
        'otp' => $otp
      ]);

      Mail::to($user->email)->send(new VerifyEmail($otp, $user->name));

      Log::info('Email verification sent successfully', [
        'user_id' => $user->id,
        'email' => $user->email
      ]);
    } catch (\Exception $e) {
      Log::error('Failed to send email verification', [
        'user_id' => $user->id,
        'email' => $user->email,
        'error' => $e->getMessage(),
        'trace' => $e->getTraceAsString()
      ]);
      throw $e;
    }
  }

  public function verify(string $identifier, string $otp): array
  {
    $type = ValidationHelper::detectIdentifierType($identifier);
    if ($type === 'email') {
      return $this->verifyEmail($otp);
    } else {
      return $this->verifyPhone($identifier, $otp);
    }
  }

  public function verifyEmail(string $otp): array
  {
    try {
      $user = $this->userRepo->findUserByOTP($otp, UserVerification::TYPE_EMAIL);
      if (!$user) {
        throw ValidationException::withMessages([
          'otp' => ['Mã OTP không hợp lệ hoặc đã hết hạn'],
        ]);
      }

      $this->userRepo->markAsVerified($user, UserVerification::TYPE_EMAIL);

      return $this->createTokenResponse($user);
    } catch (ValidationException $e) {
      throw $e;
    } catch (\Exception $e) {
      Log::error('Email verification failed', [
        'otp'   => $otp,
        'error' => $e->getMessage()
      ]);
      throw new \Exception('Xác thực email thất bại');
    }
  }

  public function sendPhoneVerification(User $user): void
  {
    $verification = $user->phoneVerification()->unverified()->first();
    if (!$verification || !$user->phone) {
      return;
    }

    // Gửi OTP qua Zalo ZNS
    $this->sendOTPViaZalo($user, $verification->token);
  }

  private function generateTrackingId(User $user, string $type = 'otp'): string
  {
    return "{$type}_{$user->id}_" . time() . '_' . substr(md5(uniqid()), 0, 6);
  }

  public function sendOTPViaZalo(User $user, string $otp): bool
  {
    try {
      $trackingId = $this->generateTrackingId($user, 'zalo_otp');

      $result = $this->zaloService->sendOTP([
        'phone'       => $user->phone,
        'otp'         => $otp,
        'tracking_id' => $trackingId,
        'template'    => config('services.zalo.otp_template', 'default')
      ]);

      if ($result['success']) {
        Log::info('Zalo OTP sent successfully', [
          'user_id'     => $user->id,
          'phone'       => $user->phone,
          'tracking_id' => $trackingId
        ]);
        return true;
      }

      Log::error('Zalo OTP sending failed', [
        'user_id' => $user->id,
        'phone'   => $user->phone,
        'error'   => $result['message'] ?? 'Unknown error'
      ]);
      return false;
    } catch (\Exception $e) {
      Log::error('Zalo OTP sending exception', [
        'user_id' => $user->id,
        'phone'   => $user->phone,
        'error'   => $e->getMessage()
      ]);
      return false;
    }
  }

  public function verifyPhone(string $phone, string $otp): array
  {
    try {
      $user = $this->userRepo->findUserByOTP($otp, UserVerification::TYPE_PHONE);

      $this->userRepo->markAsVerified($user, UserVerification::TYPE_PHONE);

      return $this->createTokenResponse($user);
    } catch (\Exception $e) {
      Log::error('Phone verification failed', [
        'phone' => $phone,
        'otp'   => $otp,
        'error' => $e->getMessage()
      ]);
      throw new \Exception('Xác thực số điện thoại thất bại');
    }
  }

  // ==========================================
  // TOKEN CREATION
  // ==========================================

  /**
   * Create complete token response with JWT and refresh token
   */
  public function createTokenResponse(User $user, ?string $deviceName = null, ?string $ipAddress = null, ?string $userAgent = null): array
  {
    // Generate JWT token
    $jwtData = $this->jwtService->generateToken($user);

    // Create refresh token
    $refreshTokenData = $this->tokenService->createRefreshToken(
      $user,
      $jwtData['jti'],
      $deviceName,
      $ipAddress,
      $userAgent
    );

    return [
      'user'             => new UserResource($user),
      'access_token'     => $jwtData['token'],
      'refresh_token'    => $refreshTokenData['token'],
      'token_type'       => 'bearer',
      'expires_in'       => $this->jwtService->getTokenTtl(),
      'email_verified'   => !is_null($user->email_verified_at),
      'phone_verified'   => !is_null($user->phone_verified_at),
      'requires_verification' => is_null($user->email_verified_at) || is_null($user->phone_verified_at),
    ];
  }

  /**
   * Gửi OTP để reset password
   */
  public function forgotPassword(string $identifier): array
  {
    try {
      // Tìm user theo identifier
      $user = $this->userRepo->findByIdentifier($identifier);

      if (!$user) {
        return [
          'success' => false,
          'message' => 'Không tìm thấy tài khoản với thông tin này'
        ];
      }

      // Tạo OTP và lưu vào cache
      $otp = $this->generateOTP();
      $cache_key = "password_reset_otp:{$identifier}";

      Cache::put($cache_key, $otp, now()->addMinutes(10));

      // Gửi OTP qua email
      $this->sendPasswordResetOTP($user, $otp);

      return [
        'success' => true,
        'message' => 'Mã OTP đã được gửi đến email của bạn'
      ];
    } catch (\Exception $e) {
      Log::error('Forgot password error: ' . $e->getMessage());

      return [
        'success' => false,
        'message' => 'Có lỗi xảy ra, vui lòng thử lại sau'
      ];
    }
  }

  /**
   * Verify OTP và trả về reset token
   */
  public function verifyOtpForPasswordReset(string $identifier, string $otp): array
  {
    try {
      // Tìm user theo identifier
      $user = $this->userRepo->findByIdentifier($identifier);

      if (!$user) {
        return [
          'success' => false,
          'message' => 'Không tìm thấy tài khoản với thông tin này'
        ];
      }

      // Verify OTP
      $cache_key = "password_reset_otp:{$identifier}";
      $cached_otp = Cache::get($cache_key);

      if (!$cached_otp || $cached_otp !== $otp) {
        return [
          'success' => false,
          'message' => 'Mã OTP không đúng hoặc đã hết hạn'
        ];
      }

      // Xóa OTP khỏi cache
      Cache::forget($cache_key);

      // Tạo reset token
      $token = $this->generateResetToken();

      // Lưu token vào database
      $this->passwordResetTokenRepository->create([
        'email'      => $user->email,
        'token'      => $token,
        'expires_at' => now()->addMinutes(60),
      ]);

      return [
        'success' => true,
        'message' => 'Xác thực OTP thành công',
        'data'    => [
          'reset_token' => $token,
          'expires_in'  => 3600, // 60 minutes
        ]
      ];
    } catch (\Exception $e) {
      Log::error('Verify OTP error: ' . $e->getMessage());

      return [
        'success' => false,
        'message' => 'Có lỗi xảy ra, vui lòng thử lại sau'
      ];
    }
  }

  /**
   * Reset password với token
   */
  public function resetPassword(string $token, string $newPassword): array
  {
    try {
      // Tìm token trong database
      $reset_record = $this->passwordResetTokenRepository->findByToken($token);

      if (!$reset_record) {
        return [
          'success' => false,
          'message' => 'Token không hợp lệ'
        ];
      }

      // Tìm user theo email
      $user = $this->userRepo->findByEmail($reset_record->email);

      if (!$user) {
        return [
          'success' => false,
          'message' => 'Không tìm thấy tài khoản'
        ];
      }

      // Update password
      $this->userRepo->updatePassword($user, $newPassword);

      // Xóa tất cả reset tokens của user này
      $this->passwordResetTokenRepository->deleteByEmail($reset_record->email);

      // Log password reset
      Log::info("Password reset successful for user: {$user->email}");

      return [
        'success' => true,
        'message' => 'Đặt lại mật khẩu thành công'
      ];
    } catch (\Exception $e) {
      Log::error('Reset password error: ' . $e->getMessage());

      return [
        'success' => false,
        'message' => 'Có lỗi xảy ra, vui lòng thử lại sau'
      ];
    }
  }

  /**
   * Gửi OTP qua email để reset password
   */
  private function sendPasswordResetOTP($user, string $otp): void
  {
    try {
      Mail::to($user->email)->send(new PasswordResetOTP($user, $otp));

      Log::info("Password reset OTP sent to: {$user->email}");
    } catch (\Exception $e) {
      Log::error("Failed to send password reset OTP to {$user->email}: " . $e->getMessage());
      throw $e;
    }
  }

  /**
   * Tạo OTP 6 chữ số
   */
  private function generateOTP(): string
  {
    return str_pad(random_int(0, 999999), 6, '0', STR_PAD_LEFT);
  }

  /**
   * Tạo reset token
   */
  private function generateResetToken(): string
  {
    return Str::random(64);
  }
}
