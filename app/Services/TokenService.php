<?php

namespace App\Services;

use App\Models\User;
use App\Models\UserToken;
use Carbon\Carbon;
use Illuminate\Support\Facades\Hash;

class TokenService
{
    /**
     * Create a new refresh token for user
     */
    public function createRefreshToken(
        User $user,
        string $jti,
        ?string $deviceName = null,
        ?string $ipAddress = null,
        ?string $userAgent = null
    ): array {
        // Generate random token
        $plainToken = bin2hex(random_bytes(32));
        
        $refreshToken = UserToken::create([
            'user_id'     => $user->id,
            'token_type'  => 'refresh_token',
            'token'       => Hash::make($plainToken),
            'jti'         => $jti,
            'expires_at'  => Carbon::now()->addMinutes(config('jwt.refresh_ttl', 20160)), // 2 weeks default
            'device_name' => $deviceName,
            'ip_address'  => $ipAddress,
            'user_agent'  => $userAgent,
        ]);

        return [
            'model' => $refreshToken,
            'token' => $refreshToken->id . '|' . $plainToken,
        ];
    }

    /**
     * Verify and get refresh token
     */
    public function verifyRefreshToken(string $refreshToken): ?UserToken
    {
        // Parse refresh token (format: id|token)
        $parts = explode('|', $refreshToken, 2);
        if (count($parts) !== 2) {
            return null;
        }

        [$tokenId, $plainToken] = $parts;

        // Find refresh token in database
        $tokenModel = UserToken::find($tokenId);
        if (!$tokenModel || !Hash::check($plainToken, $tokenModel->token)) {
            return null;
        }

        // Check if token is expired
        if ($tokenModel->isExpired()) {
            $tokenModel->delete();
            return null;
        }

        return $tokenModel;
    }

    /**
     * Update token last used timestamp
     */
    public function updateLastUsed(UserToken $token): void
    {
        $token->update(['last_used_at' => now()]);
    }

    /**
     * Update token JTI
     */
    public function updateJti(UserToken $token, string $newJti): void
    {
        $token->update(['jti' => $newJti]);
    }

    /**
     * Revoke token by JTI
     */
    public function revokeByJti(string $jti): int
    {
        return UserToken::where('jti', $jti)->delete();
    }

    /**
     * Revoke all tokens for user
     */
    public function revokeAllForUser(int $userId): int
    {
        return UserToken::where('user_id', $userId)->delete();
    }

    /**
     * Revoke all tokens except current
     */
    public function revokeAllExceptCurrent(int $userId, string $currentJti): int
    {
        return UserToken::where('user_id', $userId)
                       ->where('jti', '!=', $currentJti)
                       ->delete();
    }

    /**
     * Get active tokens for user
     */
    public function getActiveTokensForUser(int $userId)
    {
        return UserToken::where('user_id', $userId)
                       ->where('token_type', 'refresh_token')
                       ->where('expires_at', '>', now())
                       ->orderBy('last_used_at', 'desc')
                       ->get();
    }

    /**
     * Revoke specific token for user
     */
    public function revokeTokenForUser(int $userId, int $tokenId): bool
    {
        $token = UserToken::where('user_id', $userId)
                         ->where('id', $tokenId)
                         ->first();

        if (!$token) {
            return false;
        }

        $token->delete();
        return true;
    }

    /**
     * Clean up expired tokens
     */
    public function cleanupExpired(): int
    {
        return UserToken::where('expires_at', '<', now())->delete();
    }
}
