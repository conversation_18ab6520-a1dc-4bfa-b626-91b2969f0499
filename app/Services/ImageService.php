  <?php

  namespace App\Services;

  use App\Services\Interfaces\ImageServiceInterface;
  use Illuminate\Http\UploadedFile;
  use Illuminate\Support\Str;
  use Illuminate\Support\Facades\File;
  use Illuminate\Support\Facades\Log;
  use Illuminate\Support\Facades\Storage;
  use Intervention\Image\Facades\Image;
  use RuntimeException;
  use Exception;

  class ImageService implements ImageServiceInterface
  {
    /**
     * Default image quality for optimization
     */
    private const DEFAULT_QUALITY = 85;

    /**
     * Default max width for resizing
     */
    private const DEFAULT_MAX_WIDTH = 1920;

    /**
     * Default max height for resizing
     */
    private const DEFAULT_MAX_HEIGHT = 1080;

    /**
     * Allowed image extensions
     */
    private const ALLOWED_EXTENSIONS = ['jpg', 'jpeg', 'png', 'gif', 'webp', 'svg'];

    /**
     * Upload and process image
     *
     * @param UploadedFile $file
     * @param string $directory
     * @param string|null $filenamePrefix
     * @param array $options
     * @return string
     * @throws RuntimeException
     */
    public function upload(
      UploadedFile $file,
      string $directory,
      ?string $filenamePrefix = null,
      array $options = []
    ): string {
      $this->validateFile($file);

      $extension = $this->getFileExtension($file);
      $filename = $this->generateFilename($filenamePrefix, $extension);
      $relativeDir = trim($directory, '/');
      $destinationPath = public_path($relativeDir);

      // Tạo thư mục nếu chưa có
      $this->ensureDirectoryExists($destinationPath);

      try {
        // Xử lý resize nếu cần
        if ($this->shouldResize($options)) {
          $this->processAndSaveImage($file, $destinationPath, $filename, $options);
        } else {
          // Upload file gốc
          $file->move($destinationPath, $filename);
        }

        return $relativeDir . '/' . $filename;
      } catch (Exception $e) {
        Log::error('Image upload failed', [
          'file' => $file->getClientOriginalName(),
          'directory' => $directory,
          'error' => $e->getMessage()
        ]);
        throw new RuntimeException("Lỗi khi upload ảnh: " . $e->getMessage());
      }
    }

    /**
     * Upload multiple images
     *
     * @param array $files
     * @param string $directory
     * @param string|null $filenamePrefix
     * @param array $options
     * @return array
     */
    public function uploadMultiple(
      array $files,
      string $directory,
      ?string $filenamePrefix = null,
      array $options = []
    ): array {
      $uploadedPaths = [];

      foreach ($files as $file) {
        if ($file instanceof UploadedFile) {
          $uploadedPaths[] = $this->upload($file, $directory, $filenamePrefix, $options);
        }
      }

      return $uploadedPaths;
    }

    /**
     * Resize existing image
     *
     * @param string $imagePath
     * @param int $width
     * @param int $height
     * @param string|null $outputPath
     * @return string
     */
    public function resize(string $imagePath, int $width, int $height, ?string $outputPath = null): string
    {
      $fullPath = $this->getFullPath($imagePath);

      if (!File::exists($fullPath)) {
        throw new RuntimeException("File không tồn tại: {$imagePath}");
      }

      $outputPath = $outputPath ?: $this->generateResizedPath($imagePath, $width, $height);
      $outputFullPath = public_path($outputPath);

      try {
        $image = Image::make($fullPath);
        $image->resize($width, $height, function ($constraint) {
          $constraint->aspectRatio();
          $constraint->upsize();
        });

        $this->ensureDirectoryExists(dirname($outputFullPath));
        $image->save($outputFullPath, self::DEFAULT_QUALITY);

        return $outputPath;
      } catch (Exception $e) {
        Log::error('Image resize failed', [
          'original' => $imagePath,
          'output' => $outputPath,
          'error' => $e->getMessage()
        ]);
        throw new RuntimeException("Lỗi khi resize ảnh: " . $e->getMessage());
      }
    }

    /**
     * Create thumbnail from image
     *
     * @param string $imagePath
     * @param int $width
     * @param int $height
     * @return string
     */
    public function createThumbnail(string $imagePath, int $width = 300, int $height = 300): string
    {
      $thumbnailPath = $this->generateThumbnailPath($imagePath);
      return $this->resize($imagePath, $width, $height, $thumbnailPath);
    }

    /**
     * Optimize image quality and size
     *
     * @param string $imagePath
     * @param int $quality
     * @return string
     */
    public function optimize(string $imagePath, int $quality = self::DEFAULT_QUALITY): string
    {
      $fullPath = $this->getFullPath($imagePath);

      if (!File::exists($fullPath)) {
        throw new RuntimeException("File không tồn tại: {$imagePath}");
      }

      try {
        $image = Image::make($fullPath);
        $image->save($fullPath, $quality);

        return $imagePath;
      } catch (Exception $e) {
        Log::error('Image optimization failed', [
          'path' => $imagePath,
          'error' => $e->getMessage()
        ]);
        throw new RuntimeException("Lỗi khi optimize ảnh: " . $e->getMessage());
      }
    }

    /**
     * Delete image file
     *
     * @param string|null $relativePath
     * @return bool
     */
    public function delete(?string $relativePath): bool
    {
      if (empty($relativePath)) {
        return true;
      }

      $fullPath = $this->getFullPath($relativePath);

      if (File::exists($fullPath)) {
        try {
          File::delete($fullPath);

          // Xóa thumbnail nếu có
          $this->deleteThumbnail($relativePath);

          Log::info('Image deleted successfully', ['path' => $relativePath]);
          return true;
        } catch (Exception $e) {
          Log::error('Image deletion failed', [
            'path' => $relativePath,
            'error' => $e->getMessage()
          ]);
          return false;
        }
      }

      return true;
    }

    /**
     * Delete multiple images
     *
     * @param array $paths
     * @return array
     */
    public function deleteMultiple(array $paths): array
    {
      $results = [];

      foreach ($paths as $path) {
        $results[$path] = $this->delete($path);
      }

      return $results;
    }

    /**
     * Get image information
     *
     * @param string $imagePath
     * @return array
     */
    public function getImageInfo(string $imagePath): array
    {
      $fullPath = $this->getFullPath($imagePath);

      if (!File::exists($fullPath)) {
        throw new RuntimeException("File không tồn tại: {$imagePath}");
      }

      try {
        $image = Image::make($fullPath);
        $fileSize = File::size($fullPath);

        return [
          'width' => $image->width(),
          'height' => $image->height(),
          'size' => $this->formatFileSize($fileSize),
          'size_bytes' => $fileSize,
          'mime_type' => $image->mime(),
          'extension' => $image->extension,
          'filename' => basename($imagePath),
          'path' => $imagePath,
          'url' => asset($imagePath)
        ];
      } catch (Exception $e) {
        Log::error('Failed to get image info', [
          'path' => $imagePath,
          'error' => $e->getMessage()
        ]);
        throw new RuntimeException("Lỗi khi lấy thông tin ảnh: " . $e->getMessage());
      }
    }

    /**
     * Check if file is valid image
     *
     * @param UploadedFile $file
     * @return bool
     */
    public function isValidImage(UploadedFile $file): bool
    {
      return $file->isValid() &&
        in_array(strtolower($file->getClientOriginalExtension()), self::ALLOWED_EXTENSIONS) &&
        $file->getMimeType() &&
        str_starts_with($file->getMimeType(), 'image/');
    }

    /**
     * Get image URL
     *
     * @param string|null $imagePath
     * @param string $defaultImage
     * @return string
     */
    public function getImageUrl(?string $imagePath, string $defaultImage = 'images/no-image.jpg'): string
    {
      if (empty($imagePath)) {
        return asset($defaultImage);
      }

      $fullPath = $this->getFullPath($imagePath);
      return File::exists($fullPath) ? asset($imagePath) : asset($defaultImage);
    }

    /**
     * Validate uploaded file
     *
     * @param UploadedFile $file
     * @throws RuntimeException
     */
    private function validateFile(UploadedFile $file): void
    {
      if (!$file->isValid()) {
        throw new RuntimeException('File upload không hợp lệ');
      }

      if (!$this->isValidImage($file)) {
        throw new RuntimeException('File không phải là ảnh hợp lệ');
      }
    }

    /**
     * Get file extension
     *
     * @param UploadedFile $file
     * @return string
     */
    private function getFileExtension(UploadedFile $file): string
    {
      $extension = strtolower($file->getClientOriginalExtension());

      // Normalize jpeg to jpg
      return $extension === 'jpeg' ? 'jpg' : $extension;
    }

    /**
     * Generate unique filename
     *
     * @param string|null $prefix
     * @param string $extension
     * @return string
     */
    private function generateFilename(?string $prefix, string $extension): string
    {
      $baseName = $prefix
        ? Str::slug($prefix) . '-' . Str::random(6)
        : Str::uuid()->toString();

      return $baseName . '.' . $extension;
    }

    /**
     * Ensure directory exists
     *
     * @param string $path
     * @throws RuntimeException
     */
    private function ensureDirectoryExists(string $path): void
    {
      if (!File::exists($path)) {
        if (!File::makeDirectory($path, 0755, true)) {
          throw new RuntimeException("Không thể tạo thư mục: {$path}");
        }
      }
    }

    /**
     * Check if should resize image
     *
     * @param array $options
     * @return bool
     */
    private function shouldResize(array $options): bool
    {
      return isset($options['resize']) && $options['resize'] === true;
    }

    /**
     * Process and save image with resize
     *
     * @param UploadedFile $file
     * @param string $destinationPath
     * @param string $filename
     * @param array $options
     */
    private function processAndSaveImage(
      UploadedFile $file,
      string $destinationPath,
      string $filename,
      array $options
    ): void {
      $image = Image::make($file->getRealPath());

      $maxWidth = $options['max_width'] ?? self::DEFAULT_MAX_WIDTH;
      $maxHeight = $options['max_height'] ?? self::DEFAULT_MAX_HEIGHT;
      $quality = $options['quality'] ?? self::DEFAULT_QUALITY;

      // Resize if image is larger than max dimensions
      if ($image->width() > $maxWidth || $image->height() > $maxHeight) {
        $image->resize($maxWidth, $maxHeight, function ($constraint) {
          $constraint->aspectRatio();
          $constraint->upsize();
        });
      }

      $image->save($destinationPath . '/' . $filename, $quality);
    }

    /**
     * Get full path from relative path
     *
     * @param string $relativePath
     * @return string
     */
    private function getFullPath(string $relativePath): string
    {
      return public_path(ltrim($relativePath, '/'));
    }

    /**
     * Generate resized image path
     *
     * @param string $originalPath
     * @param int $width
     * @param int $height
     * @return string
     */
    private function generateResizedPath(string $originalPath, int $width, int $height): string
    {
      $pathInfo = pathinfo($originalPath);
      return $pathInfo['dirname'] . '/' . $pathInfo['filename'] . "_{$width}x{$height}." . $pathInfo['extension'];
    }

    /**
     * Generate thumbnail path
     *
     * @param string $originalPath
     * @return string
     */
    private function generateThumbnailPath(string $originalPath): string
    {
      $pathInfo = pathinfo($originalPath);
      return $pathInfo['dirname'] . '/thumbnails/' . $pathInfo['basename'];
    }

    /**
     * Delete thumbnail if exists
     *
     * @param string $originalPath
     */
    private function deleteThumbnail(string $originalPath): void
    {
      $thumbnailPath = $this->generateThumbnailPath($originalPath);
      $thumbnailFullPath = $this->getFullPath($thumbnailPath);

      if (File::exists($thumbnailFullPath)) {
        File::delete($thumbnailFullPath);
      }
    }

    /**
     * Format file size
     *
     * @param int $bytes
     * @return string
     */
    private function formatFileSize(int $bytes): string
    {
      $units = ['B', 'KB', 'MB', 'GB'];
      $bytes = max($bytes, 0);
      $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
      $pow = min($pow, count($units) - 1);

      $bytes /= pow(1024, $pow);

      return round($bytes, 2) . ' ' . $units[$pow];
    }
  }
