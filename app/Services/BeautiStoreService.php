<?php
namespace App\Services;

use App\Models\BeautiType;
use App\Repositories\Interfaces\BeautiStoreRepositoryInterface;
use App\Repositories\Interfaces\BeautiStoreServiceRepositoryInterface;
use App\Services\Interfaces\BeautiStoreServiceInterface;
use Illuminate\Database\Eloquent\Collection;

class BeautiStoreService implements BeautiStoreServiceInterface
{
      protected BeautiStoreRepositoryInterface $storeRepo;
    protected BeautiStoreServiceRepositoryInterface $storeServiceRepo;

     public function __construct(
        BeautiStoreRepositoryInterface $storeRepo,
        BeautiStoreServiceRepositoryInterface $storeServiceRepo
    ) {
        $this->storeRepo = $storeRepo;
        $this->storeServiceRepo = $storeServiceRepo;
    }


 

    public function getStoresByTypeAndProvince($typeSlug, $provinceCode) :Collection
    {
        return $this->storeRepo->getStoresByTypeAndProvince($typeSlug, $provinceCode);
    }

     public function getStoreServices(int $storeId): Collection
    {
        return $this->storeServiceRepo->getServicesByStore($storeId);
    }
}
