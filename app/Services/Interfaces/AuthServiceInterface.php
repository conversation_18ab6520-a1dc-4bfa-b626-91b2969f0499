<?php

namespace App\Services\Interfaces;

use App\DTOs\LoginData;
use App\DTOs\RegisterData;
use App\Models\User;

interface AuthServiceInterface
{
  // Authentication
  public function register(RegisterData $data): User;
  public function registerWithTokens(RegisterData $data, array $deviceInfo = []): array;
  public function registerWithResponse(RegisterData $data): array;
  public function login(LoginData $data): array;
  public function loginWithTokens(LoginData $data, array $deviceInfo = []): array;

  // Token Management
  public function logout(): void;
  public function logoutAll(User $user): void;
  public function refreshToken(string $refreshToken): array;
  public function createTokenResponse(User $user, ?string $deviceName = null, ?string $ipAddress = null, ?string $userAgent = null): array;

  // Profile Management
  public function getUserProfile(User $user): array;
  public function updateUserProfile(User $user, array $data): array;
  public function changePassword(User $user, string $currentPassword, string $newPassword): void;

  // Verification
  public function resendVerification(string $identifier): array;
  public function resendVerificationCode(string $identifier): array; // Legacy
  public function sendEmailVerification(User $user, string $otp): void;
  public function verify(string $identifier, string $otp): array;
  public function verifyEmail(string $otp): array;
  public function sendPhoneVerification(User $user): void;
  public function verifyPhone(string $phone, string $code): array;

  public function forgotPassword(string $identifier): array;

  public function verifyOtpForPasswordReset(string $identifier, string $otp): array;

  public function resetPassword(string $token, string $newPassword): array;
}
