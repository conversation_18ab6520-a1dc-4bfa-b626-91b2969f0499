<?php

namespace App\Services\Interfaces;

use Illuminate\Http\UploadedFile;

interface ImageServiceInterface
{
  /**
   * Upload and process image
   *
   * @param UploadedFile $file
   * @param string $directory
   * @param string|null $filenamePrefix
   * @param array $options
   * @return string
   */
  public function upload(
    UploadedFile $file,
    string $directory,
    ?string $filenamePrefix = null,
    array $options = []
  ): string;

  /**
   * Upload multiple images
   *
   * @param array $files
   * @param string $directory
   * @param string|null $filenamePrefix
   * @param array $options
   * @return array
   */
  public function uploadMultiple(
    array $files,
    string $directory,
    ?string $filenamePrefix = null,
    array $options = []
  ): array;

  /**
   * Resize existing image
   *
   * @param string $imagePath
   * @param int $width
   * @param int $height
   * @param string|null $outputPath
   * @return string
   */
  public function resize(string $imagePath, int $width, int $height, ?string $outputPath = null): string;

  /**
   * Create thumbnail from image
   *
   * @param string $imagePath
   * @param int $width
   * @param int $height
   * @return string
   */
  public function createThumbnail(string $imagePath, int $width = 300, int $height = 300): string;

  /**
   * Optimize image quality and size
   *
   * @param string $imagePath
   * @param int $quality
   * @return string
   */
  public function optimize(string $imagePath, int $quality = 85): string;

  /**
   * Delete image file
   *
   * @param string|null $relativePath
   * @return bool
   */
  public function delete(?string $relativePath): bool;

  /**
   * Delete multiple images
   *
   * @param array $paths
   * @return array
   */
  public function deleteMultiple(array $paths): array;

  /**
   * Get image information
   *
   * @param string $imagePath
   * @return array
   */
  public function getImageInfo(string $imagePath): array;

  /**
   * Check if file is valid image
   *
   * @param UploadedFile $file
   * @return bool
   */
  public function isValidImage(UploadedFile $file): bool;

  /**
   * Get image URL
   *
   * @param string|null $imagePath
   * @param string $defaultImage
   * @return string
   */
  public function getImageUrl(?string $imagePath, string $defaultImage = 'images/no-image.jpg'): string;
}
