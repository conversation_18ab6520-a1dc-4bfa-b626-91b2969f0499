<?php

namespace App\Helpers;

class ValidationHelper
{
  /**
   * Validate email using custom regex
   */
  public static function isValidEmail(?string $email): bool
  {
    if (empty($email)) {
      return false;
    }

    $pattern = config('validation.regex.email');
    return $pattern && preg_match($pattern, $email);
  }

  /**
   * Validate Vietnam phone number
   */
  public static function isValidVietnamPhone(?string $phone, string $type = 'all'): bool
  {
    if (empty($phone)) {
      return false;
    }

    $pattern = config("validation.regex.phone.{$type}");
    return $pattern && preg_match($pattern, $phone);
  }

  /**
   * Validate mobile phone number
   */
  public static function isValidMobilePhone(?string $phone): bool
  {
    return self::isValidVietnamPhone($phone, 'mobile');
  }

  /**
   * Validate landline phone number
   */
  public static function isValidLandlinePhone(?string $phone): bool
  {
    return self::isValidVietnamPhone($phone, 'landline');
  }

  /**
   * Detect if identifier is email or phone
   */
  public static function detectIdentifierType(?string $identifier): ?string
  {
    if (empty($identifier)) {
      return null;
    }

    if (self::isValidEmail($identifier)) {
      return 'email';
    }

    if (self::isValidVietnamPhone($identifier)) {
      return 'phone';
    }

    return null;
  }

  /**
   * Format phone number to standard format
   */
  public static function formatPhoneNumber(?string $phone): ?string
  {
    if (empty($phone)) {
      return null;
    }

    // Remove all non-digit characters
    $phone = preg_replace('/[^0-9]/', '', $phone);

    // Convert +84 to 0
    if (str_starts_with($phone, '84')) {
      $phone = '0' . substr($phone, 2);
    }

    return $phone;
  }

  /**
   * Get phone carrier from phone number
   */
  public static function getPhoneCarrier(?string $phone): ?string
  {
    if (!self::isValidVietnamPhone($phone)) {
      return null;
    }

    $phone = self::formatPhoneNumber($phone);
    $prefix = substr($phone, 0, 3);

    $prefixes = config('validation.phone_prefixes');

    foreach ($prefixes as $carrier => $carrierPrefixes) {
      if (in_array($prefix, $carrierPrefixes)) {
        return $carrier;
      }
    }

    return null;
  }

  /**
   * Validate password strength
   */
  public static function validatePasswordStrength(?string $password, string $level = 'basic'): bool
  {
    if (empty($password)) {
      return false;
    }

    $pattern = config("validation.regex.password.{$level}");
    return $pattern && preg_match($pattern, $password);
  }

  /**
   * Get validation rules for email field
   */
  public static function getEmailValidationRules(bool $required = false, ?int $userId = null): array
  {
    $rules = ['nullable'];

    if ($required) {
      $rules[0] = 'required';
    }

    $rules[] = new \App\Rules\CustomEmail();
    $rules[] = 'max:255';

    if ($userId) {
      $rules[] = "unique:users,email,{$userId}";
    } else {
      $rules[] = 'unique:users,email';
    }

    return $rules;
  }

  /**
   * Get validation rules for phone field
   */
  public static function getPhoneValidationRules(bool $required = false, ?int $userId = null, string $type = 'all'): array
  {
    $rules = ['nullable'];

    if ($required) {
      $rules[0] = 'required';
    }

    $rules[] = new \App\Rules\VietnamPhone($type);
    $rules[] = 'max:20';

    if ($userId) {
      $rules[] = "unique:users,phone,{$userId}";
    } else {
      $rules[] = 'unique:users,phone';
    }

    return $rules;
  }

  /**
   * Get validation rules for identifier field (email or phone) with uniqueness check
   */
  public static function getIdentifierValidationRules(?int $userId = null): array
  {
    return [
      'required',
      'string',
      function ($attribute, $value, $fail) use ($userId) {
        $type = self::detectIdentifierType($value);

        if (!$type) {
          $fail('Vui lòng nhập email hoặc số điện thoại hợp lệ');
          return;
        }

        // Check uniqueness
        $query = \App\Models\User::query();

        if ($type === 'email') {
          $query->where('email', $value);
        } else {
          $query->where('phone', $value);
        }

        if ($userId) {
          $query->where('id', '!=', $userId);
        }

        if ($query->exists()) {
          $message = $type === 'email' ? 'Email đã được sử dụng' : 'Số điện thoại đã được sử dụng';
          $fail($message);
        }
      }
    ];
  }

  /**
   * Get validation rules for user identifier (email or phone) - FOR USER IDENTIFICATION
   */
  public static function getIdentifierValidationRulesForUser(): array
  {
    return [
      'required',
      'string',
      function ($attribute, $value, $fail) {
        if (!self::isValidEmail($value) && !self::isValidVietnamPhone($value)) {
          $fail('Vui lòng nhập email hoặc số điện thoại hợp lệ');
        }
      }
    ];
  }

  /**
   * Get validation rules for password field
   */
  public static function getPasswordValidationRules(bool $required = true, string $level = 'basic'): array
  {
    $rules = ['nullable'];

    if ($required) {
      $rules[0] = 'required';
    }

    $rules[] = 'string';
    $rules[] = 'min:8';

    if ($level === 'strong') {
      $rules[] = 'regex:' . config('validation.regex.password.strong');
    } elseif ($level === 'very_strong') {
      $rules[] = 'regex:' . config('validation.regex.password.very_strong');
    } else {
      $rules[] = 'regex:' . config('validation.regex.password.basic');
    }

    return $rules;
  }
}
