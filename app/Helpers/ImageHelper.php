<?php

namespace App\Helpers;

use App\Services\Interfaces\ImageServiceInterface;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\App;

class ImageHelper
{
  /**
   * Get ImageService instance
   *
   * @return ImageServiceInterface
   */
  private static function getImageService(): ImageServiceInterface
  {
    return App::make(ImageServiceInterface::class);
  }

  /**
   * Upload user avatar
   *
   * @param UploadedFile $file
   * @param int $userId
   * @param array $options
   * @return string
   */
  public static function uploadUserAvatar(UploadedFile $file, int $userId, array $options = []): string
  {
    $defaultOptions = [
      'resize'     => true,
      'max_width'  => 800,
      'max_height' => 800,
      'quality'    => 85
    ];

    $options = array_merge($defaultOptions, $options);

    return self::getImageService()->upload(
      $file,
      'uploads/users/avatars',
      "user-{$userId}",
      $options
    );
  }

  /**
   * Upload member avatar
   *
   * @param UploadedFile $file
   * @param string $username
   * @param array $options
   * @return string
   */
  public static function uploadMemberAvatar(UploadedFile $file, string $username, array $options = []): string
  {
    $defaultOptions = [
      'resize'     => true,
      'max_width'  => 400,
      'max_height' => 400,
      'quality'    => 85
    ];

    $options = array_merge($defaultOptions, $options);

    return self::getImageService()->upload(
      $file,
      'uploads/avatars',
      $username,
      $options
    );
  }

  /**
   * Upload beauti store image
   *
   * @param UploadedFile $file
   * @param string $storeName
   * @param array $options
   * @return string
   */
  public static function uploadBeautiStoreImage(UploadedFile $file, string $storeName, array $options = []): string
  {
    $defaultOptions = [
      'resize'     => true,
      'max_width'  => 1200,
      'max_height' => 800,
      'quality'    => 85
    ];

    $options = array_merge($defaultOptions, $options);

    return self::getImageService()->upload(
      $file,
      'uploads/beauti-stores',
      $storeName,
      $options
    );
  }

  /**
   * Upload beauti service image
   *
   * @param UploadedFile $file
   * @param string $serviceName
   * @param array $options
   * @return string
   */
  public static function uploadBeautiServiceImage(UploadedFile $file, string $serviceName, array $options = []): string
  {
    $defaultOptions = [
      'resize'     => true,
      'max_width'  => 800,
      'max_height' => 600,
      'quality'    => 85
    ];

    $options = array_merge($defaultOptions, $options);

    return self::getImageService()->upload(
      $file,
      'uploads/beauti-services',
      $serviceName,
      $options
    );
  }

  /**
   * Upload news article image
   *
   * @param UploadedFile $file
   * @param string $articleTitle
   * @param array $options
   * @return string
   */
  public static function uploadNewsArticleImage(UploadedFile $file, string $articleTitle, array $options = []): string
  {
    $defaultOptions = [
      'resize'     => true,
      'max_width'  => 1200,
      'max_height' => 800,
      'quality'    => 85
    ];

    $options = array_merge($defaultOptions, $options);

    return self::getImageService()->upload(
      $file,
      'uploads/news-articles',
      $articleTitle,
      $options
    );
  }

  /**
   * Upload system config image
   *
   * @param UploadedFile $file
   * @param string $configName
   * @param array $options
   * @return string
   */
  public static function uploadSystemConfigImage(UploadedFile $file, string $configName, array $options = []): string
  {
    $defaultOptions = [
      'resize'     => true,
      'max_width'  => 800,
      'max_height' => 600,
      'quality'    => 85
    ];

    $options = array_merge($defaultOptions, $options);

    return self::getImageService()->upload(
      $file,
      'uploads/system-configs',
      $configName,
      $options
    );
  }

  /**
   * Upload location area image
   *
   * @param UploadedFile $file
   * @param string $areaName
   * @param array $options
   * @return string
   */
  public static function uploadLocationAreaImage(UploadedFile $file, string $areaName, array $options = []): string
  {
    $defaultOptions = [
      'resize'     => true,
      'max_width'  => 600,
      'max_height' => 400,
      'quality'    => 85
    ];

    $options = array_merge($defaultOptions, $options);

    return self::getImageService()->upload(
      $file,
      'uploads/areas',
      $areaName,
      $options
    );
  }

  /**
   * Upload TinyMCE editor image
   *
   * @param UploadedFile $file
   * @param array $options
   * @return string
   */
  public static function uploadTinyMCEImage(UploadedFile $file, array $options = []): string
  {
    $subFolder = now()->format('Y/m/d');
    $defaultOptions = [
      'resize'     => true,
      'max_width'  => 1920,
      'max_height' => 1080,
      'quality'    => 85
    ];

    $options = array_merge($defaultOptions, $options);

    return self::getImageService()->upload(
      $file,
      "uploads/images/{$subFolder}",
      null,
      $options
    );
  }

  /**
   * Delete image with logging
   *
   * @param string|null $imagePath
   * @param string $context
   * @return bool
   */
  public static function deleteImage(?string $imagePath, string $context = ''): bool
  {
    if (empty($imagePath)) {
      return true;
    }

    $result = self::getImageService()->delete($imagePath);

    if ($result) {
      \Log::info("Image deleted successfully", [
        'path'    => $imagePath,
        'context' => $context
      ]);
    } else {
      \Log::error("Image deletion failed", [
        'path'    => $imagePath,
        'context' => $context
      ]);
    }

    return $result;
  }

  /**
   * Get image URL with fallback
   *
   * @param string|null $imagePath
   * @param string $defaultImage
   * @return string
   */
  public static function getImageUrl(?string $imagePath, string $defaultImage = 'images/no-image.jpg'): string
  {
    return self::getImageService()->getImageUrl($imagePath, $defaultImage);
  }

  /**
   * Get user avatar URL
   *
   * @param string|null $avatarPath
   * @return string
   */
  public static function getUserAvatarUrl(?string $avatarPath): string
  {
    return self::getImageUrl($avatarPath, 'images/default-avatar.jpg');
  }

  /**
   * Get member avatar URL
   *
   * @param string|null $avatarPath
   * @return string
   */
  public static function getMemberAvatarUrl(?string $avatarPath): string
  {
    return self::getImageUrl($avatarPath, 'images/default-member-avatar.jpg');
  }

  /**
   * Get beauti store image URL
   *
   * @param string|null $imagePath
   * @return string
   */
  public static function getBeautiStoreImageUrl(?string $imagePath): string
  {
    return self::getImageUrl($imagePath, 'images/default-store.jpg');
  }

  /**
   * Get beauti service image URL
   *
   * @param string|null $imagePath
   * @return string
   */
  public static function getBeautiServiceImageUrl(?string $imagePath): string
  {
    return self::getImageUrl($imagePath, 'images/default-service.jpg');
  }

  /**
   * Get news article image URL
   *
   * @param string|null $imagePath
   * @return string
   */
  public static function getNewsArticleImageUrl(?string $imagePath): string
  {
    return self::getImageUrl($imagePath, 'images/default-news.jpg');
  }

  /**
   * Validate image file
   *
   * @param UploadedFile $file
   * @return bool
   */
  public static function isValidImage(UploadedFile $file): bool
  {
    return self::getImageService()->isValidImage($file);
  }

  /**
   * Get image information
   *
   * @param string $imagePath
   * @return array
   */
  public static function getImageInfo(string $imagePath): array
  {
    return self::getImageService()->getImageInfo($imagePath);
  }

  /**
   * Create thumbnail
   *
   * @param string $imagePath
   * @param int $width
   * @param int $height
   * @return string
   */
  public static function createThumbnail(string $imagePath, int $width = 300, int $height = 300): string
  {
    return self::getImageService()->createThumbnail($imagePath, $width, $height);
  }

  /**
   * Optimize image
   *
   * @param string $imagePath
   * @param int $quality
   * @return string
   */
  public static function optimizeImage(string $imagePath, int $quality = 85): string
  {
    return self::getImageService()->optimize($imagePath, $quality);
  }
}
