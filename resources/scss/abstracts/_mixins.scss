@mixin text-ellipsis($lines: 1) {
  overflow: hidden;
  text-overflow: ellipsis;

  @if $lines == 1 {
    white-space: nowrap;
  } @else {
    display: -webkit-box;
    -webkit-line-clamp: $lines;
    -webkit-box-orient: vertical;
  }
}

@mixin transition($time: 0.3s, $type: ease-in-out) {
  transition: all $time $type;
}

@mixin fadeInUp($duration: 0.4s, $distance: 10px) {
  animation: fadeInUp $duration ease-out;
  @keyframes fadeInUp {
    from {
      opacity: 0;
      transform: translateY($distance);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
}

@mixin square($size) {
  width: $size;
  height: $size;
}
