body.beauti-services .container.wide {
    max-width: 1784px;
    margin: 0 auto;
}

.container.wide {
    max-width: 1243px;
    margin: 0 auto;
}

#scrollTopBtn {
    position: fixed;
    bottom: 30px;
    right: 30px;
    width: 44px;
    height: 44px;
    border: none;
    border-radius: 50%;
    background-color: rgba(0, 0, 0, 0.6);
    color: #fff;
    font-size: 24px;
    display: none;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    z-index: 9999;
    @include transition(.2s);

    &:hover {
        background-color: rgba(0, 0, 0, 0.8);
    }
}

.section {
    padding-top: 40px;
}

.box-head {
    color: $primary-color-2;
    font-size: 36px;
    font-weight: 600;
    line-height: 1.5;
    & span {
        color: $primary-color-1;
    }
}

.arrow {
    padding: 6px 20px;
    border: 1px solid $primary-color-1;
    border-radius: 60px;
    @include transition(0.2s);
    &:hover {
        background: $primary-color-1;
        svg {
            path {
                fill: $text-white;
            }
        }
    }
}

.line {
    width: 100%;
    height: 2px;
    background: $primary-color-1;
}

ul.pagination {
    display: flex;
    align-items: center;
    gap: 10px;

    .page-link {
        width: 37px;
        height: 37px;
        color: #7e7e7e;
        background: #f4e9ff;
        border: none;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        @include transition(0.1s);
        &:hover {
            background: $primary-color-2;
            color: $text-white;
        }
    }

    li.active {
        .page-link {
            color: $primary-color-2;
            &:hover {
                background: #f4e9ff;
            }
        }
    }
}

// search-box

.search-section--beauti-type {
    background: $primary-color-2;
    color: $text-white;
    .search-box {
        margin-top: initial;
        background: initial;

        .search-input-group {
            .icon {
                background: $primary-color-1;
            }
        }
        .search-option {
            .option-title {
                color: $text-white;
            }

            &:hover {
                background: $primary-color-1;
                svg {
                    path {
                        fill: $text-white;
                    }
                }
                .option-desc {
                    color: $text-white;
                }
            }
        }
    }
}

.search-box {
    border-radius: 100px;
    background: $text-white;
    box-shadow: 0px 4px 8px 0px rgba(84, 22, 139, 0.1);
    position: relative;
    z-index: 1;
    padding-inline: 26px;
    margin-top: -50px;
    .search-input-group {
        position: relative;

        .icon {
            position: absolute;
            left: 0;
            background-color: $primary-color-2;
            border-radius: 50%;
            padding: 20px;
            svg {
                width: 30px;
                height: 30px;
            }
        }
        input {
            padding: 23px 10px 23px 90px;
            border-radius: 100px;
            border: 1px solid $primary-color-1;
        }
    }

    .search-option {
        padding: 26px 15px;
        cursor: pointer;
        @include transition;
        &:hover {
            background-color: #f4e9ff;
        }
        .option-title {
            color: $primary-color-2;
            font-weight: 600;
        }
        .option-desc {
            color: #b1b1b1;
            font-size: 14px;
            font-weight: 300;
        }
    }
    button.button {
        margin-left: auto;
    }
    .button-orange {
        color: $text-white;
        background-color: #fc3f29;
    }
}

// section featured__banners
.featured__banners {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    gap: 20px;
    max-width: 1652px;
    width: 95vw;
    margin: 0 auto;
    &--box {
        display: block;
        flex: 1;
        min-width: 0;
        aspect-ratio: 16 / 9;
        border-radius: 28px;
        line-height: 0;
        overflow: hidden;
        img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            object-position: center;
        }
    }
}

//section beauty__category
.beauty__category {
    &--box {
        display: inline-block;
        padding: 22px;
        background: $primary-color-1;
        border-radius: 16px;
        text-align: center;
        color: $text-white;
        border: 1px solid $primary-color-1;
        @include transition(0.3s, ease-in-out);

        &:hover {
            background: $text-white;
            .category-title {
                color: $primary-color-2;
            }
            .button--arrow {
                background: $primary-color-2;
                svg {
                    path {
                        fill: $text-white;
                    }
                }
            }
        }
        .category-title {
            font-size: 18px;
            font-weight: 600;
            @include transition;
        }
    }
    &--icon {
        width: 120px;
        height: 120px;
        border-radius: 50%;
        overflow: hidden;
        background: #e9d4ff;
        position: relative;
        display: inline-block;
        margin-inline: 25px;
        margin-bottom: 20px;
        img {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }
    }
}

.location__featured {
    &--tab {
        .item {
            padding: 7px 25px;
            border-radius: 86px;
            border: 1px solid $primary-color-1;
            position: relative;
        }
    }

    &--item {
        padding: 10px;
        background: $text-white;
        box-shadow: 0px 6px 12px 0px rgba(84, 22, 139, 0.1);
        border-radius: 16px;
        position: relative;

        .heart--icon,
        .featured--icon {
            position: absolute;
            right: 22px;
            cursor: pointer;
        }
        .heart--icon {
            top: 52px;
            &:hover {
                svg {
                    rect {
                        fill: $primary-color-1;
                    }
                }
            }
        }
        .featured--icon {
            top: 22px;
            width: 24px;
            height: 24px;
            background: $text-white;
            border-radius: 50%;
            overflow: hidden;
        }
        .box-image {
            border-radius: 12px;
            overflow: hidden;
            img {
                object-fit: cover;
                aspect-ratio: 3 / 2;
            }
        }

        .box-desc {
            &--logo {
            }
            &--details {
                color: $primary-color-2;
                .title {
                    font-weight: 500;
                }
                .location {
                    span {
                        font-size: 12px;
                        font-weight: 400;
                    }
                }
                .price_from {
                    font-size: 20px;
                    font-weight: 800;
                    color: $primary-color-1;
                    span {
                        font-size: 16px;
                        color: #7e7e7e;
                        font-weight: 400;
                    }
                }
                .icon {
                    position: absolute;
                    bottom: 10px;
                    right: 10px;
                }
            }
        }
    }
}
.store--time {
    padding: 5px 11px;
    border-radius: 100px;
    background: #f4e9ff;
    color: $primary-color-2;
    width: fit-content;
}

// brand__partners__carousel
.brand__partners__carousel {
    padding-block: 60px;
    background-color: $primary-color-2;
    clip-path: ellipse(120% 160% at 50% 160%);
    &--slider {
        position: relative;
        padding: 0 60px;
        overflow: hidden;
        .swiper-wrapper {
            align-items: center;
        }
        &::before,
        &::after {
            content: "";
            position: absolute;
            top: 0;
            width: 60px;
            height: 100%;
            z-index: 2;
            pointer-events: none;
        }
        &::before {
            left: 0;
            background: $primary-color-2;
        }
        &::after {
            right: 0;
            background: $primary-color-2;
        }
        .arrow {
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            z-index: 1000;
            &-left {
                left: 0;
            }
            &-right {
                right: 0;
            }
            svg {
                path {
                    fill: $text-white;
                }
            }
        }
    }
    .box-head {
        color: $text-white;
    }
    &--item {
        display: flex;
        align-items: center;
        justify-content: center;
        .box-image {
            background: #fff;
            box-shadow: 0px 6px 12px 0px rgba(237, 217, 255, 0.1);
            width: 138px;
            height: 138px;
            padding: 29px 28px;
            border-radius: 50%;
            overflow: hidden;
            img {
                max-width: 100%;
                max-height: 100%;
                object-fit: contain;
            }
        }
    }
}

// featured-news-article
.featured-news-article {
    padding-bottom: 40px;
    .box-image {
        img {
            aspect-ratio: 16 / 9;
            object-fit: cover;
            border-radius: 24px;
        }
    }
    .box-category {
        position: absolute;
        top: 13px;
        right: 19px;
        font-size: 12px;
        color: $text-white;
        padding: 4px 17px;
        border-radius: 60px;
        background: $primary-color-2;
        @include transition(0.2s);
        &:hover {
            background: $primary-color-1;
        }
    }
    .box-desc {
        color: $primary-color-2;
        .index {
            font-size: 60px;
            font-weight: 700;
        }
        .title {
            font-size: 18px;
            font-weight: 700;
            @include transition(0.2s);

            &:hover {
                color: $primary-color-1;
            }
        }
    }
}

// beauti-types-page
.beauti-types-page {
    background: #f9f4ff;
    .breadcrumb {
        a {
            color: $primary-color-2;
            font-size: 14px;
            font-weight: 500;
        }
        span,
        .breadcrumb--current {
            color: $primary-color-1;
        }
    }
    .list-stores {
        &--types {
            padding: 25px;
            background: $text-white;
            label {
                color: #202020;
                font-size: 14px;
                font-weight: 400;
            }
            .btn-filter {
                color: $primary-color-2;
                font-weight: 700;
                padding: 5px 15px;
                border-radius: 20px;
                background: #f9f4ff;
            }
        }
    }
}

//beauti-service-page
.beauti-service-page {
    background: #f9f4ff;
    color: $primary-color-2;
    padding-bottom: 40px;
    .store--location {
        span.location {
            font-weight: 400;
        }
    }
    .tag--items {
        padding: 7px 25px;
        border-radius: 86px;
        background: #e5ccff;
        font-size: 14px;
        font-weight: 700;
        cursor: pointer;
        @include transition(0.1s);
        &:hover {
            background: $primary-color-1;
            color: $text-white;
        }
    }
    img.box-image {
        border-radius: 16px;
        aspect-ratio: 16/9;
        object-fit: cover;
        height: 100%;
    }
}

.store__list--services {
    .service-item {
        padding: 7px 25px;
        border: 2px solid $primary-color-1;
        border-radius: 86px;
        font-size: 14px;
        font-weight: 500;
        cursor: pointer;
        @include transition(0.1s);
        &:hover {
            border: 2px solid #e5ccff;
            background: #e5ccff;
        }
    }
}
.list__services {
    border-radius: 20px;
}
.list__service--area {
    border-radius: 20px;
    .discover--text {
        font-size: 20px;
        font-weight: 700;
    }
    .list--nearby {
        font-size: 14px;
        .text-load-more {
            color: $primary-color-1;
            font-weight: 400;
            cursor: pointer;
        }
    }
}

.store--desc {
    color: #202020;
}
.intro__store {
    border-radius: 20px;
}
.package--item {
    box-shadow: 0px 6px 12px 0px rgba(84, 22, 139, 0.1);
    border-radius: 0px 0px 20px 20px;
    overflow: hidden;
    span {
        font-size: 14px;
        font-weight: 400;
    }
    .box-text {
        padding: 15px;
        font-weight: 700;
        svg {
            width: 14.4px;
            height: 14.4px;
            flex-shrink: 0;
            aspect-ratio: 14.4 / 14.4;
        }
        .title {
            font-size: 18px;
            font-weight: 700;
        }
    }
    .box-action {
        background: #f4e9ff;
        padding-left: 35px;
        padding-block: 15px;
        padding-right: 10px;
        .price-new {
            font-size: 20px;
            font-weight: 800;
        }
        .price-old {
            color: #9e9e9e;
            font-size: 14px;
            font-weight: 400;
            text-decoration-line: line-through;
        }
    }
    .button {
        padding: 10px 15px;
        color: $text-white;
        background: #611bb9;
        @include transition(0.1s);
        &:hover {
            background: $primary-color-2;
        }
        &--arrow {
            background: #e0d4ff;
        }
    }
}

// list-stores-location
.list-stores-location {
    &--inner {
        background: $primary-color-2;
        padding: 30px 120px;
        border-radius: 20px;
        .box-head {
            color: $primary-color-1;
        }
    }
}

// news--item
.news--item {
    background: $text-white;
    border-radius: 8px;
    overflow: hidden;
    .box-image {
        flex: 1;
        overflow: hidden;
        img {
            aspect-ratio: 16/9;
            object-fit: cover;
            height: 100%;
        }
    }
    .news--content {
        flex: 2;
        padding: 15px;
        .news--title {
            overflow: hidden;
            color: $primary-color-2;
            font-size: 18px;
            font-weight: 700;
            @include text-ellipsis(2);
        }
        .news--desc {
            color: #3a3c3e;
            font-weight: 400;
            @include text-ellipsis(4);
        }
        span {
            color: #898a8b;
            font-size: 12px;
            font-weight: 400;
        }
    }
}
/* ———————————————————————————————— */
/* Extra small (xs) */
/* ———————————————————————————————— */
@media (max-width: 575.98px) {
}

/* ———————————————————————————————————————— */
/* Small (sm)*/
/* ———————————————————————————————————————— */
@media (min-width: 576px) and (max-width: 767.98px) {
}

/* ———————————————————————————————————————— */
/* Medium (md)*/
/* (tablet) */
/* ———————————————————————————————————————— */
@media (min-width: 768px) and (max-width: 991.98px) {
}

/* ———————————————————————————————————————— */
/* Large (lg) */
/* ———————————————————————————————————————— */
@media (min-width: 992px) and (max-width: 1199.98px) {
}

/* ———————————————————————————————————————— */
/* Extra Large (xl) */
/* ———————————————————————————————————————— */
@media (min-width: 1200px) and (max-width: 1399.98px) {
    .container.wide {
        max-width: 1190px;
    }
    body.beauti-services .container.wide {
        max-width: 1731px;
    }
    .box-head {
        font-size: 32px;
    }
}

/* ———————————————————————————————————————— */
/* XXL */
/* ———————————————————————————————————————— */
@media (min-width: 1400px) {
}
