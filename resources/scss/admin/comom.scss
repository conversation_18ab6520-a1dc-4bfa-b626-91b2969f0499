@import '@lib/css/plugins/bootstrap.min.css';
@import '@lib/css/style-preset.css';
@import '@lib/css/style.css';

*{
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

.form-control {
    border: 1px solid #ccc;
    border-radius: 3px;
    box-shadow: none !important;
    margin-bottom: 15px;
}

.form-control:focus {
    border: 1px solid #34495e;
}

.select2-search--dropdown{
    padding: 0;
}

.select2-container--default .select2-search--dropdown .select2-search__field{
    border-inline: none;
    border-top:none ;
}

.select2.select2-container {
  width: 100% !important;
}

.select2.select2-container .select2-selection {
  border: 1px solid #ccc;
  -webkit-border-radius: 3px;
  -moz-border-radius: 3px;
  border-radius: 3px;
  height: 47px;
  outline: none !important;
  transition: all .15s ease-in-out;
}

.select2-container--default .select2-selection--single .select2-selection__clear{
  margin-right: 32px;
  width: 32px;
  height: 100%;

  &:hover{
    color: #fff;
    background: rgba(226, 16, 16, 0.849);
  }
}

.select2.select2-container .select2-selection .select2-selection__rendered {
  color: #333;
  line-height: 47px;
  padding-right: 33px;
}

.select2.select2-container .select2-selection .select2-selection__arrow {
  background: #f8f8f8;
  border-left: 1px solid #ccc;
  -webkit-border-radius: 0 3px 3px 0;
  -moz-border-radius: 0 3px 3px 0;
  border-radius: 0 3px 3px 0;
  height: 45px;
  width: 33px;
}

.select2.select2-container.select2-container--open .select2-selection.select2-selection--single {
  background: #f8f8f8;
}

.select2.select2-container.select2-container--open .select2-selection.select2-selection--single .select2-selection__arrow {
  -webkit-border-radius: 0 3px 0 0;
  -moz-border-radius: 0 3px 0 0;
  border-radius: 0 3px 0 0;
}