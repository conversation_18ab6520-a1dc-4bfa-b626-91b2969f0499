.hero__section {
    background-color: #f4e9ff;
    position: relative;
    overflow: hidden;
    &--inner {
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;
    }
    &--left {
        flex: 1 1 40%;
        min-width: 300px;
        padding-top: 49px;
        padding-bottom: 84px;
        padding-right: 62px;
        .button {
            margin-left: auto;
        }
    }
    &--right {
        flex: 1 1 60%;
        min-width: 300px;
        
        .box-image {
            position: absolute;
            height: 100%;
                img {
                width: 100%;
                height: 100%;
                object-fit: cover;
            }
        }
    }
}

/* ———————————————————————————————————————— */
/* Medium (md)*/
/* (tablet) */
/* ———————————————————————————————————————— */
@media (min-width: 768px) and (max-width: 991.98px) {
}

/* ———————————————————————————————————————— */
/* Large (lg) */
/* ———————————————————————————————————————— */
@media (min-width: 992px) and (max-width: 1199.98px) {
}

/* ———————————————————————————————————————— */
/* Extra Large (xl) */
/* ———————————————————————————————————————— */
@media (min-width: 1200px) and (max-width: 1399.98px) {
}

/* ———————————————————————————————————————— */
/* XXL */
/* ———————————————————————————————————————— */
@media (min-width: 1400px) {
}
