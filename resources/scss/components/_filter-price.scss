.list-stores {
    .banner {
        border-radius: 16px;
    }
    &--types {
        border-radius: 16px;
    }
}
.price-filter {
    border-radius: 16px;
    background: $text-white;
    &--text {
        color: $primary-color-2;
        font-size: 18px;
        font-weight: 700;
    }
    .price-slider-wrapper {
        height: 40px;
        position: relative;

        .form-range {
            position: absolute;
            width: 100%;
            height: 4px;
            background: transparent;
            pointer-events: none;
            -webkit-appearance: none;
            margin: 0;
            top: 50%;
            transform: translateY(-50%);
            z-index: 3;
            &::-webkit-slider-thumb {
                pointer-events: all;
                width: 16px;
                height: 16px;
                border-radius: 50%;
                background: $primary-color-2;
                -webkit-appearance: none;
                z-index: 3;
                z-index: 4;
            }

            &::-moz-range-thumb {
                pointer-events: all;
                width: 16px;
                height: 16px;
                border-radius: 50%;
                background: $primary-color-2;
                border: none;
                z-index: 4;
            }
        }

        .slider-track {
            position: absolute;
            top: 50%;
            left: 0;
            width: 100%;
            height: 4px;
            background: #e9ecef;
            transform: translateY(-50%);
            border-radius: 2px;
            z-index: 1;

            &::before {
                content: "";
                position: absolute;
                height: 100%;
                background: $primary-color-2;
                left: 0;
                width: 0%;
                z-index: 2;
            }
        }

        .thumb {
            position: absolute;
            top: 0;
            transform: translate(-50%, -70%);
            z-index: 5;

            .thumb-value {
                background: $text-white;
                padding: 2px 6px;
                border-radius: 4px;
                font-size: 14px;
                border: 1px solid #dee2e6;
                white-space: nowrap;
                color: $primary-color-1;
                font-weight: bold;
            }
        }
    }
}
