.modal-dialog {
    min-width: 690px;
    border-radius: 20px;
    .modal-content {
        padding: 31px;
        .modal-header {
            border: none;

            .modal-title {
                color: $primary-color-2;
                font-size: 28px;
                font-weight: 700;
            }
        }
        .modal-body {
            margin-top: 10px;
            label {
                color: #202020;
                font-weight: 400;
                text-transform: capitalize;
            }
            input {
                &:focus {
                    outline: 0;
                    box-shadow: none !important;
                }
            }
            .form-control {
                padding: 12px 16px;
            }
            button.btn--primary {
                padding: 14px;
                background: $primary-color-2;
                color: $text-white;
                width: 100%;
            }

            button.toggle-password,
            button.toggle-conf-password {
                border: 1px solid #dee2e6;
            }
        }
        .forget-pass {
            color: #611bb9;
            text-align: right;
            font-size: 14px;
            font-weight: 400;
            text-transform: capitalize;
        }
        .btn-close {
            position: absolute;
            top: 20px;
            right: 20px;
            &:focus {
                outline: 0;
                box-shadow: none !important;
            }
        }
        .register-link,
        .login-link {
            color: $primary-color-1;
        }
    }
    .modal-footer {
        button.btn--close {
            padding: 14px;
            border-radius: 0;
        }
        button.btn--primary {
            padding: 14px;
            border-radius: 0;
            background: $primary-color-2;
            color: $text-white;
        }
    }
}
