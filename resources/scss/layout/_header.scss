.l-header {
    height: 90px;
    display: flex;
    align-items: center;
    @include transition(0.3s);
    &.sticky {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        width: 100%;
        height: 60px;
        z-index: 1000;
        background-color: $text-white;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

        .dropdown-menu {
            transform: translate3d(0px, 52px, 0px) !important ;
        }
        .l-header__nav .l-nav-menu .submenu {
            transform: translate3d(0px, 18px, 0px) !important;
        }
    }
    &__inner {
        height: 100%;
    }
    &__nav {
        .js-nav-hide {
            visibility: hidden;
        }
        .l-nav-menu {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0 20px;
            flex-wrap: wrap;
            list-style: none;
            margin: 0;
            padding: 0;
            .menu-item {
                position: relative;

                .submenu .has-children {
                    position: relative;
                }

                .submenu .has-children > .submenu {
                    top: 0;
                    left: 100%;
                }

                &.open > .submenu {
                    display: block;
                }
            }
            li {
                @include transition;
                a {
                    padding-block: 5px;
                    font-weight: 500;
                    border-bottom: 2px solid transparent;
                    &:hover {
                        border-color: $primary-color-1 !important;
                        color: #611bb9;
                    }
                    span{
                        text-transform: lowercase;
                    }
                }

                &.active {
                    a {
                        color: #611bb9;
                        border-color: $primary-color-1 !important;
                    }
                }
            }
            .submenu {
                display: none;
                position: absolute;
                padding-block: 8px;
                z-index: 9999;
                transform: translate3d(0px, 33px, 0px) !important;
            }
        }
    }

    .dropdown-menu,
    .submenu {
        width: max-content;
        border-radius: 0 0 12px 12px;
        border: none;
        border-radius: 0 0 12px 12px;
        background: rgba(255, 255, 255, 0.8);
        box-shadow: 0px 4px 8px 0px rgba(84, 22, 139, 0.1);
        transform: translate3d(0px, 66px, 0px) !important ;

        li {
            a {
                color: #202020;
                font-weight: 500;
                @include transition(0.1s);

                &:hover {
                    color: #611bb9;
                    background: #f8f9fa;
                }
                &:focus{
                    background: initial;
                }
            }
        }
        a {
            display: block;
            color: #611bb9;
            padding: 10px 40px 10px 10px !important;
        }
    }
    .dropdown-toggle::after {
        display: none;
    }

    .header__user-action--notification,
    .header__user-action--favorite,
    .header__user-action--profile .icon {
        width: 45px;
        height: 45px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
        @include transition(0.1s);
        cursor: pointer;
        &:hover {
            background-color: #f4e9ff;

            svg {
                path {
                    fill: $primary-color-2;
                }
            }
        }
    }
    .header__user-action--notification {
        margin-top: 1px;
    }

    .header__user-action--favorite {
        margin-top: 3px;
    }

    .header__user-action--profile {
        button.show {
            .icon {
                background-color: #f4e9ff;
            }
            svg {
                path {
                    fill: $primary-color-2;
                }
            }
        }
    }

    .header__user-action--favorite {
        &:hover {
            svg {
                rect {
                    fill: $primary-color-2;
                }
            }
        }
    }
}

/* ———————————————————————————————— */
/* Extra small (xs) */
/* ———————————————————————————————— */
@media (max-width: 575.98px) {
}

/* ———————————————————————————————————————— */
/* Small (sm)*/
/* ———————————————————————————————————————— */
@media (max-width: 767.98px) {
}

/* ———————————————————————————————————————— */
/* Medium (md)*/
/* (tablet) */
/* ———————————————————————————————————————— */
@media (max-width: 991.98px) {
}

/* ———————————————————————————————————————— */
/* Large (lg) */
/* ———————————————————————————————————————— */
@media (max-width: 1199.98px) {
}

/* ———————————————————————————————————————— */
/* Extra Large (xl) */
/* ———————————————————————————————————————— */

@media (max-width: 1399.98px) {
    .l-header {
        &__nav {
            .l-nav-menu {
                li {
                    a {
                        font-size: 14px !important;
                    }
                }
            }
        }
        .dropdown-menu,
        .submenu {
            li {
                a {
                    font-size: 14px;
                }
            }
        }
    }
}
