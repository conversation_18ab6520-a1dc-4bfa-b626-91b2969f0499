@props(['label' => 'Chọn ảnh liên quan', 'name' => 'imgs', 'currentImages' => []])

@php
$inputId = 'input-' . $name;
$defaultImage = asset('images/no-image.jpg');
$decodedImages = is_string($currentImages) ? json_decode($currentImages, true) : $currentImages;
@endphp

<div class="mb-3">
    <label class="form-label">{{ $label }}</label>
    <div class="card p-2">
        <div id="{{ $inputId }}-preview" data-default-image="{{ $defaultImage }}" class="mb-2 d-flex flex-wrap justify-content-center gap-2">
            @php
            $images = (array) old($name, $decodedImages);
            $hasImages = !empty($images) && array_filter($images);
            @endphp

            @if($hasImages)
            @foreach($images as $image)
            <div class="image-wrapper position-relative">
                <img src="{{ $image ?: $defaultImage }}" style="width: 250px; height: 200px; object-fit: cover;" class="img-thumbnail">
                <input type="hidden" name="{{ $name }}[]" value="{{ $image }}">
                <button type="button" class="btn btn-sm btn-danger position-absolute top-0 end-0 remove-image">&times;</button>
            </div>
            @endforeach
            @else
            <div class="image-wrapper position-relative default-image-wrapper">
                <img src="{{ $defaultImage }}" style="width: 250px; height: 200px; object-fit: cover;" class="img-thumbnail">
            </div>
            @endif

        </div>
        <div class="text-center">
            <button type="button"
                class="btn btn-outline-primary"
                id="{{ $inputId }}-choose"
                data-input-id="{{ $inputId }}"
                data-name="{{$name}}">
                Chọn ảnh
            </button>
        </div>
    </div>
    @error($name)
    <div class="invalid-feedback d-block mt-2 text-center">{{ $message }}</div>
    @enderror
</div>