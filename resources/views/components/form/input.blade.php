@props(['label', 'name', 'type' => 'text', 'value' => '', 'class'=>'class', 'required' => false])

@php
$inputId = 'input-' . $name;
$error = $errors->first($name);
@endphp

<div class="mb-3">
    <label for="{{ $inputId }}" class="form-label">{{ $label }} @if($required)<span class="text-danger">*</span>@endif</label>
    <input
        id="{{ $inputId }}"
        type="{{ $type }}"
        name="{{ $name }}"
        value="{{ old($name, $value) }}"
        class="form-control {{$class}} @error($name) is-invalid @enderror"
        @if($required) required @endif>
    @error($name)
    <div class="invalid-feedback">
        {{ $message }}
    </div>
    @enderror
</div>