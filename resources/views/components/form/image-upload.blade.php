@props([
'label' => 'Chọn ảnh',
'name',
'currentImage' => null,
'required' => false,
])

@php
$inputId = 'input-' . $name;
$previewId = $inputId . '-preview';
$defaultImage = asset('images/no-image.jpg');
$imageUrl = old($name, $currentImage ? asset($currentImage) : '');
@endphp

<div class="mb-4">
    <label class="form-label d-block mb-2">{{ $label }} @if($required)<span class="text-danger">*</span>@endif</label>
    <div class="card p-2">
        <input type="hidden"
            id="{{ $inputId }}"
            name="{{ $name }}"
            value="{{ $imageUrl }}"
            @if($required) required @endif>

        <div class="d-flex flex-column align-items-center">
            <img id="{{ $previewId }}"
                src="{{ $imageUrl ?: $defaultImage }}"
                class="img-thumbnail mb-2"
                style="width: 250px; height: 200px; object-fit: cover; box-shadow: 0 0 6px rgba(0,0,0,0.1); transition: all 0.3s ease;">

            <div class="d-flex gap-2">
                <button type="button"
                    class="btn btn-outline-primary"
                    onclick="window.openCKFinder('{{ $inputId }}', '{{ $previewId }}')">
                    <i class="bi bi-folder2-open"></i> Chọn ảnh
                </button>

                <button type="button"
                    class="btn btn-outline-danger {{ $imageUrl ? '' : 'd-none' }}"
                    id="{{ $inputId }}-remove"
                    onclick="window.removePickedImage('{{ $inputId }}', '{{ $previewId }}', '{{ $defaultImage }}')">
                    <i class="bi bi-x-circle"></i> Xóa ảnh
                </button>
            </div>
        </div>


    </div>
    @error($name)
    <div class="invalid-feedback d-block mt-2 text-center">{{ $message }}</div>
    @enderror
</div>