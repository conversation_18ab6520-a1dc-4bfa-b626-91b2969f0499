@props(['label', 'name', 'currentImage' => null, 'required' => false])

@php
    $inputId = 'input-' . $name;
    $error = $errors->first($name);
    $defaultImage = asset('images/no-image.jpg');
@endphp

<div class="mb-3">
    <label for="{{ $inputId }}" class="form-label">{{ $label }} @if($required)<span class="text-danger">*</span>@endif</label>
    <input
        id="{{ $inputId }}"
        type="file"
        accept="image/*"
        name="{{ $name }}"
        class="form-control @error($name) is-invalid @enderror"
        @if($required) required @endif
        onchange="previewImage(event, '{{ $inputId }}-preview', '{{ $inputId }}-remove-button')"
    >

    @error($name)
        <div class="invalid-feedback">
            {{ $message }}
        </div>
    @enderror

    <div class="mt-2 text-center">
        <img id="{{ $inputId }}-preview"
             src="{{ $currentImage ? asset($currentImage) : $defaultImage }}"
             alt="Hình ảnh"
             class="img-thumbnail"
             style="width: 200px; height: auto; object-fit: cover;">

        @if($currentImage)
            <button type="button"
                    id="{{ $inputId }}-remove-button"
                    class="btn btn-danger btn-sm mt-2 d-block mx-auto"
                    onclick="removeImage('{{ $inputId }}', '{{ $inputId }}-preview', '{{ $defaultImage }}', '{{ $name }}_removed')"
                    style="width: 150px;">
                Xóa ảnh hiện tại
            </button>
        @else
            <button type="button"
                    id="{{ $inputId }}-remove-button"
                    class="btn btn-danger btn-sm mt-2 d-block mx-auto d-none" 
                    onclick="removeImage('{{ $inputId }}', '{{ $inputId }}-preview', '{{ $defaultImage }}', '{{ $name }}_removed')"
                    style="width: 150px;">
                Xóa ảnh
            </button>
        @endif

        <input type="hidden" name="{{ $name }}_removed" id="{{ $name }}_removed" value="0">
    </div>
</div>