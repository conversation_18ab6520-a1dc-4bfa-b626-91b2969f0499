@props([
'label',
'name',
'options' => [],
'selected' => '',
'optionValue' => 'id',
'optionLabel' => 'name',
'placeholder' => '-- Chọn --',
'allowClear' => true,
'ajaxUrl' => null,
'isMultiple' => false,
'minLength' => 1,
'optionAttributes'=>null
])

<div class="mb-3">
    <label for="{{ $name }}" class="form-label">{{ $label }}</label>
    <select
        name="{{ $name }}{{ $isMultiple ? '[]' : '' }}"
        id="{{ $name }}"
        class="form-control select2-initialized @error($name) is-invalid @enderror"
        data-placeholder="{{ $placeholder }}"
        data-allow-clear="{{ $allowClear ? 'true' : 'false' }}"
        @if($ajaxUrl) data-ajax-url="{{ $ajaxUrl }}" @endif
        @if($ajaxUrl) data-min-length="{{ $minLength }}" @endif
        @if($isMultiple) multiple="multiple" @endif>
        @if (!$isMultiple)
        <option></option>
        @endif
        @foreach ($options as $option)
        <option
            value="{{ $option[$optionValue] }}"
            {{ old($name, $selected) == $option[$optionValue] ? 'selected' : '' }}
            @if (is_array($optionAttributes))
            @foreach ($optionAttributes as $attr=> $key)
            @php
            $value = is_callable($key)
            ? $key($option)
            : ($option[$key] ?? '');
            @endphp
            data-{{ $attr }}="{{ $value }}"
            @endforeach
            @endif
            >
            {{ $option[$optionLabel] }}
        </option>
        @endforeach
    </select>
    @error($name)
    <div class="invalid-feedback">
        {{ $message }}
    </div>
    @enderror
</div>