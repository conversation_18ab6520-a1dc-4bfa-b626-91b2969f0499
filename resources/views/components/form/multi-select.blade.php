@props([
    'id' => null,
    'label' => '',
    'name',
    'options' => [],
    'selected' => [],
    'placeholder' => 'Chọn...',
    'valueKey' => 'value',
    'labelKey' => 'label',
])
@php
$selectId = $id ?? 'select_' . uniqid();
@endphp
<div class="form-group">
    @if($label)
    <label for="{{ $selectId }}" class="form-label">{{ $label }}</label>
    @endif
    <select id="{{ $selectId }}" class="d-none" name="{{ $name }}[]" multiple>
        @foreach($options as $option)
        <option value="{{ $option[$valueKey] }}" @if(in_array($option[$valueKey], $selected)) selected @endif>
            {{ $option[$labelKey] }}
        </option>
        @endforeach
    </select>
</div>

@push('scripts')
<script>
    window.multiSelectChoices = window.multiSelectChoices || [];
    window.multiSelectChoices.push({
        id: '{{ $selectId }}',
        placeholder: '{{ $placeholder }}'
    });
</script>
@endpush