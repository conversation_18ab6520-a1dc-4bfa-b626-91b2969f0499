@props([
    'name' => 'color',
    'label' => 'Mã màu',
    'value' => '#000000',
    'required' => false,
])

<div class="mb-3">
    <label for="{{ $name }}" class="form-label">
        {{ $label }}
        @if ($required)
            <span class="text-danger">*</span>
        @endif
    </label>
    <div class="input-group">
        <input
            type="text"
            class="form-control @error($name) is-invalid @enderror"
            id="{{ $name }}"
            name="{{ $name }}"
            value="{{ old($name, $value) }}"
            placeholder="#RRGGBB"
            pattern="^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$"
            {{ $attributes->except(['label', 'required']) }} {{-- <PERSON><PERSON> truyền thêm các thuộc tính khác từ cha --}}
        >
        <input
            type="color"
            class="form-control form-control-color"
            id="{{ $name }}-picker"
            value="{{ old($name, $value) }}"
            title="Chọn màu"
            style="max-width: 45px; height: auto;"
        >
    </div>
    {{-- Hi<PERSON><PERSON> thị lỗi validation từ Laravel --}}
    @error($name)
        <div class="invalid-feedback d-block">
            {{ $message }}
        </div>
    @enderror
</div>

