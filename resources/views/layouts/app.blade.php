<!DOCTYPE html>
<html lang="vi">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>Beauti - @yield('title')</title>
    <link rel="icon" type="image/png" href="{{ asset('images/logo.svg') }}" />
    @vite(['resources/scss/styles.scss', 'resources/js/app.js'])
</head>

<body
    data-page="{{ $pageId ?? '' }}"
    class="{{ $pageName ?? '' }}"
    @if (session()->has('toast_success'))
    data-toast-success="{{ session('toast_success') }}"
    @endif
    @if (session()->has('toast_error'))
    data-toast-error="{{ session('toast_error') }}"
    @endif
    >
    <button id="scrollTopBtn" title="<PERSON>ên đầu trang">
        <i class="mdi mdi-arrow-up"></i>
    </button>
    @include('partials.header')
    <main>
        @yield('content')
    </main>
    @include('partials.auth.register-modal')
    @include('partials.auth.login-modal')
    @include('partials.auth.verifi-modal')
    @include('partials.auth.phone-verification-modal')
    @include('partials.footer')
    @stack('scripts')
</body>

</html>