@extends('layouts.app')

@section('title', 'Tin tức')
@section('meta_description', 'Danh sách tin tức')

@section('content')
<div class="beauti-service-page">
    @include('components.search-bar', ['pageName' => 'beauti-type'])
    <div class="container wide">
        <div class="row">
            <div class="col col-md-12 p-3">
                <div class="breadcrumb d-flex align-items-center gap-1">
                    <a href="{{route('home')}}">Trang chủ</a>
                    <span> / </span>
                    <a href="javascript:void(0)" class="breadcrumb--current">Tin tức</a>
                </div>
            </div>
        </div>
        <div class="row row-gap-3 py-3">
            <div class="col-12">
                <h2 class="box-head">
                    Tin tức
                </h2>
            </div>
            <div class="col-12 col-md-9">
                <div class="row row-gap-3">
                    @forelse($articles as $article)
                    <div class="col-12">
                        @include('partials.items.news-item', ['article' => $article])
                    </div>
                    @empty
                    <div class="col col-md-12 text-center py-5 text-muted">
                        Không có bài viết nào.
                    </div>
                    @endforelse
                    <div class="col-12 mt-4 d-flex justify-content-end">
                        {{ $articles->onEachSide(2)->links('vendor.pagination.bootstrap-5') }}
                    </div>
                </div>
            </div>
            <div class="col-12 col-md-3">
                <div class="row row-gap-3">
                    <div class="col-12">
                        <h2 class="box-head">Bài mới đăng</h2>
                    </div>
                    @forelse($articles as $article)
                    <div class="col-12">
                        @include('partials.items.news-item', ['article' => $article,'class' => 'flex-column'])
                    </div>
                    @empty
                    <div class="col col-md-12 text-center py-5 text-muted">
                        Không có bài viết nào.
                    </div>
                    @endforelse
                    @if($articles->count() > 4)
                    <div class="col col-md-12 text-center mt-4">
                        <button class="button button--outline button--hover d-inline-flex d-flex align-items-center gap-1 flex-shrink-0 text-nowrap mb-5">Xem thêm</button>
                    </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
@endsection