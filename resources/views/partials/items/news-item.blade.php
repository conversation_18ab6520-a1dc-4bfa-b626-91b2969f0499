<div class="news--item d-flex {{ $class ?? '' }}">
    <a href="#" class="box-image">
        <img src="{{ $article->image ?? '' }}" alt="{{ $article->title }}">
    </a>
    <div class="news--content">
        <a href="#">
            <h3 class="news--title">{{ $article->title }}</h3>
        </a>
        <div class="d-flex align-items-center gap-2">
            @foreach ($article->categories as $category)
            <span>{{ $category->title }}</span>@if (!$loop->last), @endif
            @endforeach
            <span><svg xmlns="http://www.w3.org/2000/svg" width="4" height="4" viewBox="0 0 4 4" fill="none">
                    <circle cx="2" cy="2" r="2" fill="#D9D9D9" />
                </svg></span>
            <span>{{$article->creator->username}}</span>
        </div>
        <div class="news--desc">
            {{ $article->excerpt ?? Str::limit(strip_tags(html_entity_decode($article->description)), 120) }}
        </div>
    </div>
</div>