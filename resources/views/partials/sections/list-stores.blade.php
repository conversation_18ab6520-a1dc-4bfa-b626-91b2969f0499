<div class="list-stores">
    <div class="container wide">
        <div class="row">
            <div class="col col-md-3">
                <div class="price-filter p-4 ">
                    <h5 class="mb-4 price-filter--text">Giá</h5>

                    <div class="position-relative mb-3 price-slider-wrapper">
                        <input type="range" class="form-range range-min" min="0" max="10000000" step="100000" value="0">
                        <input type="range" class="form-range range-max" min="0" max="10000000" step="100000" value="5000000">

                        <div class="slider-track"></div>
                        <div class="thumb thumb-min"><span class="thumb-value">0₫</span></div>
                        <div class="thumb thumb-max"><span class="thumb-value">5 000 000₫</span></div>
                    </div>

                    <div class="d-flex">
                        <input type="number" class="form-control me-2 input-min" value="0">
                        <input type="number" class="form-control input-max" value="5000000">
                    </div>
                </div>
                <div class="banner mt-2 d-flex flex-column gap-2">
                    <a href="#"> <img class="" src="{{asset('images/banner/banner1.jpg')}}" alt=""></a>
                    <a href="#"> <img class="" src="{{asset('images/banner/banner2.jpg')}}" alt=""></a>
                </div>
            </div>
            <div class="col col-md-9">
                <div class="list-stores--types">
                    <div class="row row-gap-3">
                        <div class="col-col-md-12">
                            <div class="box-head">{{$provinceName}} tìm thấy <span>{{ $stores->count() }} kết quả</span></div>
                            <p>Địa điểm làm đẹp xu hướng cho bạn tại thời điểm này</p>
                        </div>
                        <div class="col col-md-12">
                            <div class="line"></div>
                        </div>
                        <div class="col col-md-12 d-flex align-items-center gap-2">
                            <label for="">Sắp xếp theo</label>
                            <div class="sort-dropdown dropdown">
                                <button class="btn-filter dropdown-toggle" type="button" id="sortDropdownMenuButton" data-bs-toggle="dropdown" aria-expanded="false">
                                    Mặc định
                                </button>
                                <ul class="dropdown-menu" aria-labelledby="sortDropdownMenuButton">
                                    <li><a class="dropdown-item active" href="#" data-sort-value="default">Mặc định</a></li>
                                    <li><a class="dropdown-item" href="#" data-sort-value="price-asc">Giá tăng dần</a></li>
                                    <li><a class="dropdown-item" href="#" data-sort-value="price-desc">Giá giảm dần</a></li>
                                    <li><a class="dropdown-item" href="#" data-sort-value="newest">Mới nhất</a></li>
                                </ul>
                            </div>
                        </div>
                        <div class="col col-md-12 d-flex align-items-center gap-2">
                            <div class="list-items w-100">
                                <div class="row row-gap-3">
                                    @forelse($stores as $store)
                                    <div class="col col-md-4">
                                        @include('partials.items.beauti-store', ['store' => $store])
                                    </div>
                                    @empty
                                    <div class="text-center w-100 py-4">Không có kết quả phù hợp.</div>
                                    @endforelse
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>