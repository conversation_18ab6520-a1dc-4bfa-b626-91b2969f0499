@php
$hasServices = isset($listServices) && $listServices->count() > 0;
@endphp

<div class="section store__list--services">
    <div class="container wide">
        <div class="row">
            <div class="col col-md-9 h-100">
                <div class="list__services bg-white p-4">
                    <div class="row row-gap-3">
                        <div class="col col-md-12">
                            <h2 class="box-head">
                                Dịch vụ tại <span>{{$store->name}}</span>
                            </h2>
                        </div>
                        @if($hasServices)
                        <div class="row">
                            <div class="col col-md-12 d-flex flex-wrap algin-items-center gap-3">
                                <span class="service-item active" data-service="all">
                                    Tất cả
                                </span>
                                @foreach($listServices as $service)
                                <span class="service-item" data-service="{{ $service->id }}">
                                    {{ $service->name }}
                                </span>
                                @endforeach
                            </div>
                            @if(isset($listPackages) && $listPackages->count() > 0)
                                <div class="col col-12 mt-4">
                                    <div class="list__service--package">
                                        <div class="row row-gap-3">
                                            @foreach($listPackages as $package)
                                            <div class="col col-md-4">
                                                @include('partials.items.beauti-service',['package'=>$package])
                                            </div>
                                            @endforeach
                                        </div>
                                    </div>
                                </div>
                                @if($listPackages->count() > 9)
                                <div class="col col-md-12 text-center mt-4">
                                    <button class="button button--outline button--hover d-inline-flex d-flex align-items-center gap-1 flex-shrink-0 text-nowrap mb-5">Xem thêm</button>
                                </div>
                                @endif
                            @else
                                <div class="col col-12 mt-4">
                                    <p class="text-muted">Chưa có gói dịch vụ nào để hiển thị.</p>
                                </div>
                            @endif
                        </div>
                        @else
                        <p class="text-muted">Chưa có dịch vụ nào để hiển thị.</p>
                        @endif
                    </div>
                </div>
            </div>
            <div class="col col-md-3">
                <div class="list__service--area bg-white p-4">
                    <p class="discover--text">Khám phá khu vực</p>
                    <div class="box-map my-2">
                        <iframe src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3024.2820702687022!2d107.58396657388299!3d16.453281029044703!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x3141a1cf126d7839%3A0xeb03ce766bd36056!2zQ8O0bmcgdHkgVE5ISCBUcuG7sWMgdHV54bq_biBPSEk!5e1!3m2!1svi!2s!4v1749788954485!5m2!1svi!2s" style="border:0; width: 100%; height: 300px;" allowfullscreen="" loading="lazy" referrerpolicy="no-referrer-when-downgrade"></iframe>
                    </div>
                    <div class="list--nearby">
                        <div class="row row-gap-2">
                            <div class="col col-md-12">
                                <div class="d-flex align-item-center justify-content-between">
                                    <div class="d-flex align-items-center gap-2">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="21" viewBox="0 0 16 21" fill="none">
                                            <path d="M7.17783 19.9622C1.12375 11.7341 0 10.8897 0 7.86572C0 3.72357 3.58171 0.365723 8 0.365723C12.4183 0.365723 16 3.72357 16 7.86572C16 10.8897 14.8763 11.7341 8.82217 19.9622C8.42488 20.5002 7.57508 20.5002 7.17783 19.9622ZM8 10.9907C9.84096 10.9907 11.3333 9.59162 11.3333 7.86572C11.3333 6.13982 9.84096 4.74072 8 4.74072C6.15904 4.74072 4.66667 6.13982 4.66667 7.86572C4.66667 9.59162 6.15904 10.9907 8 10.9907Z" fill="#331559" />
                                        </svg>
                                        <span class="loction-title">
                                            Hồ gươm
                                        </span>
                                    </div>
                                    <div class="travel-time">
                                        1km - 10 phút đi bộ
                                    </div>
                                </div>
                            </div>
                            <div class="col col-md-12">
                                <div class="d-flex align-item-center justify-content-between">
                                    <div class="d-flex align-items-center gap-2">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="21" viewBox="0 0 16 21" fill="none">
                                            <path d="M7.17783 19.9622C1.12375 11.7341 0 10.8897 0 7.86572C0 3.72357 3.58171 0.365723 8 0.365723C12.4183 0.365723 16 3.72357 16 7.86572C16 10.8897 14.8763 11.7341 8.82217 19.9622C8.42488 20.5002 7.57508 20.5002 7.17783 19.9622ZM8 10.9907C9.84096 10.9907 11.3333 9.59162 11.3333 7.86572C11.3333 6.13982 9.84096 4.74072 8 4.74072C6.15904 4.74072 4.66667 6.13982 4.66667 7.86572C4.66667 9.59162 6.15904 10.9907 8 10.9907Z" fill="#331559" />
                                        </svg>
                                        <span class="loction-title">
                                            Hồ gươm
                                        </span>
                                    </div>
                                    <div class="travel-time">
                                        1km - 10 phút đi bộ
                                    </div>
                                </div>
                            </div>
                            <div class="col col-md-12">
                                <div class="d-flex align-item-center justify-content-between">
                                    <div class="d-flex align-items-center gap-2">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="21" viewBox="0 0 16 21" fill="none">
                                            <path d="M7.17783 19.9622C1.12375 11.7341 0 10.8897 0 7.86572C0 3.72357 3.58171 0.365723 8 0.365723C12.4183 0.365723 16 3.72357 16 7.86572C16 10.8897 14.8763 11.7341 8.82217 19.9622C8.42488 20.5002 7.57508 20.5002 7.17783 19.9622ZM8 10.9907C9.84096 10.9907 11.3333 9.59162 11.3333 7.86572C11.3333 6.13982 9.84096 4.74072 8 4.74072C6.15904 4.74072 4.66667 6.13982 4.66667 7.86572C4.66667 9.59162 6.15904 10.9907 8 10.9907Z" fill="#331559" />
                                        </svg>
                                        <span class="loction-title">
                                            Hồ gươm
                                        </span>
                                    </div>
                                    <div class="travel-time">
                                        1km - 10 phút đi bộ
                                    </div>
                                </div>
                            </div>
                            <div class="col col-md-12">
                                <div class="text-load-more">Hiển thị thêm</div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="list__service--area bg-white p-4 mt-4">
                    @include('contacts.index')
                </div>
            </div>
        </div>
        <div class="row pt-4 row-gap-3">
            <div class="col col-md-12">
                <div class="intro__store p-4 bg-white">
                    <h2 class="box-head">
                        Giới thiệu về <span>{{$store->name}}</span>
                    </h2>
                    @if(blank($store->description))
                    <p class="text-muted">Chưa có mô tả cho cơ sở này.</p>
                    @else
                    <div class="store--desc mt-2">
                        {!! html_entity_decode($store->description) !!}
                    </div>
                    @endif
                </div>

            </div>
        </div>
    </div>
</div>