@unless($storesRelated->isEmpty())
<div class="section list-stores-location py-5">
    <div class="container wide">
        <div class="list-stores-location--inner">
            <div class="row g-3">
                <div class="col-12 text-center">
                    <h2 class="box-head text-white">
                        <PERSON><PERSON><PERSON> địa chỉ <span>làm đẹp khác tại {{ $provinceName }}</span>
                    </h2>
                </div>
                @foreach($storesRelated as $otherStore)
                <div class="col-12 col-md-3">
                    @include('partials.items.beauti-store', ['store' => $otherStore])
                </div>
                @endforeach
            </div>
            @if($storesRelated->count() >8)
            <div class="text-center mt-4">
                <a
                    href="{{ route('beautitype.stores', [
                            'type'     => optional($store->beauties->types->first())->slug,
                            'province' => $store->province_code,
                        ]) }}"
                    class="button button--outline button--hover d-inline-flex d-flex align-items-center gap-1 flex-shrink-0 text-nowrap text-white">
                    Xem thêm ->
                </a>
            </div>
            @endif
        </div>
    </div>
</div>
@endunless

@if($storesRelated->isEmpty())
<p class="text-center text-muted py-4">Hiện chưa có cơ sở nào khác tại {{ $provinceName }}.</p>
@endif