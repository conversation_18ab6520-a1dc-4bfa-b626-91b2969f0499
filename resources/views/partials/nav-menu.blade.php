<nav class="col-6 col-md-6 l-header__nav">
  <ul class="l-nav-menu js-nav-hide">
    @foreach($beautyTypes as $i => $type)
      @php
        $provinceCodes = collect();
        foreach($type->beauties as $beauty) {
          $provinceCodes = $provinceCodes->merge($beauty->stores->pluck('province_code'));
        }
        $provinceCodes = $provinceCodes->unique()->filter();
      @endphp
      @if($provinceCodes->isEmpty())
        @continue
      @endif

      <li class="menu-item has-children {{ request()->routeIs('beautitype.stores') && request()->route('type') === $type->slug ? 'active' : '' }}" data-index="{{ $i }}">
        <a href="#" class="menu-link">Top <span>{{ $type->name }}</span></a>
        <ul class="submenu">
          @foreach($provinceCodes as $code)
            <li class="submenu-item">
              <a href="{{ route('beautitype.stores', ['type' => $type->slug, 'province' => $code]) }}">
                Top <span>{{ $type->name }} {{ $provinceMap[$code] ?? $code }}</span>
              </a>
            </li>
          @endforeach
        </ul>
      </li>
    @endforeach

    <li class="menu-item see-more d-none">
      <a href="#" class="menu-link">Xem thêm</a>
      <ul class="submenu" id="seeMoreMenu"></ul>
    </li>

    <li class="menu-item"><a href="{{ route('news.index') }}" class="menu-link">Blog</a></li>
    <li class="menu-item"><a href="#" class="menu-link">Hướng dẫn</a></li>
  </ul>
</nav>
