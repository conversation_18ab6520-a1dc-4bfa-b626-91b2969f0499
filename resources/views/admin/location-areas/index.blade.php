@extends('admin.layouts.app')

@section('title', 'Quản lý Khu vực')
@php
$pageId = 'location-ward-page';
$columns = [
['data' => 'DT_RowIndex', 'name' => 'DT_RowIndex', 'title' => 'STT', 'orderable' => false, 'searchable' => false, 'className' => 'dt-center'],
['data' => 'name', 'name' => 'name', 'title' => 'Đường'],
['data' => 'full_address', 'name' => 'full_address','width'=>'250px', 'title' => 'Điạ chỉ'],
['data' => 'district.name', 'defaultContent' => '', 'name' => 'location_district_name', 'title' => 'Quận/huyện'],
['data' => 'province.name', 'defaultContent' => '', 'name' => 'location_province_name', 'title' => 'Tỉnh/TP'],
['data' => 'actions', 'name' => 'actions', 'title' => 'Hành động', 'orderable' => false, 'searchable' => false],
];
@endphp

@section('content')
<div class="pc-content">
    <div class="page-header">
        <div class="page-block">
            <div class="d-flex align-items-center justify-content-between">
                <div class="page-header-title">
                    <h2 class="mb-0">Khu vực</h2>
                </div>
                <a href="{{ route('admin.location-areas.create') }}" class="btn btn-success">+ Thêm mới</a>
            </div>
        </div>
    </div>
    <div class="card-body pb-4">
        <table class="data-table table table-striped table-bordered table-hover"
            data-url="{{ route('admin.location-areas.data') }}"
            data-columns='@json($columns)'>
        </table>
    </div>
</div>
@endsection