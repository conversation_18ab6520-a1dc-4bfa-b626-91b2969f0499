<form method="POST" action="{{ isset($item) ? route('admin.location-streets.update', $item->id) : route('admin.location-streets.store') }}"
    enctype="multipart/form-data">
    @csrf
    @if(isset($item))
    @method('PUT')
    @endif

    <div class="card mb-4">
        <div class="card-header">
            <strong>Thông tin</strong>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <x-form.input
                        label="Tên"
                        name="name"
                        :value="$item->name ?? ''" />
                </div>
                <div class="col-md-6">
                    <x-form.input
                        label="Mã"
                        name="code"
                        :value="$item->code ?? ''" />
                </div>
            </div>
            <div class="row">
                <div class="col-md-6">
                    <x-form.select
                        label="Tỉnh/Thành phố"
                        name="province_code"
                        :options="$locationProvince"
                        :selected="$item->province_code ?? ''"
                        optionValue="code"
                        optionLabel="name" />
                </div>
                <div class="col-md-6">
                    <x-form.select
                        label="Quận/huyện"
                        name="district_code"
                        :options="$selectedDistrict"
                        :selected="$item->district_code ?? ''"
                        optionValue="code"
                        optionLabel="name" />
                </div>
                <div class="col-md-6">
                    <x-form.select
                        label="Phường/xã/Thị trấn"
                        name="ward_code"
                        :options="$selectedWard"
                        :selected="$item->ward_code ?? ''"
                        optionValue="code"
                        optionLabel="name" />
                </div>
            </div>
        </div>
    </div>

    <div class="mb-3">
        <button type="submit" class="btn btn-primary">
            {{ isset($item) ? 'Cập nhật' : 'Tạo mới' }}
        </button>
        <a href="{{ route('admin.location-streets.index') }}" class="btn btn-secondary">Quay lại</a>
    </div>
</form>