<form method="POST" action="{{ isset($item) ? route('admin.beauti-stores.update', $item->id) : route('admin.beauti-stores.store') }}"
    enctype="multipart/form-data">
    @csrf
    @if(isset($item))
    @method('PUT')
    @endif

    <div class="card mb-4">
        <div class="card-header">
            <ul class="nav nav-tabs card-header-tabs" id="myTab" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="thong-tin-chinh-tab" data-bs-toggle="tab" data-bs-target="#thong-tin-chinh" type="button" role="tab" aria-controls="thong-tin-chinh" aria-selected="true">Thông tin chính</button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="dich-vu-tab" data-bs-toggle="tab" data-bs-target="#dich-vu" type="button" role="tab" aria-controls="dich-vu" aria-selected="false">Loại hình & Dịch vụ</button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="cau-hinh-tab" data-bs-toggle="tab" data-bs-target="#cau-hinh" type="button" role="tab" aria-controls="cau-hinh" aria-selected="false">Cấu hình</button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="seo-tab" data-bs-toggle="tab" data-bs-target="#seo" type="button" role="tab" aria-controls="seo" aria-selected="false">SEO</button>
                </li>
            </ul>
        </div>
        <div class="card-body">
            <div class="tab-content" id="myTabContent">
                <div class="tab-pane fade show active" id="thong-tin-chinh" role="tabpanel" aria-labelledby="thong-tin-chinh-tab">
                    <div class="row">
                        <div class="col-md-12">
                            <x-form.input
                                label="Tên cơ sở"
                                name="name"
                                :value="old('name', $item->name ?? '')" />
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-12">
                            <x-form.select
                                label="Beauti"
                                name="beauti_id"
                                :options="$beauties"
                                :selected="old('beauti_id', $item->beauti_id ?? '')"
                                optionValue="id"
                                optionLabel="name" />
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-12">
                            <x-form.input
                                :type="'number'"
                                class="form-control price-input"
                                label="Giá khởi điểm từ (VND)"
                                name="price_from"
                                :value="old('price_from', $item->price_from ?? '')" />
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-12">
                            <x-form.input
                                :type="'time'"
                                class="form-control"
                                label="Giờ mở cửa"
                                name="opening_time"
                                :value="old('opening_time', $item->opening_time ?? '')" />
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-12">
                            <x-form.input
                                :type="'time'"
                                class="form-control"
                                label="Giờ đóng cửa"
                                name="closing_time"
                                :value="old('closing_time', $item->closing_time ?? '')" />
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-12">
                            <x-form.image-upload
                                label="Hình ảnh"
                                label="Logo cơ sở"
                                name="logo"
                                :current-image="$item->logo ?? null" />
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-12">
                            <x-form.image-upload
                                label="Hình ảnh cơ sở"
                                name="image"
                                :current-image="$item->image ?? null" />
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <x-form.select
                                label="Tỉnh/Thành phố"
                                name="province_code"
                                :options="$locationProvince"
                                :selected="$item->province_code ?? ''"
                                optionValue="code"
                                optionLabel="name" />
                        </div>
                        <div class="col-md-6">
                            <x-form.select
                                label="Quận/huyện"
                                name="district_code"
                                :options="$selectedDistrict"
                                :selected="$item->district_code ?? ''"
                                optionValue="code"
                                optionLabel="name" />
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <x-form.select
                                label="Phường/xã/Thị trấn"
                                name="ward_code"
                                :options="$selectedWard"
                                :selected="$item->ward_code ?? ''"
                                optionValue="code"
                                optionLabel="name" />
                        </div>
                        <div class="col-md-6">
                            <x-form.select
                                label="Đường"
                                name="street"
                                :options="$selectedStreet"
                                :selected="$item->street ?? ''"
                                optionValue="code"
                                optionLabel="name" />
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-12">
                            <x-form.input
                                label="Địa chỉ"
                                name="full_address"
                                :value="$item->full_address ?? ''" />
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-12">
                            <x-form.editor
                                label="Mô tả"
                                name="description"
                                :value="$item->description ?? ''" />
                        </div>
                    </div>
                </div>

                <div class="tab-pane fade" id="dich-vu" role="tabpanel">
                    <template id="service-item-template">
                        @include('admin.components.service-component', [
                        'index' => '__INDEX__',
                        'pivotId' => '__PIVOT_ID__',
                        'serviceId'=> '',
                        'priceFrom'=> '',
                        'status' => true,
                        'services' => $beautiServices->pluck('name','id')->toArray()
                        ])
                    </template>

                    <div class="row mb-3">
                        <x-form.multi-select
                            label="Chọn loại hình áp dụng cho cơ sở"
                            name="beauti_types"
                            id="type_ids_select"   
                            :options="$beautiTypes"
                            :selected="$selectedTypes ?? []"
                            valueKey="id"
                            labelKey="name"
                            placeholder="Chọn loại hình..." />
                    </div>

                    <div id="services-container">
                        @php
                        $existingServices = old('services', []);
                        if(isset($item) && empty(old('services'))) {
                        $existingServices = collect($item->services)->map(fn($s) => [
                        'id' => $s->pivot->id,
                        'service_id' => $s->id,
                        'price_from' => $s->pivot->price_from,
                        'status' => $s->pivot->status,
                        ])->toArray();
                        }
                        @endphp

                        @foreach($existingServices as $idx => $svc)
                        @include('admin.components.service-component', [
                        'index' => $idx,
                        'pivotId' => $svc['id'] ?? '',
                        'serviceId' => $svc['service_id'] ?? '',
                        'priceFrom' => $svc['price_from'] ?? '',
                        'status' => $svc['status'] ?? false,
                        'services' => $beautiServices->pluck('name','id')->toArray()
                        ])
                        @endforeach
                    </div>

                    <button type="button" id="add-service" class="btn btn-info btn-sm mt-3">
                        Thêm dịch vụ mới
                    </button>
                </div>

                <div class="tab-pane fade" id="cau-hinh" role="tabpanel" aria-labelledby="cau-hinh-tab">
                    <div class="row">
                        <div class="col-md-12">
                            <x-form.input
                                label="Slug"
                                name="slug"
                                :value="old('slug', $item->slug ?? '')" />
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-12">
                            <x-form.select
                                label="Trạng thái"
                                name="status"
                                :options="[
                                    ['id' => '1', 'name' => 'Kích hoạt'],
                                    ['id' => '0', 'name' => 'Ẩn']
                                ]"
                                :selected="old('status', $item->status ?? '1')" />
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-12">
                            <x-form.input
                                label="Thứ tự"
                                name="position"
                                type="number"
                                :value="old('position', $item->position ?? '')" />
                        </div>
                    </div>
                </div>

                <div class="tab-pane fade" id="seo" role="tabpanel" aria-labelledby="seo-tab">
                    <div class="row">
                        <div class="col-md-12">
                            <x-form.input
                                label="Tiêu đề SEO"
                                name="seo_title"
                                :value="old('seo_title', $item->seo_title ?? '')" />
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-12">
                            <x-form.textarea
                                label="Mô tả SEO"
                                name="seo_description"
                                :value="old('seo_description', $item->seo_description ?? '')" />
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-12">
                            <x-form.textarea
                                label="Từ khóa SEO"
                                name="seo_keywords"
                                :value="old('seo_keywords', $item->seo_keywords ?? '')" />
                        </div>
                    </div>
                    <div class="col-md-12">
                        <x-form.image-upload
                            label="Hình ảnh"
                            name="seo_image"
                            :current-image="$item->seo_image ?? null" />
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="mb-3">
        <button type="submit" class="btn btn-primary">
            {{ isset($item) ? 'Cập nhật' : 'Tạo mới' }}
        </button>
        <a href="{{ route('admin.beauti-stores.index') }}" class="btn btn-secondary">Quay lại</a>
    </div>
</form>