<form method="POST"
    action="{{ isset($item)
          ? route('admin.beauti-packages.update', $item)
          : route('admin.beauti-packages.store') }}"
    enctype="multipart/form-data">
    @csrf
    @if(isset($item)) @method('PUT') @endif

    <div class="card mb-4">
        <div class="card-header">
            <ul class="nav nav-tabs card-header-tabs" id="tab-packages" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="thong-tin-chinh-tab" data-bs-toggle="tab" data-bs-target="#thong-tin-chinh" type="button" role="tab" aria-controls="thong-tin-chinh" aria-selected="true">Thông tin chính</button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="cau-hinh-tab" data-bs-toggle="tab" data-bs-target="#cau-hinh" type="button" role="tab" aria-controls="cau-hinh" aria-selected="false">Cấu hình</button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="seo-tab" data-bs-toggle="tab" data-bs-target="#seo" type="button" role="tab" aria-controls="seo" aria-selected="false">SEO</button>
                </li>
            </ul>
        </div>
        <div class="card-body">
            <div class="tab-content">
                <div class="tab-pane fade show active" id="thong-tin-chinh">
                    <div class="row">
                        <div class="col-md-4">
                            <x-form.select
                                label="Beauti"
                                name="business_id"
                                :options="$beauties"
                                :selected="old('business_id', $item->store->beauties->id ?? '')"
                                optionValue="id"
                                optionLabel="name"
                                id="select-business"
                                data-old="{{ old('business_id', $item->beauties->id ?? '') }}" />
                        </div>
                        <div class="col-md-4">
                            <x-form.select
                                label="Cơ sở"
                                name="store_id"
                                :options="isset($stores) ? $stores : []"
                                :selected="old('store_id', $item->store->id ?? '')"
                                optionValue="id"
                                optionLabel="name"
                                id="select-store"
                                data-old="{{ old('store_id', $item->store->id ?? '')"
                                />
                        </div>
                        <div class="col-md-4">
                            <x-form.select
                                label="Dịch vụ"
                                name="service_id"
                                :options="isset($services) ? $services : []"
                                :selected="old('service_id',$item->service->id ?? '')"
                                optionValue="id"
                                optionLabel="name"
                                id="select-service" 
                                data-old="{{ old('service_id', $item->service->id ?? '') }}" />
                        </div>
                    </div>

                    <div class="row mt-3">
                        <div class="col-md-12">
                            <x-form.input
                                label="Tên gói"
                                name="name"
                                :value="old('name', $item->name ?? '')" />
                        </div>

                    </div>
                    <div class="row mt-3">
                        <div class="col-md-6">
                            <x-form.input
                                type="number"
                                label="Giá mặc định (₫)"
                                name="price_old"
                                :value="old('price_old', $item->price_old ?? '')" />
                        </div>
                        <div class="col-md-6">
                            <x-form.input
                                type="number"
                                label="Giá ưu đãi (₫)"
                                name="price_new"
                                :value="old('price_new', $item->price_old ?? '')" />
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-md-6">
                            <x-form.input
                                type="number"
                                label="Số buổi làm (ngày)"
                                name="duration_days"
                                :value="old('duration_days', $item->duration_days ?? '')" />
                        </div>
                        <div class="col-md-6">
                            <x-form.input
                                type="number"
                                label="Bào hành (ngày)"
                                name="warranty_days"
                                :value="old('warranty_days', $item->warranty_days ?? '')" />
                        </div>
                    </div>

                    <div class="row mt-3">
                        <div class="col-md-12">
                            <x-form.editor
                                label="Mô tả"
                                name="description"
                                :value="$item->description ?? ''" />
                        </div>
                    </div>
                </div>

                <div class="tab-pane fade" id="cau-hinh">
                    <div class="row">
                        <div class="col-md-12">
                            <x-form.input
                                label="Slug"
                                name="slug"
                                :value="old('slug', $item->slug ?? '')" />
                        </div>
                        <div class="col-md-12">
                            <x-form.select
                                label="Trạng thái"
                                name="status"
                                :options="[
                ['id'=>'1','name'=>'Kích hoạt'],
                ['id'=>'0','name'=>'Ẩn']
              ]"
                                :selected="old('status', $item->status ?? '1')" />
                        </div>
                    </div>
                </div>

                <div class="tab-pane fade" id="seo">
                    <x-form.input
                        label="Tiêu đề SEO"
                        name="seo_title"
                        :value="old('seo_title', $item->seo_title ?? '')" />
                    <x-form.textarea
                        label="Mô tả SEO"
                        name="seo_description"
                        :value="old('seo_description', $item->seo_description ?? '')" />
                    <x-form.textarea
                        label="Từ khóa SEO"
                        name="seo_keywords"
                        :value="old('seo_keywords', $item->seo_keywords ?? '')" />
                    <x-form.image-upload
                        label="Ảnh SEO"
                        name="seo_image"
                        :current-image="$item->seo_image ?? null" />
                </div>

            </div>
        </div>
    </div>

    <div class="mb-3">
        <button class="btn btn-primary">{{ isset($item) ? 'Cập nhật' : 'Tạo mới' }}</button>
        <a href="{{ route('admin.beauti-packages.index') }}" class="btn btn-secondary">Hủy</a>
    </div>
</form>