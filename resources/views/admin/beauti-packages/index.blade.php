@extends('admin.layouts.app')

@section('title', 'Quản lý gói dịch vụ')
@php
$pageId = 'beauti-store-package-service';
 $columns = [
        ['data' => 'DT_RowIndex', 'name' => 'DT_RowIndex', 'title' => 'STT', 'orderable' => false, 'searchable' => false, 'className' => 'dt-center'],
        ['data' => 'name', 'name' => 'name', 'title' => 'Tên gói dịch vụ'],
        ['data' => 'store.beauties.name','defaultContent' => '', 'name' => 'type_name', 'title' => 'Beauti'],
        ['data' => 'store.name', 'name' => 'store_name', 'title' => 'Cơ sở'],
        ['data' => 'service.name','defaultContent' => '', 'name' => 'type_name', 'title' => 'Dịch vụ'],
        ['data' => 'status', 'name' => 'status', 'title' => 'Hiển thị', 'className'=> 'dt-center'],
        ['data' => 'creator.username', 'defaultContent' => '', 'name' => 'creator_username', 'title' => 'Người tạo'],
        ['data' => 'actions', 'name' => 'actions', 'title' => 'Hành động', 'orderable' => false, 'searchable' => false],
    ];
@endphp

@section('content')
<div class="pc-content">
    <div class="page-header">
        <div class="page-block">
            <div class="d-flex align-items-center justify-content-between">
                <div class="page-header-title">
                    <h2 class="mb-0">Gói dịch vụ</h2>
                </div>
                <a href="{{ route('admin.beauti-packages.create') }}" class="btn btn-success">+ Thêm mới</a>
            </div>
        </div>
    </div>
    <div class="card-body pb-4">
        <table class="data-table table table-striped table-bordered table-hover"
            data-url="{{ route('admin.beauti-packages.data') }}"
            data-columns='@json($columns)'>
        </table>
    </div>
</div>
@endsection