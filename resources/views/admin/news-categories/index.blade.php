@extends('admin.layouts.app')

@section('title', 'Quản lý danh mục')
@php
$pageId = 'news-categories-page';
$columns = [
['data' => 'DT_RowIndex', 'name' => 'DT_RowIndex', 'title' => 'STT', 'orderable' => false, 'searchable' => false, 'className' => 'dt-center'],
['data' => 'title', 'name' => 'title', 'title' => 'Tiêu đề'],
['data' => 'parent.title', 'defaultContent' => '', 'name' => 'parent_name', 'title' => 'Thuộc danh mục'],
['data' => 'status', 'name' => 'status', 'title' => 'Hiển thị', 'className'=> 'dt-center'],
['data' => 'creator.username', 'defaultContent' => '', 'name' => 'creator_username', 'title' => 'Ngư<PERSON>i tạo'],
['data' => 'actions', 'name' => 'actions', 'title' => 'Hành động', 'orderable' => false, 'searchable' => false],
];
@endphp

@section('content')
<div class="pc-content">
    <div class="page-header">
        <div class="page-block">
            <div class="d-flex align-items-center justify-content-between">
                <div class="page-header-title">
                    <h2 class="mb-0">Danh mục tin tức</h2>
                </div>
                <a href="{{ route('admin.news-categories.create') }}" class="btn btn-success">+ Thêm mới</a>
            </div>
        </div>
    </div>
    <div class="card-body pb-4">
        <table class="data-table table table-striped table-bordered table-hover"
            data-url="{{ route('admin.news-categories.data') }}"
            data-columns='@json($columns)'>
        </table>
    </div>
</div>
@endsection