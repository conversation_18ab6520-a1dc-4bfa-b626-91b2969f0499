@props([
  'index',          
  'pivotId'  => '', 
  'serviceId'=> '',
  'priceFrom'=> '', 
  'status'   => 1, 
  'services' => [],
])

<div class="service-item card card-body mb-3" data-index="{{ $index }}">
  <input type="hidden" name="services[{{ $index }}][id]" value="{{ $pivotId }}">

  <div class="row">
    <div class="col-md-6 mb-3">
      <label class="form-label">Dịch vụ</label>
      <select class="form-select select2-service"
              name="services[{{ $index }}][service_id]"
              required>
        <option value="">Chọn dịch vụ</option>
        @foreach($services as $id => $name)
          <option value="{{ $id }}" @if($id == $serviceId) selected @endif>
            {{ $name }}
          </option>
        @endforeach
      </select>
    </div>
    <div class="col-md-4 mb-3">
      <label class="form-label"><PERSON>i<PERSON> khởi điểm (VND)</label>
      <input type="number"
             class="form-control price-input"
             name="services[{{ $index }}][price_from]"
             value="{{ $priceFrom }}">
    </div>
    <div class="col-md-2 d-flex align-items-center">
      <input type="hidden" name="services[{{ $index }}][status]" value="0">
      <div class="form-check form-switch">
        <input class="form-check-input"
               type="checkbox"
               id="service_status_{{ $index }}"
               name="services[{{ $index }}][status]"
               value="1" {{ $status ? 'checked' : '' }}>
        <label class="form-check-label" for="service_status_{{ $index }}">
          Kích hoạt
        </label>
      </div>
    </div>
  </div>

  <div class="d-flex justify-content-end mt-3">
    <button type="button" class="btn btn-danger btn-sm remove-service">
      Xóa dịch vụ này
    </button>
  </div>
</div>
