<form method="POST" action="{{ isset($item) ? route('admin.members.update', $item->id) : route('admin.members.store') }}"
    enctype="multipart/form-data">
    @csrf
    @if(isset($item))
    @method('PUT')
    @endif

    <div class="card mb-4">
        <div class="card-header">
            <strong>Thông tin cá nhân</strong>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <x-form.input
                        label="Họ"
                        name="lastname"
                        :value="$item->lastname ?? ''" />
                </div>
                <div class="col-md-6">
                    <x-form.input
                        label="Tên"
                        name="firstname"
                        :value="$item->firstname ?? ''" />
                </div>
            </div>

            <div class="row">
                <div class="col-md-6">
                    <x-form.input
                        label="Mã nhân viên"
                        name="code"
                        :value="$item->code ?? ''" />
                </div>
                <div class="col-md-6">
                    <x-form.select
                        label="Team"
                        name="department_id"
                        :options="$departments"
                        :selected="$item->department_id ?? ''"
                        optionValue="id"
                        optionLabel="name" />
                </div>
            </div>

            <div class="row">
                <div class="col-md-6">
                    <x-form.input
                        label="Chức vụ"
                        name="title"
                        :value="$item->title ?? ''" />
                </div>
                <div class="col-md-6">
                    <x-form.input
                        label="Email"
                        name="email"
                        type="email"
                        :value="$item->email ?? ''" />
                </div>
            </div>

            <div class="row">
                <div class="col-md-6">
                   <x-form.select
                        label="Giới tính"
                        name="gender"
                        :options="[
                                ['id' => 'male', 'name' => 'Nam'],
                                ['id' => 'female', 'name' => 'Nữ'],
                                ['id' => 'other', 'name' => 'Khác'],
                            ]"
                        :selected="$item->gender ?? null" />
                </div>
                <div class="col-md-6">
                    <x-form.image-upload
                        label="Ảnh đại diện"
                        name="avatar"
                        :current-image="$item->avatar ?? null" />
                </div>
            </div>
        </div>
    </div>
    <div class="card mb-4">
        <div class="card-header">
            <strong>Tài khoản & quyền</strong>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <x-form.input
                        label="Tên đăng nhập"
                        name="username"
                        :value="$item->username ?? ''" />
                </div>
                <div class="col-md-6">
                    <x-form.multi-select
                        label="Vai trò"
                        name="roles"
                        :options="$roles"
                        :selected="$itemRoles ?? []"
                        valueKey="id"
                        labelKey="name"
                        placeholder="Chọn vai trò" />
                </div>
            </div>

            <div class="row">
                @if(isset($item))
                <div class="col-md-6 mb-3 d-flex align-items-end">
                    <button type="button" onclick="resetPassword(event)" data-action="reset-password" class="btn btn-warning">
                        Đặt lại mật khẩu
                    </button>
                </div>
                @endif
            </div>
            <div class="row">
                <div class="col-md-6">
                    <x-form.color-picker
                        name="color"
                        label="Mã màu"
                        :value="$item->color ?? '#000000'" />
                </div>
            </div>
            <div class="row">
                <div class="col-md-6">
                    <x-form.select
                        label="Kích hoạt"
                        name="status"
                        :options="[
                            ['id' => '1', 'name' => 'Có'],
                            ['id' => '0', 'name' => 'Không']
                                ]"
                        :selected="$item->status ?? '1'" />
                </div>
            </div>
        </div>
    </div>

    <div class="mb-3">
        <button type="submit" class="btn btn-primary">
            {{ isset($item) ? 'Cập nhật' : 'Tạo mới' }}
        </button>
        <a href="{{ route('admin.members.index') }}" class="btn btn-secondary">Quay lại</a>
    </div>
</form>
@if(isset($item))
<form id="reset-password-form" method="POST" action="{{ route('admin.members.reset-password', $item->id) }}" style="display: none;">
    @csrf
</form>
@endif