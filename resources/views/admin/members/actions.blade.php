<div class="btn-group" role="group" aria-label="Actions">
    @php
    $loggedInUserId = Auth::guard('admin')->id();
    $isCurrentUser = ($loggedInUserId == $row->id);
    @endphp

    <a href="{{ $isCurrentUser ? 'javascript:void(0);' : route('admin.members.edit', $row->id) }}"
        class="btn btn-sm btn-outline-primary me-1 {{ $isCurrentUser ? 'disabled' : '' }}"
        title="Sửa"
        @if ($isCurrentUser) aria-disabled="true" @endif>
        <i class="fa fa-edit"></i> Sửa
    </a>

    <a href="javascript:void(0);"
        class="btn btn-sm btn-outline-danger delete-btn {{ $isCurrentUser ? 'disabled' : '' }}"
        data-id="{{ $row->id }}"
        @if (!$isCurrentUser)
        data-delete-url="{{ route('admin.members.destroy', $row->id) }}"
        @endif
        data-name="{{ $row->fullname ?? $row->username }}"
        title="Xoá"
        @if ($isCurrentUser) aria-disabled="true" @endif>
        <i class="fa fa-trash"></i> Xoá
    </a>
</div>