@extends('admin.layouts.app')

@section('title', 'Quản lý thành viên')
@php
$pageId = 'member-page';
$columns = [
['data' => 'DT_RowIndex', 'name' => 'DT_RowIndex', 'title' => 'STT', 'orderable' => false, 'searchable' => false, 'className' => 'dt-center'],
['data' => 'fullname', 'name' => 'fullname', 'title' => 'Họ tên'],
['data' => 'username', 'name' => 'username', 'title' => 'Tài khoản'],
['data' => 'code', 'name' => 'code', 'title' => 'Mã'],
['data' => 'department.name', 'defaultContent' => '', 'name' => 'department_name', 'title' => 'Team'],
['data' => 'status', 'name' => 'status', 'title' => 'Hoạt động', 'className'=> 'dt-center'],
['data' => 'created_at_formatted', 'type' => 'datetime', 'name' => 'created_at', 'title' => 'Ngày tạo'],
['data' => 'actions', 'name' => 'actions', 'title' => 'Hành động', 'orderable' => false, 'searchable' => false],
];
@endphp

@section('content')
<div class="pc-content">
    <div class="page-header">
        <div class="page-block">
            <div class="d-flex align-items-center justify-content-between">
                <div class="page-header-title">
                    <h2 class="mb-0">Thành viên</h2>
                </div>
                <a href="{{ route('admin.members.create') }}" class="btn btn-success">+ Thêm mới</a>
            </div>
        </div>
    </div>
    <div class="card-body pb-4">
        <table class="data-table table table-striped table-bordered table-hover"
            data-url="{{ route('admin.members.data') }}"
            data-columns='@json($columns)'>
        </table>
    </div>
</div>
@endsection