<form method="POST" action="{{ isset($item) ? route('admin.beauties.update', $item->id) : route('admin.beauties.store') }}"
    enctype="multipart/form-data">
    @csrf
    @if(isset($item))
    @method('PUT')
    @endif

    <div class="card mb-4">
        <div class="card-header">
            <strong>Thông tin cá nhân</strong>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-12">
                    <x-form.input
                        label="Tên"
                        name="name"
                        :value="old('name', $item->name ?? '')" />
                </div>
            </div>
            <div class="row">
                <div class="col-md-12">
                    <x-form.multi-select
                        label="Chọn loại hình"
                        name="beauti_types"
                        :options="$types"
                        :selected="$selectedTypes ?? []"
                        valueKey="id"
                        labelKey="name"
                        placeholder="Chọn loại hình" />
                </div>
            </div>
            <div class="row">
                <div class="col-md-12">
                    <x-form.select
                        label="Host"
                        name="user_id"
                        :options="$users"
                        :selected="old('user_id', $item->user_id ?? '')"
                        optionValue="id"
                        optionLabel="fullname" 
                        />
                </div>
            </div>
            <div class="row">
                <div class="col-md-12">
                    <x-form.input
                        label="Địa chỉ"
                        name="address"
                        :value="$item->address ?? ''" />
                </div>
            </div>
            <div class="row">
                <div class="col-md-12">
                    <x-form.select
                        label="Trạng thái"
                        name="status"
                        :options="[
                                    ['id' => '1', 'name' => 'Kích hoạt'],
                                    ['id' => '0', 'name' => 'Ẩn']
                                ]"
                        :selected="old('status', $item->status ?? '1')" />
                </div>
            </div>
        </div>
    </div>
    <div class="mb-3">
        <button type="submit" class="btn btn-primary">
            {{ isset($item) ? 'Cập nhật' : 'Tạo mới' }}
        </button>
        <a href="{{ route('admin.beauties.index') }}" class="btn btn-secondary">Quay lại</a>
    </div>
</form>