@php
use App\Models\BeautiType;
use App\Models\BeautiService;
use App\Models\User;
use App\Models\LocationProvince;

$types = BeautiType::all();
$users =User::all();
$beautiServices =BeautiService::all();
$locationProvince = LocationProvince::all();
@endphp

@extends('admin.layouts.app')

@section('title', 'Beauti')
@section('subtitle', 'Thêm mới')
@section('content')
<div class="pc-content">
    <div class="page-header">
        <div class="page-header-title">
            <h2 class="mb-0">
                @yield('title')
                @hasSection('subtitle')
                <small class="text-muted">@yield('subtitle')</small>
                @endif
            </h2>
        </div>
    </div>
    <div class="page-form pb-4">
        @include('admin.beauties._form')
    </div>
</div>
@endsection