@extends('admin.layouts.app')

@section('title', 'Quản lý beauti')
@php
$pageId = 'beauti-store-page';
 $columns = [
        ['data' => 'DT_RowIndex', 'name' => 'DT_RowIndex', 'title' => 'STT', 'orderable' => false, 'searchable' => false, 'className' => 'dt-center'],
        ['data' => 'name', 'name' => 'name', 'title' => 'Tên'],
        ['data' => 'types_list', 'name' => 'types_list', 'title' => 'Loại hình'],
        ['data' => 'address', 'name' => 'address', 'title' => 'Địa chỉ'],
        ['data' => 'status', 'name' => 'status', 'title' => 'Hiển thị', 'className'=> 'dt-center'],
        ['data' => 'creator.username', 'defaultContent' => '', 'name' => 'creator_username', 'title' => 'Ngư<PERSON>i tạo'],
        ['data' => 'created_at_formatted', 'type' => 'datetime', 'name' => 'created_at', 'title' => 'Ngày tạo'],
        ['data' => 'actions', 'name' => 'actions', 'title' => 'Hành động', 'orderable' => false, 'searchable' => false],
    ];
@endphp

@section('content')
<div class="pc-content">
    <div class="page-header">
        <div class="page-block">
            <div class="d-flex align-items-center justify-content-between">
                <div class="page-header-title">
                    <h2 class="mb-0">Beauti</h2>
                </div>
                <a href="{{ route('admin.beauties.create') }}" class="btn btn-success">+ Thêm mới</a>
            </div>
        </div>
    </div>
    <div class="card-body pb-4">
        <table class="data-table table table-striped table-bordered table-hover"
            data-url="{{ route('admin.beauties.data') }}"
            data-columns='@json($columns)'>
        </table>
    </div>
</div>
@endsection