<form method="POST" action="{{ isset($item) ? route('admin.location-wards.update', $item->id) : route('admin.location-wards.store') }}"
    enctype="multipart/form-data">
    @csrf
    @if(isset($item))
    @method('PUT')
    @endif

    <div class="card mb-4">
        <div class="card-header">
            <strong>Thông tin</strong>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <x-form.input
                        label="Tên"
                        name="name"
                        :value="$item->name ?? ''" />
                </div>
                <div class="col-md-6">
                    <x-form.input
                        label="Mã"
                        name="code"
                        :value="$item->code ?? ''" />
                </div>
            </div>
            <div class="row">
                <div class="col-md-6">
                    <x-form.select
                        label="Loại"
                        name="type"
                        :options="[
                                ['id' => 'Xã', 'name' => 'Xã'],
                                ['id' => 'Phường', 'name' => 'Phường'],
                                ['id' => 'Thị trấn', 'name' => 'Thị trấn']
                            ]"
                        :selected="$item->type ?? ''" />
                </div>
                <div class="col-md-6">
                    <x-form.select
                        label="Quân/huyện"
                        name="district_code"
                        :options="$locationDistrict"
                        :selected="$item->district_code ?? ''"
                        optionValue="code"
                        optionLabel="name" />
                </div>
            </div>
        </div>
    </div>

    <div class="mb-3">
        <button type="submit" class="btn btn-primary">
            {{ isset($item) ? 'Cập nhật' : 'Tạo mới' }}
        </button>
        <a href="{{ route('admin.location-wards.index') }}" class="btn btn-secondary">Quay lại</a>
    </div>
</form>