@php
use App\Models\NewsCategory;
$categories = NewsCategory::getFormattedForSelect();
$selectedCategories = $item->categories->pluck('id')->toArray();
@endphp

@extends('admin.layouts.app')

@section('title', 'Tin tức')
@section('subtitle', 'Chỉnh sửa')
@section('content')
<div class="pc-content">
    <div class="page-header">
        <div class="page-header-title">
            <h2 class="mb-0">
                @yield('title')
                @hasSection('subtitle')
                <small class="text-muted">@yield('subtitle')</small>
                @endif
            </h2>
        </div>
    </div>
    <div class="page-form pb-4">
        @include('admin.news-article._form')
    </div>
</div>
@endsection