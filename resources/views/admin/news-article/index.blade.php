@extends('admin.layouts.app')

@section('title', 'Quản lý tin tức')
@php
$pageId = 'news-article-page';
$columns = [
['data' => 'DT_RowIndex', 'name' => 'DT_RowIndex', 'title' => 'STT', 'orderable' => false, 'searchable' => false, 'className' => 'dt-center'],
['data' => 'title', 'name' => 'title', 'title' => 'Tiêu đề','width'=>'200px'],
['data' => 'categories_list', 'defaultContent' => '', 'name' => 'categories_list', 'title' => 'Danh mục','width'=>'200px'],
['data' => 'hits', 'name' => 'hits', 'title' => 'Truy cập','className'=> 'dt-center'],
['data' => 'featured', 'name' => 'featured', 'title' => 'Nổi bật', 'className'=> 'dt-center'],
['data' => 'status', 'name' => 'status', 'title' => 'Hiển thị', 'className'=> 'dt-center'],
['data' => 'creator.username', 'defaultContent' => '', 'name' => 'creator_username', 'title' => 'Người tạo'],
['data' => 'actions', 'name' => 'actions', 'title' => 'Hành động', 'orderable' => false, 'searchable' => false],
];
@endphp

@section('content')
<div class="pc-content">
    <div class="page-header">
        <div class="page-block">
            <div class="d-flex align-items-center justify-content-between">
                <div class="page-header-title">
                    <h2 class="mb-0">Tin tức</h2>
                </div>
                <a href="{{ route('admin.news-article.create') }}" class="btn btn-success">+ Thêm mới</a>
            </div>
        </div>
    </div>
    <div class="card-body pb-4">
        <table class="data-table table table-striped table-bordered table-hover"
            data-url="{{ route('admin.news-article.data') }}"
            data-columns='@json($columns)'>
        </table>
    </div>
</div>
@endsection