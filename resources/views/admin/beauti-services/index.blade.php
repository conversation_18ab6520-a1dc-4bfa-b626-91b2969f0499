@extends('admin.layouts.app')

@section('title', 'Quản lý D<PERSON>ch vụ')
@php
$pageId = 'location-province-page';
$columns = [
['data' => 'DT_RowIndex', 'name' => 'DT_RowIndex', 'title' => 'STT', 'orderable' => false, 'searchable' => false, 'className' => 'dt-center'],
['data' => 'name', 'name' => 'name', 'title' => 'Tên'],
['data' => 'types_list', 'name' => 'types_list', 'title' => 'Loại hình'],
['data' => 'status', 'name' => 'status', 'title' => 'Hiển thị', 'className'=> 'dt-center'],
['data' => 'creator.username', 'defaultContent' => '', 'name' => 'creator_username', 'title' => 'Người tạo'],
['data' => 'created_at_formatted', 'type' => 'datetime', 'name' => 'created_at', 'title' => 'Ngày tạo'],
['data' => 'actions', 'name' => 'actions', 'title' => 'Hành động', 'orderable' => false, 'searchable' => false],
];
@endphp

@section('content')
<div class="pc-content">
    <div class="page-header">
        <div class="page-block">
            <div class="d-flex align-items-center justify-content-between">
                <div class="page-header-title">
                    <h2 class="mb-0">Dịch vụ</h2>
                </div>
                <a href="{{ route('admin.beauti-services.create') }}" class="btn btn-success">+ Thêm mới</a>
            </div>
        </div>
    </div>
    <div class="card-body pb-4">
        <table class="data-table table table-striped table-bordered table-hover"
            data-url="{{ route('admin.beauti-services.data') }}"
            data-columns='@json($columns)'>
        </table>
    </div>
</div>
@endsection