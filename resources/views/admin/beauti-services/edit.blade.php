@php
use App\Models\BeautiType;
$types = BeautiType::select('id', 'name')->get();
$selectedTypes = $item->types->pluck('id')->toArray();

@endphp

@extends('admin.layouts.app')

@section('title', 'Dịch vụ')
@section('subtitle', 'Chỉnh sửa')
@section('content')
<div class="pc-content">
    <div class="page-header">
        <div class="page-header-title">
            <h2 class="mb-0">
                @yield('title')
                @hasSection('subtitle')
                <small class="text-muted">@yield('subtitle')</small>
                @endif
            </h2>
        </div>
    </div>
    <div class="page-form pb-4">
        @include('admin.beauti-services._form')
    </div>
</div>
@endsection