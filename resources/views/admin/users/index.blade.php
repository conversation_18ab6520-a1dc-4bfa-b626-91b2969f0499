@extends('admin.layouts.app')

@section('title', 'Quản lý tài khoản')
@php
$pageId = 'host-page';
$columns = [
['data' => 'DT_RowIndex', 'name' => 'DT_RowIndex', 'title' => 'STT', 'orderable' => false, 'searchable' => false, 'className' => 'dt-center'],
['data' => 'id', 'name' => 'id', 'title' => 'ID'],
['data' => 'fullname', 'name' => 'fullname', 'title' => 'Họ tên'],
['data' => 'phone', 'name' => 'phone', 'title' => 'Điện thoại'],
['data' => 'email', 'name' => 'email', 'title' => 'Email'],
['data' => 'status', 'name' => 'status', 'title' => 'Hoạt động', 'className'=> 'dt-center'],
['data' => 'supporter.username', 'defaultContent' => '', 'name' => 'supporter_username', 'title' => '<PERSON><PERSON> trách'],
['data' => 'creator.username', 'defaultContent' => '', 'name' => 'creator_username', 'title' => 'Người tạo'],
['data' => 'created_at_formatted', 'type' => 'datetime', 'name' => 'created_at', 'title' => 'Thời gian tạo'],
['data' => 'actions', 'name' => 'actions', 'title' => 'Hành động', 'orderable' => false, 'searchable' => false],
];
@endphp

@section('content')
<div class="pc-content">
    <div class="page-header">
        <div class="page-block">
            <div class="d-flex align-items-center justify-content-between">
                <div class="page-header-title">
                    <h2 class="mb-0">Quản lý tài khoản</h2>
                </div>
                <a href="{{ route('admin.users.create') }}" class="btn btn-success">+ Thêm mới</a>
            </div>
        </div>
    </div>
    <div class="card-body pb-4">
        <table class="data-table table table-striped table-bordered table-hover"
            data-url="{{ route('admin.users.data') }}"
            data-columns='@json($columns)'>
        </table>
    </div>
</div>
@endsection