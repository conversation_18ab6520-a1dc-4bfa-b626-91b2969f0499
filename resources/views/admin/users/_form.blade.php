<form method="POST" action="{{ isset($item) ? route('admin.users.update', $item->id) : route('admin.users.store') }}"
    enctype="multipart/form-data">
    @csrf
    @if(isset($item))
    @method('PUT')
    @endif

    <div class="card mb-4">
        <div class="card-header">
            <strong>Thông tin</strong>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-12">
                    <x-form.input
                        label="Họ tên"
                        name="fullname"
                        :value="$item->fullname ?? ''" />
                </div>
            </div>
            <div class="row">
                <div class="col-md-12">
                    <x-form.input
                        label="Điện thoại"
                        name="phone"
                        type="number"
                        :value="$item->phone ?? ''" />
                </div>
                <div class="col-md-12">
                    <x-form.input
                        label="Email"
                        name="email"
                        :value="$item->email ?? ''" />
                </div>
            </div>
            <div class="row">
                <div class="col-md-12">
                    <x-form.input
                        label="Địa chỉ"
                        name="address"
                        :value="$item->address ?? ''" />
                </div>
                <div class="col-md-12">
                    <x-form.input
                        type="date"
                        label="Ngày sinh"
                        name="date_of_birth"
                        :value="$item->date_of_birth ?? ''" />
                </div>
                <div class="col-md-12">
                    <x-form.select
                        label="Kích hoạt"
                        name="status"
                        :options="[
                            ['id' => '1', 'name' => 'Có'],
                            ['id' => '0', 'name' => 'Không']
                                ]"
                        :selected="$item->status ?? '1'" />
                </div>
            </div>
            <div class="row">
                <div class="col-md-12">
                    <x-form.select
                        label="Giới tính"
                        name="gender"
                        :options="[
                                ['id' => 'male', 'name' => 'Nam'],
                                ['id' => 'female', 'name' => 'Nữ'],
                                ['id' => 'other', 'name' => 'Khác'],
                            ]"
                        :selected="$item->gender ?? null" />

                </div>
                <div class="col-md-12">
                    <x-form.select
                        label="Phụ trách"
                        name="supporter_id"
                        :options="$supporter"
                        :selected="$item->supporter_id ?? ''"
                        optionValue="id"
                        optionLabel="username" />
                </div>
            </div>
        </div>
    </div>

    <div class="mb-3">
        <button type="submit" class="btn btn-primary">
            {{ isset($item) ? 'Cập nhật' : 'Tạo mới' }}
        </button>
        <a href="{{ route('admin.users.index') }}" class="btn btn-secondary">Quay lại</a>
    </div>
</form>