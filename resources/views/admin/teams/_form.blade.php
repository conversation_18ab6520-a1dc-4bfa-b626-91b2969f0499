<form method="POST" action="{{ isset($item) ? route('admin.teams.update', $item->id) : route('admin.teams.store') }}"
    enctype="multipart/form-data">
    @csrf
    @if(isset($item))
    @method('PUT')
    @endif

    <div class="card mb-4 border-top-3 border-top-primary">
        <div class="card-header">
            <strong>Thông tin</strong>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <x-form.input
                        label="Tên"
                        name="name"
                        :value="$item->name ?? ''" />
                </div>
                <div class="col-md-6">
                    <x-form.input
                        label="Mã"
                        name="code"
                        :value="$item->code ?? ''" />
                </div>
            </div>
            <div class="row">
                <div class="col-md-6">
                    <x-form.select
                        label="Quản lý"
                        name="manager_id"
                        :options="$manager"
                        :selected="$item->manager_id ?? ''"
                        optionValue="id"
                        optionLabel="fullname" />
                </div>
                <div class="col-md-6">
                    <x-form.input
                        label="Telegram"
                        name="telegram"
                        :value="$item->telegram ?? ''" />
                </div>
            </div>
            <div class="row">
                <div class="col-md-6">
                    <x-form.color-picker
                        name="color"
                        label="Mã màu"
                        :value="$item->color ?? '#000000'" />
                </div>
            </div>
            <div class="row">
                <div class="col-md-6">
                    <x-form.input
                        label="Thứ tự"
                        name="position"
                        :value="$item->position ?? ''" />
                </div>
            </div>
            <div class="row">
                <div class="col-md-6">
                    <x-form.select
                        label="Kích hoạt"
                        name="status"
                        :options="[
                            ['id' => '1', 'name' => 'Có'],
                            ['id' => '0', 'name' => 'Không']
                                ]"
                        :selected="$item->status ?? '1'" />
                </div>
            </div>
        </div>
    </div>

    <div class="mb-3">
        <button type="submit" class="btn btn-primary">
            {{ isset($item) ? 'Cập nhật' : 'Tạo mới' }}
        </button>
        <a href="{{ route('admin.teams.index') }}" class="btn btn-secondary">Quay lại</a>
    </div>
</form>