<form method="POST" action="{{ isset($item) ? route('admin.host.update', $item->id) : route('admin.host.store') }}"
    enctype="multipart/form-data">
    @csrf
    @if(isset($item))
    @method('PUT')
    @endif

    <div class="card mb-4">
        <div class="card-header">
            <strong>Thông tin</strong>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <x-form.select
                        label="Mã tài khoản"
                        name="user_id"
                        :options="$users"
                        :selected="old('user_id', $item->user_id ?? '')"
                        optionValue="id"
                        optionLabel="id"
                        :optionAttributes="[
                                'name' => 'fullname',
                                'email' => 'email',
                                'phone' => 'phone',
                                'address' => 'address'
                            ]" />
                </div>
                <div class="col-md-6">
                    <x-form.input
                        label="Họ tên"
                        name="name"
                        :value="$item->name ?? ''" />
                </div>
            </div>
            <div class="row">
                <div class="col-md-6">
                    <x-form.input
                        label="Bí danh"
                        name="alias_name"
                        :value="$item->alias_name ?? ''" />
                </div>
                <div class="col-md-6">
                    <x-form.input
                        label="Email"
                        name="email"
                        :value="$item->email ?? ''" />
                </div>
            </div>
            <div class="row">
                <div class="col-md-6">
                    <x-form.input
                        label="Điện thoại"
                        name="phone"
                        :value="$item->phone ?? ''" />
                </div>
                <div class="col-md-6">
                    <x-form.input
                        label="Zalo"
                        name="zalo"
                        :value="$item->zalo ?? ''" />
                </div>
            </div>
            <div class="row">
                <div class="col-md-6">
                    <x-form.input
                        label="Địa chỉ"
                        name="address"
                        :value="$item->address ?? ''" />
                </div>
                <div class="col-md-6">
                    <x-form.select
                        label="Kích hoạt"
                        name="status"
                        :options="[
                            ['id' => '1', 'name' => 'Có'],
                            ['id' => '0', 'name' => 'Không']
                                ]"
                        :selected="$item->status ?? '1'" />
                </div>
            </div>
            <div class="row">
                <div class="col-md-6">
                    <x-form.select
                        label="Phụ trách"
                        name="in_charge_id"
                        :options="$inCharge"
                        :selected="$item->in_charge_id ?? ''"
                        optionValue="id"
                        optionLabel="username" />
                </div>
            </div>
            <div class="row">
                <div class="col-md-12">
                    <x-form.editor
                        label="Mô tả"
                        name="description"
                        :value="$item->description ?? ''" />
                </div>
            </div>
        </div>
    </div>

    <div class="mb-3">
        <button type="submit" class="btn btn-primary">
            {{ isset($item) ? 'Cập nhật' : 'Tạo mới' }}
        </button>
        <a href="{{ route('admin.host.index') }}" class="btn btn-secondary">Quay lại</a>
    </div>
</form>