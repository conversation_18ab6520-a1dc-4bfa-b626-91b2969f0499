<form method="POST" action="{{ isset($item) ? route('admin.system-config.update', $item->id) : route('admin.system-config.store') }}"
    enctype="multipart/form-data">
    @csrf
    @if(isset($item))
    @method('PUT')
    @endif

    <div class="card mb-4 border-top-3 border-top-primary">
        <div class="card-header">
            <strong>Thông tin</strong>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <x-form.input
                        label="Tên"
                        name="name"
                        :value="$item->name ?? ''" />
                </div>
                <div class="col-md-6">
                    <x-form.input
                        label="Mã"
                        name="code"
                        :value="$item->code ?? ''" />
                </div>
            </div>
            <div class="row">
                <div class="col-md-6">
                    <x-form.input
                        label="Tiê<PERSON> đề"
                        name="title"
                        :value="$item->title ?? ''" />
                </div>
                <div class="col-md-6">
                    <x-form.image-upload
                        label="Hình ảnh"
                        name="image"
                        :current-image="$item->image ?? null" />
                </div>
            </div>
            <div class="row">
                <div class="col-md-6">
                    <x-form.input
                        label="Liên kết"
                        name="link"
                        :value="$item->link ?? ''" />
                </div>
                <div class="col-md-6">
                    <x-form.select
                        label="Mở trang"
                        name="url_target"
                        :options="[
                            ['id' => '_self', 'name' => 'Mặc định (Cùng cửa sổ)'],
                            ['id' => '_blank', 'name' => 'Cửa sổ / Tab mới'],
                                ]"
                        :selected="$item->url_target ?? ''" />
                </div>
            </div>
            <div class="row">
                <div class="col-md-12">
                    <x-form.select
                        label="Kích hoạt"
                        name="status"
                        :options="[
                            ['id' => '1', 'name' => 'Có'],
                            ['id' => '0', 'name' => 'Không']
                                ]"
                        :selected="$item->status ?? '1'" />
                </div>
            </div>
            <div class="row">
                <div class="col-md-12">
                    <x-form.textarea
                        label="Mô tả"
                        name="description"
                        :value="$item->description ?? ''" />
                </div>
            </div>
            <div class="row">
                <div class="col-md-12">
                    <x-form.editor
                        label="Dữ liệu"
                        name="data"
                        :value="$item->data ?? ''" />
                </div>
            </div>
        </div>
    </div>

    <div class="mb-3">
        <button type="submit" class="btn btn-primary">
            {{ isset($item) ? 'Cập nhật' : 'Tạo mới' }}
        </button>
        <a href="{{ route('admin.system-config.index') }}" class="btn btn-secondary">Quay lại</a>
    </div>
</form>