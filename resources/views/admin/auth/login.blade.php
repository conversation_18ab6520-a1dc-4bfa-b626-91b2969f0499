@extends('admin.layouts.app')

@section('title', 'Đăng nhập Admin')
@section('content')
<div class="login-container">
    <div class="login__card">
        <div class="text-center login-logo">
            <a href="/">
                <img src="{{ asset('images/logo.png') }}" height="54" alt="logo" style="filter: drop-shadow(0 2px 8px #6366f133);" />
            </a>
        </div>
        <div class="login_card--body">
            <div class="login__card--title text-center"><PERSON><PERSON><PERSON> nhập hệ thống</div>
            <form method="POST" action="{{ route('admin.login.submit') }}">
                @csrf
                <div class="mb-3">
                    <label for="username" class="form-label">Tên đăng nhập</label>
                    <input type="text" class="form-control @error('username') is-invalid @enderror" id="username" name="username" value="{{ old('username') }}" placeholder="Nhập tên đăng nhập" autofocus />
                    @error('username')
                    <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>
                <div class="mb-3">
                    <label for="userpassword" class="form-label">Mật khẩu</label>
                    <input type="password" name="password" class="form-control @error('password') is-invalid @enderror" id="userpassword" placeholder="Nhập mật khẩu" />
                    @error('password')
                    <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>
                <div class="text-center mb-3">
                    <button class="btn btn-secondary text-center" type="submit">Đăng nhập</button>
                </div>
            </form>
            <div class="login__card--created text-center">
                &copy; {{ date('Y') }} created <i class="mdi mdi-heart text-danger"></i> by OHi
            </div>
        </div>
    </div>
</div>
@endsection