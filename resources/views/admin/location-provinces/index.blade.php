@extends('admin.layouts.app')

@section('title', 'Quản lý tỉnh/Tp')
@php
$pageId = 'location-province-page';
$columns = [
['data' => 'DT_RowIndex', 'name' => 'DT_RowIndex', 'title' => 'STT', 'orderable' => false, 'searchable' => false, 'className' => 'dt-center'],
['data' => 'name', 'name' => 'name', 'title' => 'TỈnh/Thành phố'],
['data' => 'type', 'name' => 'type', 'title' => 'Loại'],
['data' => 'status', 'name' => 'status', 'title' => 'Hiển thị', 'className'=> 'dt-center'],
['data' => 'actions', 'name' => 'actions', 'title' => 'Hành động', 'orderable' => false, 'searchable' => false],
];
@endphp

@section('content')
<div class="pc-content">
    <div class="page-header">
        <div class="page-block">
            <div class="d-flex align-items-center justify-content-between">
                <div class="page-header-title">
                    <h2 class="mb-0">Tỉnh/TP</h2>
                </div>
                <a href="{{ route('admin.location-provinces.create') }}" class="btn btn-success">+ Thêm mới</a>
            </div>
        </div>
    </div>
    <div class="card-body pb-4">
        <table class="data-table table table-striped table-bordered table-hover"
            data-url="{{ route('admin.location-provinces.data') }}"
            data-columns='@json($columns)'>
        </table>
    </div>
</div>
@endsection