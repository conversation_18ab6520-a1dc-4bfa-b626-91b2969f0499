<form method="POST" action="{{ isset($item) ? route('admin.location-provinces.update', $item->id) : route('admin.location-provinces.store') }}"
    enctype="multipart/form-data">
    @csrf
    @if(isset($item))
    @method('PUT')
    @endif

    <div class="card mb-4">
        <div class="card-header">
            <strong>Thông tin</strong>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <x-form.input
                        label="Tên"
                        name="name"
                        :value="$item->name ?? ''" />
                </div>
                <div class="col-md-6">
                    <x-form.input
                        label="Mã"
                        name="code"
                        :value="$item->code ?? ''" />
                </div>
            </div>
            <div class="row">
                <div class="col-md-6">
                    <x-form.select
                        label="Loại"
                        name="type"
                        :options="[
                                ['id' => 'Tỉnh', 'name' => 'Tỉnh'],
                                ['id' => 'Thành phố', 'name' => 'Thành phố']
                            ]"
                        :selected="$item->type ?? ''" />
                </div>
                <div class="col-md-6">
                    <x-form.select
                        label="Kích hoạt"
                        name="status"
                        :options="[
                            ['id' => '1', 'name' => 'Có'],
                            ['id' => '0', 'name' => 'Không']
                                ]"
                        :selected="$item->status ?? '1'" />
                </div>
            </div>
        </div>
    </div>

    <div class="mb-3">
        <button type="submit" class="btn btn-primary">
            {{ isset($item) ? 'Cập nhật' : 'Tạo mới' }}
        </button>
        <a href="{{ route('admin.location-provinces.index') }}" class="btn btn-secondary">Quay lại</a>
    </div>
</form>