<!DOCTYPE html>
<html lang="vi">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title><PERSON><PERSON> thống quản lý - @yield('title')</title>
    <link rel="icon" type="image/png" href="{{ asset('images/logo.svg') }}" />
    @vite(['resources/scss/admin/admin.scss', 'resources/js/admin/main.js'])

</head>

<body class="bg-gray-100 font-sans" data-page="{{ $pageId ?? '' }}">
    <div class="position-fixed top-0 end-0 p-3" style="z-index: 1050;">
        @if (session('success'))
        <div class="toast align-items-center text-bg-success border-0 show" role="alert" aria-live="assertive" aria-atomic="true" data-bs-delay="5000">
            <div class="d-flex">
                <div class="toast-body">
                    {{ session('success') }}
                </div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
            </div>
        </div>
        @endif

        @if ($errors->any())
        <div class="toast align-items-center text-bg-danger border-0 show" role="alert" aria-live="assertive" aria-atomic="true" data-bs-delay="7000">
            <div class="d-flex">
                <div class="toast-body">
                    <ul class="mb-0">
                        @foreach ($errors->all() as $error)
                        <li>{{ $error }}</li>
                        @endforeach
                    </ul>
                </div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
            </div>
        </div>
        @endif
    </div>

    <!-- Loading Spinner -->
    <div id="global-loading" style="display: none; position: fixed; z-index: 9999; background: rgba(255,255,255,0.7); top: 0; left: 0; width: 100%; height: 100%;">
        <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%)">
            <div class="spinner-grow text-danger" role="status">
                <span class="visually-hidden">Đang tải...</span>
            </div>
        </div>
    </div>

    <div class="min-h-screen">
        @auth('admin')
        @include('admin.layouts.partials.sidebar')

        @include('admin.layouts.partials.header')
        @endauth
        <main class="{{ auth('admin')->check() ? 'pc-container' : '' }}">
            @yield('content')
        </main>
    </div>


    @stack('scripts')
</body>

</html>