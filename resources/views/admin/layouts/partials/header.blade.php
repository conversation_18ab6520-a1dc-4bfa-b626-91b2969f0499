<!-- [ Header <PERSON>bar ] start -->
<header class="pc-header">
    <div class="header-wrapper"> <!-- [Mobile Media Block] start -->
        <div class="me-auto pc-mob-drp">
            <ul class="list-unstyled">
                <!-- ======= Menu collapse Icon ===== -->
                <li class="pc-h-item pc-sidebar-collapse">
                    <a href="#" class="pc-head-link ms-0" id="sidebar-hide">
                        <i class="ti ti-menu-2"></i>
                    </a>
                </li>
                <li class="pc-h-item pc-sidebar-popup">
                    <a href="#" class="pc-head-link ms-0" id="mobile-collapse">
                        <i class="ti ti-menu-2"></i>
                    </a>
                </li>
            </ul>
        </div>
        <!-- [Mobile Media Block end] -->
        <div class="ms-auto">
            <ul class="list-unstyled">
                <li class="dropdown pc-h-item">
                    <a
                        class="pc-head-link dropdown-toggle arrow-none me-0"
                        data-bs-toggle="dropdown"
                        href="#"
                        role="button"
                        aria-haspopup="false"
                        aria-expanded="false">
                        <i class="fas fa-bell"></i>
                    </a>
                    <div class="dropdown-menu dropdown-notification dropdown-menu-end pc-h-dropdown">
                        <div class="dropdown-header d-flex align-items-center justify-content-between">
                            <h5 class="m-0">Thông báo</h5>
                            <a href="#!" class="pc-head-link bg-transparent"><i class="ti ti-x text-danger"></i></a>
                        </div>
                        <div class="dropdown-divider"></div>
                        <div class="dropdown-header px-0 text-wrap header-notification-scroll position-relative" style="max-height: calc(100vh - 215px)">
                            <p class="p-1">Không có thông báo nào</p>
                        </div>
                        <div class="dropdown-divider"></div>
                        <div class="text-center py-2">
                            <a href="#!" class="link-primary">Xem tất cả</a>
                        </div>
                    </div>
                </li>
                <li class="dropdown pc-h-item header-user-profile">
                    <a
                        class="pc-head-link dropdown-toggle arrow-none me-0"
                        data-bs-toggle="dropdown"
                        href="#"
                        role="button"
                        aria-haspopup="false"
                        data-bs-auto-close="outside"
                        aria-expanded="false">
                        <img src="{{ Auth::guard('admin')->user()->avatar 
                                        ? asset(Auth::guard('admin')->user()->avatar) 
                                        : asset('images/avatar.jpg') 
                                    }}" alt="user-image" class="user-avtar">
                        <span>{{ Auth::guard('admin')->user()->fullname }}</span>
                    </a>
                    <div class="dropdown-menu dropdown-user-profile dropdown-menu-end pc-h-dropdown p-0">
                        <div class="dropdown-header border-bottom">
                            <div class="d-flex align-items-center mb-1">
                                <div class="flex-shrink-0">
                                    <img src="{{ Auth::guard('admin')->user()->avatar 
                                        ? asset(Auth::guard('admin')->user()->avatar) 
                                        : asset('images/avatar.jpg') 
                                    }}" alt="user-image" class="user-avtar wid-35">
                                </div>
                                <div class="flex-grow-1">
                                    <h6 class="mb-1">{{ Auth::guard('admin')->user()->fullname }}</h6>
                                    @if(!empty(Auth::guard('admin')->user()->title))
                                    <span>{{ Auth::guard('admin')->user()->title }}</span>
                                    @endif
                                </div>
                            </div>
                        </div>
                        <div class="tab-content py-2" id="mysrpTabContent">
                            <div class="d-flex align-items-center justify-content-around" id="drp-tab-1" role="tabpanel" aria-labelledby="drp-t1" tabindex="0">
                                @auth('admin')
                                <a href="#!" class="">
                                    <button type="button" class="btn btn-sm btn-primary">Thông tin</button>
                                </a>
                                <div class="btn-logout d-inline text-end px-3">
                                    <form method="POST" action="{{ route('admin.logout') }}">
                                        @csrf
                                        <button type="submit" class="btn btn-danger btn-sm d-inline-flex gap-1 align-items-center"> <i class="ti ti-power"></i><span>Logout</span></button>
                                    </form>
                                </div>
                                @endauth
                            </div>
                        </div>
                    </div>
                </li>
            </ul>
        </div>
    </div>
</header>
<!-- [ Header ] end -->