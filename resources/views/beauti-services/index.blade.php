@extends('layouts.app', ['pageName' => 'beauti-services'])

@section('title', $store->name . ' — <PERSON><PERSON>c dịch vụ')
@section('meta_description', 'Danh sách dịch vụ thuộc cơ sở ' . $store->name)

@section('content')
<div class="beauti-service-page">

    @include('components.search-bar', ['class' => 'custom-search'])
    <div class="container wide">
        <div class="row">
            <div class="col col-md-12 p-3">
                <div class="breadcrumb d-flex align-items-center gap-1">
                    <a href="{{route('home')}}">Trang chủ</a>
                    <span> / </span>
                    <a href="javascript:void(0)" class="breadcrumb--current">{{$store->name}}</a>
                </div>
            </div>
        </div>
        <div class="row row-gap-3">
            <div class="col col-md-12">
                <div class="d-flex align-items-center gap-2">
                    <div class="box-head">{{$store->name}}</div>
                    <div class="store--time">{{ \Carbon\Carbon::parse($store->opening_time)->format('H:i') }} - {{ \Carbon\Carbon::parse($store->closing_time)->format('H:i') }}</div>
                </div>
            </div>
            <div class="col col-md-12 d-flex align-items-center gap-5">
                <div class="store--location d-flex align-items-center gap-1">
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
                        <path d="M5.05005 4.05005C7.78358 1.31651 12.2158 1.31679 14.9495 4.05005C17.6831 6.78372 17.6831 11.2158 14.9495 13.9495L10.0002 18.8997L5.05005 13.9495C2.31679 11.2158 2.31651 6.78358 5.05005 4.05005ZM10.0002 7.00024C8.89567 7.00024 8.00024 7.89567 8.00024 9.00024C8.00029 10.1048 8.8957 11.0002 10.0002 11.0002C11.1047 11.0002 12.0002 10.1047 12.0002 9.00024C12.0002 7.8957 11.1048 7.00029 10.0002 7.00024Z" fill="#331559" />
                    </svg>
                    <span>{{$store->full_address}}</span>
                </div>
                <div class="store--rating d-flex align-items-center gap-2">
                    <svg xmlns="http://www.w3.org/2000/svg" width="19" height="18" viewBox="0 0 19 18" fill="none">
                        <path d="M12.7295 5.52246L19 6.875L14.7246 11.6416L15.3711 18L9.5 15.4229L3.62891 18L4.27539 11.6416L0 6.875L6.27051 5.52246L9.5 0L12.7295 5.52246Z" fill="#FFCB45" />
                    </svg>
                    <svg xmlns="http://www.w3.org/2000/svg" width="19" height="18" viewBox="0 0 19 18" fill="none">
                        <path d="M12.7295 5.52246L19 6.875L14.7246 11.6416L15.3711 18L9.5 15.4229L3.62891 18L4.27539 11.6416L0 6.875L6.27051 5.52246L9.5 0L12.7295 5.52246Z" fill="#FFCB45" />
                    </svg>
                    <svg xmlns="http://www.w3.org/2000/svg" width="19" height="18" viewBox="0 0 19 18" fill="none">
                        <path d="M12.7295 5.52246L19 6.875L14.7246 11.6416L15.3711 18L9.5 15.4229L3.62891 18L4.27539 11.6416L0 6.875L6.27051 5.52246L9.5 0L12.7295 5.52246Z" fill="#FFCB45" />
                    </svg>
                    <svg xmlns="http://www.w3.org/2000/svg" width="19" height="18" viewBox="0 0 19 18" fill="none">
                        <path d="M12.7295 5.52246L19 6.875L14.7246 11.6416L15.3711 18L9.5 15.4229L3.62891 18L4.27539 11.6416L0 6.875L6.27051 5.52246L9.5 0L12.7295 5.52246Z" fill="#FFCB45" />
                    </svg>
                    <svg xmlns="http://www.w3.org/2000/svg" width="19" height="18" viewBox="0 0 19 18" fill="none">
                        <path d="M12.7295 5.52246L19 6.875L14.7246 11.6416L15.3711 18L9.5 15.4229L3.62891 18L4.27539 11.6416L0 6.875L6.27051 5.52246L9.5 0L12.7295 5.52246Z" fill="#FFCB45" />
                    </svg>
                    <div class="store--rating--desc">
                        (4.5)
                    </div>
                    <div class="store--review">
                        (25 đánh giá)
                    </div>
                </div>
            </div>
            <div class="col col-md-12">
                <div class="store--tag d-flex align-items-center gap-3">
                    <span class="tag--items">
                        Hà nội
                    </span>
                    <span class="tag--items">
                        Spa
                    </span>
                </div>
            </div>
        </div>

        <div class="row mt-3 h-100">
            <div class="col col-md-6">
                <img class="box-image" src="{{asset('images/services/s1.jpg')}}" alt="{{$store->name}}">
            </div>
            <div class="col col-md-3">
                <div class="row row-gap-4">
                    <div class="col col-12">
                        <img class="box-image" src="{{asset('images/services/s2.jpg')}}" alt="{{$store->name}}">
                    </div>
                    <div class="col col-12">
                        <img class="box-image" src="{{asset('images/services/s3.jpg')}}" alt="{{$store->name}}">
                    </div>
                </div>
            </div>
            <div class="col col-md-3">
                <div class="row row-gap-4">
                    <div class="col col-12">
                        <img class="box-image" src="{{asset('images/services/s4.jpg')}}" alt="{{$store->name}}">
                    </div>
                    <div class="col col-12">
                        <img class="box-image" src="{{asset('images/services/s5.jpg')}}" alt="{{$store->name}}">
                    </div>
                </div>
            </div>
        </div>
    </div>

    @include('partials.sections.list-services', ['listServices' => $services, 'store'=>$store,'listPackages'=>$packages])
    @include('partials.sections.list-stores-location' , ['listStoresRelated' => $storesRelated, 'provinceName'=> $provinceName])
</div>
@endsection