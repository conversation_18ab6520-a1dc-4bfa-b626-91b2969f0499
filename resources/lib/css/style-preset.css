/**======================================================================
=========================================================================
Template Name: Able Pro - Bootstrap Admin Template
Author: codedthemes
Support: https://codedthemes.authordesk.app
File: style.css
=========================================================================
=================================================================================== */
h1,
h2 {
  font-weight: 700;
}

:root {
  --bs-body-bg: #fafafb;
  --bs-body-bg-rgb: 250, 250, 251;
  --pc-heading-color: #343a40;
  --pc-active-background: #e9ecef;
  --pc-sidebar-background: #fff;
  --pc-sidebar-color: #141414;
  --pc-sidebar-color-rgb: 20, 20, 20;
  --pc-sidebar-active-color: var(--bs-primary);
  --pc-sidebar-shadow: 1px 0 0 0px rgb(240 240 240);
  --pc-sidebar-caption-color: #495057;
  --pc-header-background: #fff;
  --pc-header-color: #141414;
  --pc-header-shadow: 0 1px 0 0px rgb(240 240 240);
  --pc-card-box-shadow: none;
  --pc-header-submenu-background: #ffffff;
  --pc-header-submenu-color: #6c757d;
}

[data-pc-theme_contrast=true] {
  --bs-body-bg: #ffffff;
  --pc-sidebar-background: transparent;
  --pc-sidebar-active-color: #1890ff;
  --pc-sidebar-shadow: 1px 0 3px 0px #dee2e6;
  --pc-sidebar-border: none;
  --pc-card-box-shadow: 0px 8px 24px rgba(27, 46, 94, 0.08);
}

[data-pc-preset=preset-1] {
  --pc-sidebar-active-color: #1890ff;
  --bs-blue: #1890ff;
  --bs-primary: #1890ff;
  --bs-primary-rgb: 24, 144, 255;
  --bs-primary-light: #e8f4ff;
  --bs-link-color: #1890ff;
  --bs-link-color-rgb: 24, 144, 255;
  --bs-link-hover-color: #1373cc;
  --bs-link-hover-color-rgb: to-rgb(shift-color($pc-primary, $link-shade-percentage));
  --dt-row-selected: 24, 144, 255;
}
[data-pc-preset=preset-1] .bg-light-primary {
  background: #e8f4ff;
  color: #1890ff;
}
[data-pc-preset=preset-1] .link-primary {
  color: #1890ff !important;
}
[data-pc-preset=preset-1] .link-primary:hover, [data-pc-preset=preset-1] .link-primary:focus {
  color: #1373cc !important;
}
[data-pc-preset=preset-1] .btn-primary {
  --bs-btn-color: #ffffff;
  --bs-btn-bg: #1890ff;
  --bs-btn-border-color: #1890ff;
  --bs-btn-hover-color: #ffffff;
  --bs-btn-hover-bg: #147ad9;
  --bs-btn-hover-border-color: #1373cc;
  --bs-btn-focus-shadow-rgb: 59, 161, 255;
  --bs-btn-active-color: #ffffff;
  --bs-btn-active-bg: #1373cc;
  --bs-btn-active-border-color: #126cbf;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  --bs-btn-disabled-color: #ffffff;
  --bs-btn-disabled-bg: #1890ff;
  --bs-btn-disabled-border-color: #1890ff;
}
[data-pc-preset=preset-1] .btn-link {
  --bs-btn-color: #1890ff;
  --bs-btn-hover-color: #1373cc;
  --bs-btn-active-color: #1373cc;
}
[data-pc-preset=preset-1] .text-bg-primary {
  color: #ffffff !important;
  background-color: RGBA(24, 144, 255, var(--bs-bg-opacity, 1)) !important;
}
[data-pc-preset=preset-1] .accordion {
  --bs-accordion-btn-focus-border-color: #1890ff;
  --bs-accordion-btn-focus-box-shadow: 0 0 0 0.2rem rgba(24, 144, 255, 0.25);
  --bs-accordion-active-color: #1890ff;
  --bs-accordion-active-bg: #e8f4ff;
  --bs-accordion-btn-active-icon: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%231890ff'%3e%3cpath fill-rule=' evenodd' d=' M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/%3e%3c/svg%3e");
}
[data-pc-preset=preset-1] .alert-primary {
  --bs-alert-color: #0e5699;
  --bs-alert-bg: #d1e9ff;
  --bs-alert-border-color: #badeff;
  --bs-alert-link-color: #0b457a;
}
[data-pc-preset=preset-1] .list-group {
  --bs-list-group-active-bg: #1890ff;
  --bs-list-group-active-border-color: #1890ff;
}
[data-pc-preset=preset-1] .list-group-item-primary {
  color: #0e5699;
  background-color: #d1e9ff;
}
[data-pc-preset=preset-1] .nav {
  --bs-nav-link-hover-color: #1373cc;
}
[data-pc-preset=preset-1] .nav-pills {
  --bs-nav-pills-link-active-bg: #1890ff;
}
[data-pc-preset=preset-1] .pagination {
  --bs-pagination-hover-color: #1373cc;
  --bs-pagination-focus-color: #1373cc;
  --bs-pagination-focus-box-shadow: 0 0 0 0.2rem rgba(24, 144, 255, 0.25);
  --bs-pagination-active-bg: #1890ff;
  --bs-pagination-active-border-color: #1890ff;
}
[data-pc-preset=preset-1] .progress {
  --bs-progress-bar-bg: #1890ff;
}
[data-pc-preset=preset-1] .form-check .form-check-input.input-primary:checked {
  border-color: #1890ff;
  background-color: #1890ff;
}
[data-pc-preset=preset-1] .form-check .form-check-input.input-light-primary:checked {
  border-color: #e8f4ff;
  background-color: #e8f4ff;
}
[data-pc-preset=preset-1] .form-check .form-check-input.input-light-primary:checked[type=checkbox] {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'%3e%3cpath fill='none' stroke='%231890ff' stroke-linecap='round' stroke-linejoin='round' stroke-width='3' d='M6 10l3 3l6-6'/%3e%3c/svg%3e");
}
[data-pc-preset=preset-1] .form-check .form-check-input.input-light-primary:checked[type=radio] {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='2' fill='%231890ff'/%3e%3c/svg%3e");
}
[data-pc-preset=preset-1] .form-check .form-check-input.input-primary:focus[type=checkbox], [data-pc-preset=preset-1] .form-check .form-check-input.input-primary:focus[type=radio], [data-pc-preset=preset-1] .form-check .form-check-input.input-light-primary:focus[type=checkbox], [data-pc-preset=preset-1] .form-check .form-check-input.input-light-primary:focus[type=radio] {
  box-shadow: 0 0 0 0.2rem rgba(24, 144, 255, 0.25);
  border-color: #1890ff;
}
[data-pc-preset=preset-1] .form-check.form-switch .form-check-input.input-light-primary:checked {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='%231890ff'/%3e%3c/svg%3e");
}
[data-pc-preset=preset-1] .btn-light-primary {
  background: #e8f4ff;
  color: #1890ff;
  border-color: #e8f4ff;
}
[data-pc-preset=preset-1] .btn-light-primary .material-icons-two-tone {
  background-color: #1890ff;
}
[data-pc-preset=preset-1] .btn-light-primary:hover {
  background: #1890ff;
  color: #fff;
  border-color: #1890ff;
}
[data-pc-preset=preset-1] .btn-light-primary.focus, [data-pc-preset=preset-1] .btn-light-primary:focus {
  background: #1890ff;
  color: #fff;
  border-color: #1890ff;
}
[data-pc-preset=preset-1] .btn-light-primary:not(:disabled):not(.disabled).active, [data-pc-preset=preset-1] .btn-light-primary:not(:disabled):not(.disabled):active, .show > [data-pc-preset=preset-1] .btn-light-primary.dropdown-toggle {
  background: #1890ff;
  color: #fff;
  border-color: #1890ff;
}
[data-pc-preset=preset-1] .btn-check:active + .btn-light-primary,
[data-pc-preset=preset-1] .btn-check:checked + .btn-light-primary {
  background: #1890ff;
  color: #fff;
  border-color: #1890ff;
}
[data-pc-preset=preset-1] .btn-link-primary {
  background: transparent;
  color: #1890ff;
  border-color: transparent;
}
[data-pc-preset=preset-1] .btn-link-primary .material-icons-two-tone {
  background-color: #1890ff;
}
[data-pc-preset=preset-1] .btn-link-primary:hover {
  background: #e8f4ff;
  color: #1890ff;
  border-color: #e8f4ff;
}
[data-pc-preset=preset-1] .btn-link-primary.focus, [data-pc-preset=preset-1] .btn-link-primary:focus {
  background: #e8f4ff;
  color: #1890ff;
  border-color: #e8f4ff;
}
[data-pc-preset=preset-1] .btn-link-primary:not(:disabled):not(.disabled).active, [data-pc-preset=preset-1] .btn-link-primary:not(:disabled):not(.disabled):active, .show > [data-pc-preset=preset-1] .btn-link-primary.dropdown-toggle {
  background: #e8f4ff;
  color: #1890ff;
  border-color: #e8f4ff;
}
[data-pc-preset=preset-1] .btn-check:active + .btn-link-primary,
[data-pc-preset=preset-1] .btn-check:checked + .btn-link-primary {
  background: #e8f4ff;
  color: #1890ff;
  border-color: #e8f4ff;
}
[data-pc-preset=preset-1] .slider-selection {
  background-image: linear-gradient(to bottom, #98cdff 0, #98cdff 100%);
}
[data-pc-preset=preset-1] .slider-selection.tick-slider-selection {
  background-image: linear-gradient(to bottom, #7ec1ff 0, #7ec1ff 100%);
}
[data-pc-preset=preset-1] .swal-button:not([disabled]):hover {
  background-color: #0084fe;
}
[data-pc-preset=preset-1] .swal-button:active {
  background-color: #0084fe;
}
[data-pc-preset=preset-1] .swal-button:focus {
  box-shadow: 0 0 0 1px #fff, 0 0 0 3px rgba(24, 144, 255, 0.29);
}
[data-pc-preset=preset-1] .swal-content__input:focus {
  border-color: rgba(24, 144, 255, 0.29);
}
[data-pc-preset=preset-1] .swal-content__textarea:focus {
  border-color: rgba(24, 144, 255, 0.29);
}
[data-pc-preset=preset-1] .swal2-styled:focus {
  box-shadow: 0 0 0 1px #fff, 0 0 0 3px rgba(24, 144, 255, 0.4) !important;
}
[data-pc-preset=preset-1] .slider-tick.in-selection {
  background-image: linear-gradient(to bottom, #7ec1ff 0, #7ec1ff 100%);
}
[data-pc-preset=preset-1] .table-primary {
  --bs-table-color: #ffffff;
  --bs-table-bg: #1890ff;
  --bs-table-border-color: #2f9bff;
  --bs-table-striped-bg: #2496ff;
  --bs-table-striped-color: #ffffff;
  --bs-table-active-bg: #2f9bff;
  --bs-table-active-color: #ffffff;
  --bs-table-hover-bg: #1d92ff;
  --bs-table-hover-color: #ffffff;
  color: var(--bs-table-color);
  border-color: var(--bs-table-border-color);
}

[data-pc-preset=preset-2] {
  --pc-sidebar-active-color: #3366ff;
  --bs-blue: #3366ff;
  --bs-primary: #3366ff;
  --bs-primary-rgb: 51, 102, 255;
  --bs-primary-light: #ebf0ff;
  --bs-link-color: #3366ff;
  --bs-link-color-rgb: 51, 102, 255;
  --bs-link-hover-color: #2952cc;
  --bs-link-hover-color-rgb: to-rgb(shift-color($pc-primary, $link-shade-percentage));
  --dt-row-selected: 51, 102, 255;
}
[data-pc-preset=preset-2] .bg-light-primary {
  background: #ebf0ff;
  color: #3366ff;
}
[data-pc-preset=preset-2] .link-primary {
  color: #3366ff !important;
}
[data-pc-preset=preset-2] .link-primary:hover, [data-pc-preset=preset-2] .link-primary:focus {
  color: #2952cc !important;
}
[data-pc-preset=preset-2] .btn-primary {
  --bs-btn-color: #ffffff;
  --bs-btn-bg: #3366ff;
  --bs-btn-border-color: #3366ff;
  --bs-btn-hover-color: #ffffff;
  --bs-btn-hover-bg: #2b57d9;
  --bs-btn-hover-border-color: #2952cc;
  --bs-btn-focus-shadow-rgb: 82, 125, 255;
  --bs-btn-active-color: #ffffff;
  --bs-btn-active-bg: #2952cc;
  --bs-btn-active-border-color: #264dbf;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  --bs-btn-disabled-color: #ffffff;
  --bs-btn-disabled-bg: #3366ff;
  --bs-btn-disabled-border-color: #3366ff;
}
[data-pc-preset=preset-2] .btn-link {
  --bs-btn-color: #3366ff;
  --bs-btn-hover-color: #2952cc;
  --bs-btn-active-color: #2952cc;
}
[data-pc-preset=preset-2] .text-bg-primary {
  color: #ffffff !important;
  background-color: RGBA(51, 102, 255, var(--bs-bg-opacity, 1)) !important;
}
[data-pc-preset=preset-2] .accordion {
  --bs-accordion-btn-focus-border-color: #3366ff;
  --bs-accordion-btn-focus-box-shadow: 0 0 0 0.2rem rgba(51, 102, 255, 0.25);
  --bs-accordion-active-color: #3366ff;
  --bs-accordion-active-bg: #ebf0ff;
  --bs-accordion-btn-active-icon: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%233366ff'%3e%3cpath fill-rule=' evenodd' d=' M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/%3e%3c/svg%3e");
}
[data-pc-preset=preset-2] .alert-primary {
  --bs-alert-color: #1f3d99;
  --bs-alert-bg: #d6e0ff;
  --bs-alert-border-color: #c2d1ff;
  --bs-alert-link-color: #19317a;
}
[data-pc-preset=preset-2] .list-group {
  --bs-list-group-active-bg: #3366ff;
  --bs-list-group-active-border-color: #3366ff;
}
[data-pc-preset=preset-2] .list-group-item-primary {
  color: #1f3d99;
  background-color: #d6e0ff;
}
[data-pc-preset=preset-2] .nav {
  --bs-nav-link-hover-color: #2952cc;
}
[data-pc-preset=preset-2] .nav-pills {
  --bs-nav-pills-link-active-bg: #3366ff;
}
[data-pc-preset=preset-2] .pagination {
  --bs-pagination-hover-color: #2952cc;
  --bs-pagination-focus-color: #2952cc;
  --bs-pagination-focus-box-shadow: 0 0 0 0.2rem rgba(51, 102, 255, 0.25);
  --bs-pagination-active-bg: #3366ff;
  --bs-pagination-active-border-color: #3366ff;
}
[data-pc-preset=preset-2] .progress {
  --bs-progress-bar-bg: #3366ff;
}
[data-pc-preset=preset-2] .form-check .form-check-input.input-primary:checked {
  border-color: #3366ff;
  background-color: #3366ff;
}
[data-pc-preset=preset-2] .form-check .form-check-input.input-light-primary:checked {
  border-color: #ebf0ff;
  background-color: #ebf0ff;
}
[data-pc-preset=preset-2] .form-check .form-check-input.input-light-primary:checked[type=checkbox] {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'%3e%3cpath fill='none' stroke='%233366ff' stroke-linecap='round' stroke-linejoin='round' stroke-width='3' d='M6 10l3 3l6-6'/%3e%3c/svg%3e");
}
[data-pc-preset=preset-2] .form-check .form-check-input.input-light-primary:checked[type=radio] {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='2' fill='%233366ff'/%3e%3c/svg%3e");
}
[data-pc-preset=preset-2] .form-check .form-check-input.input-primary:focus[type=checkbox], [data-pc-preset=preset-2] .form-check .form-check-input.input-primary:focus[type=radio], [data-pc-preset=preset-2] .form-check .form-check-input.input-light-primary:focus[type=checkbox], [data-pc-preset=preset-2] .form-check .form-check-input.input-light-primary:focus[type=radio] {
  box-shadow: 0 0 0 0.2rem rgba(51, 102, 255, 0.25);
  border-color: #3366ff;
}
[data-pc-preset=preset-2] .form-check.form-switch .form-check-input.input-light-primary:checked {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='%233366ff'/%3e%3c/svg%3e");
}
[data-pc-preset=preset-2] .btn-light-primary {
  background: #ebf0ff;
  color: #3366ff;
  border-color: #ebf0ff;
}
[data-pc-preset=preset-2] .btn-light-primary .material-icons-two-tone {
  background-color: #3366ff;
}
[data-pc-preset=preset-2] .btn-light-primary:hover {
  background: #3366ff;
  color: #fff;
  border-color: #3366ff;
}
[data-pc-preset=preset-2] .btn-light-primary.focus, [data-pc-preset=preset-2] .btn-light-primary:focus {
  background: #3366ff;
  color: #fff;
  border-color: #3366ff;
}
[data-pc-preset=preset-2] .btn-light-primary:not(:disabled):not(.disabled).active, [data-pc-preset=preset-2] .btn-light-primary:not(:disabled):not(.disabled):active, .show > [data-pc-preset=preset-2] .btn-light-primary.dropdown-toggle {
  background: #3366ff;
  color: #fff;
  border-color: #3366ff;
}
[data-pc-preset=preset-2] .btn-check:active + .btn-light-primary,
[data-pc-preset=preset-2] .btn-check:checked + .btn-light-primary {
  background: #3366ff;
  color: #fff;
  border-color: #3366ff;
}
[data-pc-preset=preset-2] .btn-link-primary {
  background: transparent;
  color: #3366ff;
  border-color: transparent;
}
[data-pc-preset=preset-2] .btn-link-primary .material-icons-two-tone {
  background-color: #3366ff;
}
[data-pc-preset=preset-2] .btn-link-primary:hover {
  background: #ebf0ff;
  color: #3366ff;
  border-color: #ebf0ff;
}
[data-pc-preset=preset-2] .btn-link-primary.focus, [data-pc-preset=preset-2] .btn-link-primary:focus {
  background: #ebf0ff;
  color: #3366ff;
  border-color: #ebf0ff;
}
[data-pc-preset=preset-2] .btn-link-primary:not(:disabled):not(.disabled).active, [data-pc-preset=preset-2] .btn-link-primary:not(:disabled):not(.disabled):active, .show > [data-pc-preset=preset-2] .btn-link-primary.dropdown-toggle {
  background: #ebf0ff;
  color: #3366ff;
  border-color: #ebf0ff;
}
[data-pc-preset=preset-2] .btn-check:active + .btn-link-primary,
[data-pc-preset=preset-2] .btn-check:checked + .btn-link-primary {
  background: #ebf0ff;
  color: #3366ff;
  border-color: #ebf0ff;
}
[data-pc-preset=preset-2] .slider-selection {
  background-image: linear-gradient(to bottom, #b3c6ff 0, #b3c6ff 100%);
}
[data-pc-preset=preset-2] .slider-selection.tick-slider-selection {
  background-image: linear-gradient(to bottom, #99b3ff 0, #99b3ff 100%);
}
[data-pc-preset=preset-2] .swal-button:not([disabled]):hover {
  background-color: #1a53ff;
}
[data-pc-preset=preset-2] .swal-button:active {
  background-color: #1a53ff;
}
[data-pc-preset=preset-2] .swal-button:focus {
  box-shadow: 0 0 0 1px #fff, 0 0 0 3px rgba(51, 102, 255, 0.29);
}
[data-pc-preset=preset-2] .swal-content__input:focus {
  border-color: rgba(51, 102, 255, 0.29);
}
[data-pc-preset=preset-2] .swal-content__textarea:focus {
  border-color: rgba(51, 102, 255, 0.29);
}
[data-pc-preset=preset-2] .swal2-styled:focus {
  box-shadow: 0 0 0 1px #fff, 0 0 0 3px rgba(51, 102, 255, 0.4) !important;
}
[data-pc-preset=preset-2] .slider-tick.in-selection {
  background-image: linear-gradient(to bottom, #99b3ff 0, #99b3ff 100%);
}
[data-pc-preset=preset-2] .table-primary {
  --bs-table-color: #ffffff;
  --bs-table-bg: #3366ff;
  --bs-table-border-color: #4775ff;
  --bs-table-striped-bg: #3d6eff;
  --bs-table-striped-color: #ffffff;
  --bs-table-active-bg: #4775ff;
  --bs-table-active-color: #ffffff;
  --bs-table-hover-bg: #3769ff;
  --bs-table-hover-color: #ffffff;
  color: var(--bs-table-color);
  border-color: var(--bs-table-border-color);
}

[data-pc-preset=preset-3] {
  --pc-sidebar-active-color: #7265e6;
  --bs-blue: #7265e6;
  --bs-primary: #7265e6;
  --bs-primary-rgb: 114, 101, 230;
  --bs-primary-light: #f1f0fd;
  --bs-link-color: #7265e6;
  --bs-link-color-rgb: 114, 101, 230;
  --bs-link-hover-color: #5b51b8;
  --bs-link-hover-color-rgb: to-rgb(shift-color($pc-primary, $link-shade-percentage));
  --dt-row-selected: 114, 101, 230;
}
[data-pc-preset=preset-3] .bg-light-primary {
  background: #f1f0fd;
  color: #7265e6;
}
[data-pc-preset=preset-3] .link-primary {
  color: #7265e6 !important;
}
[data-pc-preset=preset-3] .link-primary:hover, [data-pc-preset=preset-3] .link-primary:focus {
  color: #5b51b8 !important;
}
[data-pc-preset=preset-3] .btn-primary {
  --bs-btn-color: #ffffff;
  --bs-btn-bg: #7265e6;
  --bs-btn-border-color: #7265e6;
  --bs-btn-hover-color: #ffffff;
  --bs-btn-hover-bg: #6156c4;
  --bs-btn-hover-border-color: #5b51b8;
  --bs-btn-focus-shadow-rgb: 135, 124, 234;
  --bs-btn-active-color: #ffffff;
  --bs-btn-active-bg: #5b51b8;
  --bs-btn-active-border-color: #564cad;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  --bs-btn-disabled-color: #ffffff;
  --bs-btn-disabled-bg: #7265e6;
  --bs-btn-disabled-border-color: #7265e6;
}
[data-pc-preset=preset-3] .btn-link {
  --bs-btn-color: #7265e6;
  --bs-btn-hover-color: #5b51b8;
  --bs-btn-active-color: #5b51b8;
}
[data-pc-preset=preset-3] .text-bg-primary {
  color: #ffffff !important;
  background-color: RGBA(114, 101, 230, var(--bs-bg-opacity, 1)) !important;
}
[data-pc-preset=preset-3] .accordion {
  --bs-accordion-btn-focus-border-color: #7265e6;
  --bs-accordion-btn-focus-box-shadow: 0 0 0 0.2rem rgba(114, 101, 230, 0.25);
  --bs-accordion-active-color: #7265e6;
  --bs-accordion-active-bg: #f1f0fd;
  --bs-accordion-btn-active-icon: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%237265e6'%3e%3cpath fill-rule=' evenodd' d=' M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/%3e%3c/svg%3e");
}
[data-pc-preset=preset-3] .alert-primary {
  --bs-alert-color: #443d8a;
  --bs-alert-bg: #e3e0fa;
  --bs-alert-border-color: #d5d1f8;
  --bs-alert-link-color: #36316e;
}
[data-pc-preset=preset-3] .list-group {
  --bs-list-group-active-bg: #7265e6;
  --bs-list-group-active-border-color: #7265e6;
}
[data-pc-preset=preset-3] .list-group-item-primary {
  color: #443d8a;
  background-color: #e3e0fa;
}
[data-pc-preset=preset-3] .nav {
  --bs-nav-link-hover-color: #5b51b8;
}
[data-pc-preset=preset-3] .nav-pills {
  --bs-nav-pills-link-active-bg: #7265e6;
}
[data-pc-preset=preset-3] .pagination {
  --bs-pagination-hover-color: #5b51b8;
  --bs-pagination-focus-color: #5b51b8;
  --bs-pagination-focus-box-shadow: 0 0 0 0.2rem rgba(114, 101, 230, 0.25);
  --bs-pagination-active-bg: #7265e6;
  --bs-pagination-active-border-color: #7265e6;
}
[data-pc-preset=preset-3] .progress {
  --bs-progress-bar-bg: #7265e6;
}
[data-pc-preset=preset-3] .form-check .form-check-input.input-primary:checked {
  border-color: #7265e6;
  background-color: #7265e6;
}
[data-pc-preset=preset-3] .form-check .form-check-input.input-light-primary:checked {
  border-color: #f1f0fd;
  background-color: #f1f0fd;
}
[data-pc-preset=preset-3] .form-check .form-check-input.input-light-primary:checked[type=checkbox] {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'%3e%3cpath fill='none' stroke='%237265e6' stroke-linecap='round' stroke-linejoin='round' stroke-width='3' d='M6 10l3 3l6-6'/%3e%3c/svg%3e");
}
[data-pc-preset=preset-3] .form-check .form-check-input.input-light-primary:checked[type=radio] {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='2' fill='%237265e6'/%3e%3c/svg%3e");
}
[data-pc-preset=preset-3] .form-check .form-check-input.input-primary:focus[type=checkbox], [data-pc-preset=preset-3] .form-check .form-check-input.input-primary:focus[type=radio], [data-pc-preset=preset-3] .form-check .form-check-input.input-light-primary:focus[type=checkbox], [data-pc-preset=preset-3] .form-check .form-check-input.input-light-primary:focus[type=radio] {
  box-shadow: 0 0 0 0.2rem rgba(114, 101, 230, 0.25);
  border-color: #7265e6;
}
[data-pc-preset=preset-3] .form-check.form-switch .form-check-input.input-light-primary:checked {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='%237265e6'/%3e%3c/svg%3e");
}
[data-pc-preset=preset-3] .btn-light-primary {
  background: #f1f0fd;
  color: #7265e6;
  border-color: #f1f0fd;
}
[data-pc-preset=preset-3] .btn-light-primary .material-icons-two-tone {
  background-color: #7265e6;
}
[data-pc-preset=preset-3] .btn-light-primary:hover {
  background: #7265e6;
  color: #fff;
  border-color: #7265e6;
}
[data-pc-preset=preset-3] .btn-light-primary.focus, [data-pc-preset=preset-3] .btn-light-primary:focus {
  background: #7265e6;
  color: #fff;
  border-color: #7265e6;
}
[data-pc-preset=preset-3] .btn-light-primary:not(:disabled):not(.disabled).active, [data-pc-preset=preset-3] .btn-light-primary:not(:disabled):not(.disabled):active, .show > [data-pc-preset=preset-3] .btn-light-primary.dropdown-toggle {
  background: #7265e6;
  color: #fff;
  border-color: #7265e6;
}
[data-pc-preset=preset-3] .btn-check:active + .btn-light-primary,
[data-pc-preset=preset-3] .btn-check:checked + .btn-light-primary {
  background: #7265e6;
  color: #fff;
  border-color: #7265e6;
}
[data-pc-preset=preset-3] .btn-link-primary {
  background: transparent;
  color: #7265e6;
  border-color: transparent;
}
[data-pc-preset=preset-3] .btn-link-primary .material-icons-two-tone {
  background-color: #7265e6;
}
[data-pc-preset=preset-3] .btn-link-primary:hover {
  background: #f1f0fd;
  color: #7265e6;
  border-color: #f1f0fd;
}
[data-pc-preset=preset-3] .btn-link-primary.focus, [data-pc-preset=preset-3] .btn-link-primary:focus {
  background: #f1f0fd;
  color: #7265e6;
  border-color: #f1f0fd;
}
[data-pc-preset=preset-3] .btn-link-primary:not(:disabled):not(.disabled).active, [data-pc-preset=preset-3] .btn-link-primary:not(:disabled):not(.disabled):active, .show > [data-pc-preset=preset-3] .btn-link-primary.dropdown-toggle {
  background: #f1f0fd;
  color: #7265e6;
  border-color: #f1f0fd;
}
[data-pc-preset=preset-3] .btn-check:active + .btn-link-primary,
[data-pc-preset=preset-3] .btn-check:checked + .btn-link-primary {
  background: #f1f0fd;
  color: #7265e6;
  border-color: #f1f0fd;
}
[data-pc-preset=preset-3] .slider-selection {
  background-image: linear-gradient(to bottom, #d6d3f8 0, #d6d3f8 100%);
}
[data-pc-preset=preset-3] .slider-selection.tick-slider-selection {
  background-image: linear-gradient(to bottom, #c2bdf4 0, #c2bdf4 100%);
}
[data-pc-preset=preset-3] .swal-button:not([disabled]):hover {
  background-color: #5e4fe2;
}
[data-pc-preset=preset-3] .swal-button:active {
  background-color: #5e4fe2;
}
[data-pc-preset=preset-3] .swal-button:focus {
  box-shadow: 0 0 0 1px #fff, 0 0 0 3px rgba(114, 101, 230, 0.29);
}
[data-pc-preset=preset-3] .swal-content__input:focus {
  border-color: rgba(114, 101, 230, 0.29);
}
[data-pc-preset=preset-3] .swal-content__textarea:focus {
  border-color: rgba(114, 101, 230, 0.29);
}
[data-pc-preset=preset-3] .swal2-styled:focus {
  box-shadow: 0 0 0 1px #fff, 0 0 0 3px rgba(114, 101, 230, 0.4) !important;
}
[data-pc-preset=preset-3] .slider-tick.in-selection {
  background-image: linear-gradient(to bottom, #c2bdf4 0, #c2bdf4 100%);
}
[data-pc-preset=preset-3] .table-primary {
  --bs-table-color: #ffffff;
  --bs-table-bg: #7265e6;
  --bs-table-border-color: #8074e9;
  --bs-table-striped-bg: #796de7;
  --bs-table-striped-color: #ffffff;
  --bs-table-active-bg: #8074e9;
  --bs-table-active-color: #ffffff;
  --bs-table-hover-bg: #7568e7;
  --bs-table-hover-color: #ffffff;
  color: var(--bs-table-color);
  border-color: var(--bs-table-border-color);
}

[data-pc-preset=preset-4] {
  --pc-sidebar-active-color: #068e44;
  --bs-blue: #068e44;
  --bs-primary: #068e44;
  --bs-primary-rgb: 6, 142, 68;
  --bs-primary-light: #e6f4ec;
  --bs-link-color: #068e44;
  --bs-link-color-rgb: 6, 142, 68;
  --bs-link-hover-color: #057236;
  --bs-link-hover-color-rgb: to-rgb(shift-color($pc-primary, $link-shade-percentage));
  --dt-row-selected: 6, 142, 68;
}
[data-pc-preset=preset-4] .bg-light-primary {
  background: #e6f4ec;
  color: #068e44;
}
[data-pc-preset=preset-4] .link-primary {
  color: #068e44 !important;
}
[data-pc-preset=preset-4] .link-primary:hover, [data-pc-preset=preset-4] .link-primary:focus {
  color: #057236 !important;
}
[data-pc-preset=preset-4] .btn-primary {
  --bs-btn-color: #ffffff;
  --bs-btn-bg: #068e44;
  --bs-btn-border-color: #068e44;
  --bs-btn-hover-color: #ffffff;
  --bs-btn-hover-bg: #05793a;
  --bs-btn-hover-border-color: #057236;
  --bs-btn-focus-shadow-rgb: 43, 159, 96;
  --bs-btn-active-color: #ffffff;
  --bs-btn-active-bg: #057236;
  --bs-btn-active-border-color: #056b33;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  --bs-btn-disabled-color: #ffffff;
  --bs-btn-disabled-bg: #068e44;
  --bs-btn-disabled-border-color: #068e44;
}
[data-pc-preset=preset-4] .btn-link {
  --bs-btn-color: #068e44;
  --bs-btn-hover-color: #057236;
  --bs-btn-active-color: #057236;
}
[data-pc-preset=preset-4] .text-bg-primary {
  color: #ffffff !important;
  background-color: RGBA(6, 142, 68, var(--bs-bg-opacity, 1)) !important;
}
[data-pc-preset=preset-4] .accordion {
  --bs-accordion-btn-focus-border-color: #068e44;
  --bs-accordion-btn-focus-box-shadow: 0 0 0 0.2rem rgba(6, 142, 68, 0.25);
  --bs-accordion-active-color: #068e44;
  --bs-accordion-active-bg: #e6f4ec;
  --bs-accordion-btn-active-icon: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23068e44'%3e%3cpath fill-rule=' evenodd' d=' M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/%3e%3c/svg%3e");
}
[data-pc-preset=preset-4] .alert-primary {
  --bs-alert-color: #045529;
  --bs-alert-bg: #cde8da;
  --bs-alert-border-color: #b4ddc7;
  --bs-alert-link-color: #034421;
}
[data-pc-preset=preset-4] .list-group {
  --bs-list-group-active-bg: #068e44;
  --bs-list-group-active-border-color: #068e44;
}
[data-pc-preset=preset-4] .list-group-item-primary {
  color: #045529;
  background-color: #cde8da;
}
[data-pc-preset=preset-4] .nav {
  --bs-nav-link-hover-color: #057236;
}
[data-pc-preset=preset-4] .nav-pills {
  --bs-nav-pills-link-active-bg: #068e44;
}
[data-pc-preset=preset-4] .pagination {
  --bs-pagination-hover-color: #057236;
  --bs-pagination-focus-color: #057236;
  --bs-pagination-focus-box-shadow: 0 0 0 0.2rem rgba(6, 142, 68, 0.25);
  --bs-pagination-active-bg: #068e44;
  --bs-pagination-active-border-color: #068e44;
}
[data-pc-preset=preset-4] .progress {
  --bs-progress-bar-bg: #068e44;
}
[data-pc-preset=preset-4] .form-check .form-check-input.input-primary:checked {
  border-color: #068e44;
  background-color: #068e44;
}
[data-pc-preset=preset-4] .form-check .form-check-input.input-light-primary:checked {
  border-color: #e6f4ec;
  background-color: #e6f4ec;
}
[data-pc-preset=preset-4] .form-check .form-check-input.input-light-primary:checked[type=checkbox] {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'%3e%3cpath fill='none' stroke='%23068e44' stroke-linecap='round' stroke-linejoin='round' stroke-width='3' d='M6 10l3 3l6-6'/%3e%3c/svg%3e");
}
[data-pc-preset=preset-4] .form-check .form-check-input.input-light-primary:checked[type=radio] {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='2' fill='%23068e44'/%3e%3c/svg%3e");
}
[data-pc-preset=preset-4] .form-check .form-check-input.input-primary:focus[type=checkbox], [data-pc-preset=preset-4] .form-check .form-check-input.input-primary:focus[type=radio], [data-pc-preset=preset-4] .form-check .form-check-input.input-light-primary:focus[type=checkbox], [data-pc-preset=preset-4] .form-check .form-check-input.input-light-primary:focus[type=radio] {
  box-shadow: 0 0 0 0.2rem rgba(6, 142, 68, 0.25);
  border-color: #068e44;
}
[data-pc-preset=preset-4] .form-check.form-switch .form-check-input.input-light-primary:checked {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='%23068e44'/%3e%3c/svg%3e");
}
[data-pc-preset=preset-4] .btn-light-primary {
  background: #e6f4ec;
  color: #068e44;
  border-color: #e6f4ec;
}
[data-pc-preset=preset-4] .btn-light-primary .material-icons-two-tone {
  background-color: #068e44;
}
[data-pc-preset=preset-4] .btn-light-primary:hover {
  background: #068e44;
  color: #fff;
  border-color: #068e44;
}
[data-pc-preset=preset-4] .btn-light-primary.focus, [data-pc-preset=preset-4] .btn-light-primary:focus {
  background: #068e44;
  color: #fff;
  border-color: #068e44;
}
[data-pc-preset=preset-4] .btn-light-primary:not(:disabled):not(.disabled).active, [data-pc-preset=preset-4] .btn-light-primary:not(:disabled):not(.disabled):active, .show > [data-pc-preset=preset-4] .btn-light-primary.dropdown-toggle {
  background: #068e44;
  color: #fff;
  border-color: #068e44;
}
[data-pc-preset=preset-4] .btn-check:active + .btn-light-primary,
[data-pc-preset=preset-4] .btn-check:checked + .btn-light-primary {
  background: #068e44;
  color: #fff;
  border-color: #068e44;
}
[data-pc-preset=preset-4] .btn-link-primary {
  background: transparent;
  color: #068e44;
  border-color: transparent;
}
[data-pc-preset=preset-4] .btn-link-primary .material-icons-two-tone {
  background-color: #068e44;
}
[data-pc-preset=preset-4] .btn-link-primary:hover {
  background: #e6f4ec;
  color: #068e44;
  border-color: #e6f4ec;
}
[data-pc-preset=preset-4] .btn-link-primary.focus, [data-pc-preset=preset-4] .btn-link-primary:focus {
  background: #e6f4ec;
  color: #068e44;
  border-color: #e6f4ec;
}
[data-pc-preset=preset-4] .btn-link-primary:not(:disabled):not(.disabled).active, [data-pc-preset=preset-4] .btn-link-primary:not(:disabled):not(.disabled):active, .show > [data-pc-preset=preset-4] .btn-link-primary.dropdown-toggle {
  background: #e6f4ec;
  color: #068e44;
  border-color: #e6f4ec;
}
[data-pc-preset=preset-4] .btn-check:active + .btn-link-primary,
[data-pc-preset=preset-4] .btn-check:checked + .btn-link-primary {
  background: #e6f4ec;
  color: #068e44;
  border-color: #e6f4ec;
}
[data-pc-preset=preset-4] .slider-selection {
  background-image: linear-gradient(to bottom, #1ef580 0, #1ef580 100%);
}
[data-pc-preset=preset-4] .slider-selection.tick-slider-selection {
  background-image: linear-gradient(to bottom, #0af073 0, #0af073 100%);
}
[data-pc-preset=preset-4] .swal-button:not([disabled]):hover {
  background-color: #057638;
}
[data-pc-preset=preset-4] .swal-button:active {
  background-color: #057638;
}
[data-pc-preset=preset-4] .swal-button:focus {
  box-shadow: 0 0 0 1px #fff, 0 0 0 3px rgba(6, 142, 68, 0.29);
}
[data-pc-preset=preset-4] .swal-content__input:focus {
  border-color: rgba(6, 142, 68, 0.29);
}
[data-pc-preset=preset-4] .swal-content__textarea:focus {
  border-color: rgba(6, 142, 68, 0.29);
}
[data-pc-preset=preset-4] .swal2-styled:focus {
  box-shadow: 0 0 0 1px #fff, 0 0 0 3px rgba(6, 142, 68, 0.4) !important;
}
[data-pc-preset=preset-4] .slider-tick.in-selection {
  background-image: linear-gradient(to bottom, #0af073 0, #0af073 100%);
}
[data-pc-preset=preset-4] .table-primary {
  --bs-table-color: #ffffff;
  --bs-table-bg: #068e44;
  --bs-table-border-color: #1f9957;
  --bs-table-striped-bg: #12944d;
  --bs-table-striped-color: #ffffff;
  --bs-table-active-bg: #1f9957;
  --bs-table-active-color: #ffffff;
  --bs-table-hover-bg: #0b9048;
  --bs-table-hover-color: #ffffff;
  color: var(--bs-table-color);
  border-color: var(--bs-table-border-color);
}

[data-pc-preset=preset-5] {
  --pc-sidebar-active-color: #3c64d0;
  --bs-blue: #3c64d0;
  --bs-primary: #3c64d0;
  --bs-primary-rgb: 60, 100, 208;
  --bs-primary-light: #ecf0fa;
  --bs-link-color: #3c64d0;
  --bs-link-color-rgb: 60, 100, 208;
  --bs-link-hover-color: #3050a6;
  --bs-link-hover-color-rgb: to-rgb(shift-color($pc-primary, $link-shade-percentage));
  --dt-row-selected: 60, 100, 208;
}
[data-pc-preset=preset-5] .bg-light-primary {
  background: #ecf0fa;
  color: #3c64d0;
}
[data-pc-preset=preset-5] .link-primary {
  color: #3c64d0 !important;
}
[data-pc-preset=preset-5] .link-primary:hover, [data-pc-preset=preset-5] .link-primary:focus {
  color: #3050a6 !important;
}
[data-pc-preset=preset-5] .btn-primary {
  --bs-btn-color: #ffffff;
  --bs-btn-bg: #3c64d0;
  --bs-btn-border-color: #3c64d0;
  --bs-btn-hover-color: #ffffff;
  --bs-btn-hover-bg: #3355b1;
  --bs-btn-hover-border-color: #3050a6;
  --bs-btn-focus-shadow-rgb: 89, 123, 215;
  --bs-btn-active-color: #ffffff;
  --bs-btn-active-bg: #3050a6;
  --bs-btn-active-border-color: #2d4b9c;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  --bs-btn-disabled-color: #ffffff;
  --bs-btn-disabled-bg: #3c64d0;
  --bs-btn-disabled-border-color: #3c64d0;
}
[data-pc-preset=preset-5] .btn-link {
  --bs-btn-color: #3c64d0;
  --bs-btn-hover-color: #3050a6;
  --bs-btn-active-color: #3050a6;
}
[data-pc-preset=preset-5] .text-bg-primary {
  color: #ffffff !important;
  background-color: RGBA(60, 100, 208, var(--bs-bg-opacity, 1)) !important;
}
[data-pc-preset=preset-5] .accordion {
  --bs-accordion-btn-focus-border-color: #3c64d0;
  --bs-accordion-btn-focus-box-shadow: 0 0 0 0.2rem rgba(60, 100, 208, 0.25);
  --bs-accordion-active-color: #3c64d0;
  --bs-accordion-active-bg: #ecf0fa;
  --bs-accordion-btn-active-icon: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%233c64d0'%3e%3cpath fill-rule=' evenodd' d=' M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/%3e%3c/svg%3e");
}
[data-pc-preset=preset-5] .alert-primary {
  --bs-alert-color: #243c7d;
  --bs-alert-bg: #d8e0f6;
  --bs-alert-border-color: #c5d1f1;
  --bs-alert-link-color: #1d3064;
}
[data-pc-preset=preset-5] .list-group {
  --bs-list-group-active-bg: #3c64d0;
  --bs-list-group-active-border-color: #3c64d0;
}
[data-pc-preset=preset-5] .list-group-item-primary {
  color: #243c7d;
  background-color: #d8e0f6;
}
[data-pc-preset=preset-5] .nav {
  --bs-nav-link-hover-color: #3050a6;
}
[data-pc-preset=preset-5] .nav-pills {
  --bs-nav-pills-link-active-bg: #3c64d0;
}
[data-pc-preset=preset-5] .pagination {
  --bs-pagination-hover-color: #3050a6;
  --bs-pagination-focus-color: #3050a6;
  --bs-pagination-focus-box-shadow: 0 0 0 0.2rem rgba(60, 100, 208, 0.25);
  --bs-pagination-active-bg: #3c64d0;
  --bs-pagination-active-border-color: #3c64d0;
}
[data-pc-preset=preset-5] .progress {
  --bs-progress-bar-bg: #3c64d0;
}
[data-pc-preset=preset-5] .form-check .form-check-input.input-primary:checked {
  border-color: #3c64d0;
  background-color: #3c64d0;
}
[data-pc-preset=preset-5] .form-check .form-check-input.input-light-primary:checked {
  border-color: #ecf0fa;
  background-color: #ecf0fa;
}
[data-pc-preset=preset-5] .form-check .form-check-input.input-light-primary:checked[type=checkbox] {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'%3e%3cpath fill='none' stroke='%233c64d0' stroke-linecap='round' stroke-linejoin='round' stroke-width='3' d='M6 10l3 3l6-6'/%3e%3c/svg%3e");
}
[data-pc-preset=preset-5] .form-check .form-check-input.input-light-primary:checked[type=radio] {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='2' fill='%233c64d0'/%3e%3c/svg%3e");
}
[data-pc-preset=preset-5] .form-check .form-check-input.input-primary:focus[type=checkbox], [data-pc-preset=preset-5] .form-check .form-check-input.input-primary:focus[type=radio], [data-pc-preset=preset-5] .form-check .form-check-input.input-light-primary:focus[type=checkbox], [data-pc-preset=preset-5] .form-check .form-check-input.input-light-primary:focus[type=radio] {
  box-shadow: 0 0 0 0.2rem rgba(60, 100, 208, 0.25);
  border-color: #3c64d0;
}
[data-pc-preset=preset-5] .form-check.form-switch .form-check-input.input-light-primary:checked {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='%233c64d0'/%3e%3c/svg%3e");
}
[data-pc-preset=preset-5] .btn-light-primary {
  background: #ecf0fa;
  color: #3c64d0;
  border-color: #ecf0fa;
}
[data-pc-preset=preset-5] .btn-light-primary .material-icons-two-tone {
  background-color: #3c64d0;
}
[data-pc-preset=preset-5] .btn-light-primary:hover {
  background: #3c64d0;
  color: #fff;
  border-color: #3c64d0;
}
[data-pc-preset=preset-5] .btn-light-primary.focus, [data-pc-preset=preset-5] .btn-light-primary:focus {
  background: #3c64d0;
  color: #fff;
  border-color: #3c64d0;
}
[data-pc-preset=preset-5] .btn-light-primary:not(:disabled):not(.disabled).active, [data-pc-preset=preset-5] .btn-light-primary:not(:disabled):not(.disabled):active, .show > [data-pc-preset=preset-5] .btn-light-primary.dropdown-toggle {
  background: #3c64d0;
  color: #fff;
  border-color: #3c64d0;
}
[data-pc-preset=preset-5] .btn-check:active + .btn-light-primary,
[data-pc-preset=preset-5] .btn-check:checked + .btn-light-primary {
  background: #3c64d0;
  color: #fff;
  border-color: #3c64d0;
}
[data-pc-preset=preset-5] .btn-link-primary {
  background: transparent;
  color: #3c64d0;
  border-color: transparent;
}
[data-pc-preset=preset-5] .btn-link-primary .material-icons-two-tone {
  background-color: #3c64d0;
}
[data-pc-preset=preset-5] .btn-link-primary:hover {
  background: #ecf0fa;
  color: #3c64d0;
  border-color: #ecf0fa;
}
[data-pc-preset=preset-5] .btn-link-primary.focus, [data-pc-preset=preset-5] .btn-link-primary:focus {
  background: #ecf0fa;
  color: #3c64d0;
  border-color: #ecf0fa;
}
[data-pc-preset=preset-5] .btn-link-primary:not(:disabled):not(.disabled).active, [data-pc-preset=preset-5] .btn-link-primary:not(:disabled):not(.disabled):active, .show > [data-pc-preset=preset-5] .btn-link-primary.dropdown-toggle {
  background: #ecf0fa;
  color: #3c64d0;
  border-color: #ecf0fa;
}
[data-pc-preset=preset-5] .btn-check:active + .btn-link-primary,
[data-pc-preset=preset-5] .btn-check:checked + .btn-link-primary {
  background: #ecf0fa;
  color: #3c64d0;
  border-color: #ecf0fa;
}
[data-pc-preset=preset-5] .slider-selection {
  background-image: linear-gradient(to bottom, #a3b6e9 0, #a3b6e9 100%);
}
[data-pc-preset=preset-5] .slider-selection.tick-slider-selection {
  background-image: linear-gradient(to bottom, #8ea5e4 0, #8ea5e4 100%);
}
[data-pc-preset=preset-5] .swal-button:not([disabled]):hover {
  background-color: #2f57c3;
}
[data-pc-preset=preset-5] .swal-button:active {
  background-color: #2f57c3;
}
[data-pc-preset=preset-5] .swal-button:focus {
  box-shadow: 0 0 0 1px #fff, 0 0 0 3px rgba(60, 100, 208, 0.29);
}
[data-pc-preset=preset-5] .swal-content__input:focus {
  border-color: rgba(60, 100, 208, 0.29);
}
[data-pc-preset=preset-5] .swal-content__textarea:focus {
  border-color: rgba(60, 100, 208, 0.29);
}
[data-pc-preset=preset-5] .swal2-styled:focus {
  box-shadow: 0 0 0 1px #fff, 0 0 0 3px rgba(60, 100, 208, 0.4) !important;
}
[data-pc-preset=preset-5] .slider-tick.in-selection {
  background-image: linear-gradient(to bottom, #8ea5e4 0, #8ea5e4 100%);
}
[data-pc-preset=preset-5] .table-primary {
  --bs-table-color: #ffffff;
  --bs-table-bg: #3c64d0;
  --bs-table-border-color: #5074d5;
  --bs-table-striped-bg: #466cd2;
  --bs-table-striped-color: #ffffff;
  --bs-table-active-bg: #5074d5;
  --bs-table-active-color: #ffffff;
  --bs-table-hover-bg: #4067d1;
  --bs-table-hover-color: #ffffff;
  color: var(--bs-table-color);
  border-color: var(--bs-table-border-color);
}

[data-pc-preset=preset-6] {
  --pc-sidebar-active-color: #f27013;
  --bs-blue: #f27013;
  --bs-primary: #f27013;
  --bs-primary-rgb: 242, 112, 19;
  --bs-primary-light: #fef1e7;
  --bs-link-color: #f27013;
  --bs-link-color-rgb: 242, 112, 19;
  --bs-link-hover-color: #c25a0f;
  --bs-link-hover-color-rgb: to-rgb(shift-color($pc-primary, $link-shade-percentage));
  --dt-row-selected: 242, 112, 19;
}
[data-pc-preset=preset-6] .bg-light-primary {
  background: #fef1e7;
  color: #f27013;
}
[data-pc-preset=preset-6] .link-primary {
  color: #f27013 !important;
}
[data-pc-preset=preset-6] .link-primary:hover, [data-pc-preset=preset-6] .link-primary:focus {
  color: #c25a0f !important;
}
[data-pc-preset=preset-6] .btn-primary {
  --bs-btn-color: #ffffff;
  --bs-btn-bg: #f27013;
  --bs-btn-border-color: #f27013;
  --bs-btn-hover-color: #ffffff;
  --bs-btn-hover-bg: #ce5f10;
  --bs-btn-hover-border-color: #c25a0f;
  --bs-btn-focus-shadow-rgb: 244, 133, 54;
  --bs-btn-active-color: #ffffff;
  --bs-btn-active-bg: #c25a0f;
  --bs-btn-active-border-color: #b6540e;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  --bs-btn-disabled-color: #ffffff;
  --bs-btn-disabled-bg: #f27013;
  --bs-btn-disabled-border-color: #f27013;
}
[data-pc-preset=preset-6] .btn-link {
  --bs-btn-color: #f27013;
  --bs-btn-hover-color: #c25a0f;
  --bs-btn-active-color: #c25a0f;
}
[data-pc-preset=preset-6] .text-bg-primary {
  color: #ffffff !important;
  background-color: RGBA(242, 112, 19, var(--bs-bg-opacity, 1)) !important;
}
[data-pc-preset=preset-6] .accordion {
  --bs-accordion-btn-focus-border-color: #f27013;
  --bs-accordion-btn-focus-box-shadow: 0 0 0 0.2rem rgba(242, 112, 19, 0.25);
  --bs-accordion-active-color: #f27013;
  --bs-accordion-active-bg: #fef1e7;
  --bs-accordion-btn-active-icon: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23f27013'%3e%3cpath fill-rule=' evenodd' d=' M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/%3e%3c/svg%3e");
}
[data-pc-preset=preset-6] .alert-primary {
  --bs-alert-color: #91430b;
  --bs-alert-bg: #fce2d0;
  --bs-alert-border-color: #fbd4b8;
  --bs-alert-link-color: #743609;
}
[data-pc-preset=preset-6] .list-group {
  --bs-list-group-active-bg: #f27013;
  --bs-list-group-active-border-color: #f27013;
}
[data-pc-preset=preset-6] .list-group-item-primary {
  color: #91430b;
  background-color: #fce2d0;
}
[data-pc-preset=preset-6] .nav {
  --bs-nav-link-hover-color: #c25a0f;
}
[data-pc-preset=preset-6] .nav-pills {
  --bs-nav-pills-link-active-bg: #f27013;
}
[data-pc-preset=preset-6] .pagination {
  --bs-pagination-hover-color: #c25a0f;
  --bs-pagination-focus-color: #c25a0f;
  --bs-pagination-focus-box-shadow: 0 0 0 0.2rem rgba(242, 112, 19, 0.25);
  --bs-pagination-active-bg: #f27013;
  --bs-pagination-active-border-color: #f27013;
}
[data-pc-preset=preset-6] .progress {
  --bs-progress-bar-bg: #f27013;
}
[data-pc-preset=preset-6] .form-check .form-check-input.input-primary:checked {
  border-color: #f27013;
  background-color: #f27013;
}
[data-pc-preset=preset-6] .form-check .form-check-input.input-light-primary:checked {
  border-color: #fef1e7;
  background-color: #fef1e7;
}
[data-pc-preset=preset-6] .form-check .form-check-input.input-light-primary:checked[type=checkbox] {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'%3e%3cpath fill='none' stroke='%23f27013' stroke-linecap='round' stroke-linejoin='round' stroke-width='3' d='M6 10l3 3l6-6'/%3e%3c/svg%3e");
}
[data-pc-preset=preset-6] .form-check .form-check-input.input-light-primary:checked[type=radio] {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='2' fill='%23f27013'/%3e%3c/svg%3e");
}
[data-pc-preset=preset-6] .form-check .form-check-input.input-primary:focus[type=checkbox], [data-pc-preset=preset-6] .form-check .form-check-input.input-primary:focus[type=radio], [data-pc-preset=preset-6] .form-check .form-check-input.input-light-primary:focus[type=checkbox], [data-pc-preset=preset-6] .form-check .form-check-input.input-light-primary:focus[type=radio] {
  box-shadow: 0 0 0 0.2rem rgba(242, 112, 19, 0.25);
  border-color: #f27013;
}
[data-pc-preset=preset-6] .form-check.form-switch .form-check-input.input-light-primary:checked {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='%23f27013'/%3e%3c/svg%3e");
}
[data-pc-preset=preset-6] .btn-light-primary {
  background: #fef1e7;
  color: #f27013;
  border-color: #fef1e7;
}
[data-pc-preset=preset-6] .btn-light-primary .material-icons-two-tone {
  background-color: #f27013;
}
[data-pc-preset=preset-6] .btn-light-primary:hover {
  background: #f27013;
  color: #fff;
  border-color: #f27013;
}
[data-pc-preset=preset-6] .btn-light-primary.focus, [data-pc-preset=preset-6] .btn-light-primary:focus {
  background: #f27013;
  color: #fff;
  border-color: #f27013;
}
[data-pc-preset=preset-6] .btn-light-primary:not(:disabled):not(.disabled).active, [data-pc-preset=preset-6] .btn-light-primary:not(:disabled):not(.disabled):active, .show > [data-pc-preset=preset-6] .btn-light-primary.dropdown-toggle {
  background: #f27013;
  color: #fff;
  border-color: #f27013;
}
[data-pc-preset=preset-6] .btn-check:active + .btn-light-primary,
[data-pc-preset=preset-6] .btn-check:checked + .btn-light-primary {
  background: #f27013;
  color: #fff;
  border-color: #f27013;
}
[data-pc-preset=preset-6] .btn-link-primary {
  background: transparent;
  color: #f27013;
  border-color: transparent;
}
[data-pc-preset=preset-6] .btn-link-primary .material-icons-two-tone {
  background-color: #f27013;
}
[data-pc-preset=preset-6] .btn-link-primary:hover {
  background: #fef1e7;
  color: #f27013;
  border-color: #fef1e7;
}
[data-pc-preset=preset-6] .btn-link-primary.focus, [data-pc-preset=preset-6] .btn-link-primary:focus {
  background: #fef1e7;
  color: #f27013;
  border-color: #fef1e7;
}
[data-pc-preset=preset-6] .btn-link-primary:not(:disabled):not(.disabled).active, [data-pc-preset=preset-6] .btn-link-primary:not(:disabled):not(.disabled):active, .show > [data-pc-preset=preset-6] .btn-link-primary.dropdown-toggle {
  background: #fef1e7;
  color: #f27013;
  border-color: #fef1e7;
}
[data-pc-preset=preset-6] .btn-check:active + .btn-link-primary,
[data-pc-preset=preset-6] .btn-check:checked + .btn-link-primary {
  background: #fef1e7;
  color: #f27013;
  border-color: #fef1e7;
}
[data-pc-preset=preset-6] .slider-selection {
  background-image: linear-gradient(to bottom, #f9b98c 0, #f9b98c 100%);
}
[data-pc-preset=preset-6] .slider-selection.tick-slider-selection {
  background-image: linear-gradient(to bottom, #f7ab74 0, #f7ab74 100%);
}
[data-pc-preset=preset-6] .swal-button:not([disabled]):hover {
  background-color: #df640c;
}
[data-pc-preset=preset-6] .swal-button:active {
  background-color: #df640c;
}
[data-pc-preset=preset-6] .swal-button:focus {
  box-shadow: 0 0 0 1px #fff, 0 0 0 3px rgba(242, 112, 19, 0.29);
}
[data-pc-preset=preset-6] .swal-content__input:focus {
  border-color: rgba(242, 112, 19, 0.29);
}
[data-pc-preset=preset-6] .swal-content__textarea:focus {
  border-color: rgba(242, 112, 19, 0.29);
}
[data-pc-preset=preset-6] .swal2-styled:focus {
  box-shadow: 0 0 0 1px #fff, 0 0 0 3px rgba(242, 112, 19, 0.4) !important;
}
[data-pc-preset=preset-6] .slider-tick.in-selection {
  background-image: linear-gradient(to bottom, #f7ab74 0, #f7ab74 100%);
}
[data-pc-preset=preset-6] .table-primary {
  --bs-table-color: #ffffff;
  --bs-table-bg: #f27013;
  --bs-table-border-color: #f37e2b;
  --bs-table-striped-bg: #f3771f;
  --bs-table-striped-color: #ffffff;
  --bs-table-active-bg: #f37e2b;
  --bs-table-active-color: #ffffff;
  --bs-table-hover-bg: #f27318;
  --bs-table-hover-color: #ffffff;
  color: var(--bs-table-color);
  border-color: var(--bs-table-border-color);
}

[data-pc-preset=preset-7] {
  --pc-sidebar-active-color: #2aa1af;
  --bs-blue: #2aa1af;
  --bs-primary: #2aa1af;
  --bs-primary-rgb: 42, 161, 175;
  --bs-primary-light: #eaf6f7;
  --bs-link-color: #2aa1af;
  --bs-link-color-rgb: 42, 161, 175;
  --bs-link-hover-color: #22818c;
  --bs-link-hover-color-rgb: to-rgb(shift-color($pc-primary, $link-shade-percentage));
  --dt-row-selected: 42, 161, 175;
}
[data-pc-preset=preset-7] .bg-light-primary {
  background: #eaf6f7;
  color: #2aa1af;
}
[data-pc-preset=preset-7] .link-primary {
  color: #2aa1af !important;
}
[data-pc-preset=preset-7] .link-primary:hover, [data-pc-preset=preset-7] .link-primary:focus {
  color: #22818c !important;
}
[data-pc-preset=preset-7] .btn-primary {
  --bs-btn-color: #ffffff;
  --bs-btn-bg: #2aa1af;
  --bs-btn-border-color: #2aa1af;
  --bs-btn-hover-color: #ffffff;
  --bs-btn-hover-bg: #248995;
  --bs-btn-hover-border-color: #22818c;
  --bs-btn-focus-shadow-rgb: 74, 175, 187;
  --bs-btn-active-color: #ffffff;
  --bs-btn-active-bg: #22818c;
  --bs-btn-active-border-color: #207983;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  --bs-btn-disabled-color: #ffffff;
  --bs-btn-disabled-bg: #2aa1af;
  --bs-btn-disabled-border-color: #2aa1af;
}
[data-pc-preset=preset-7] .btn-link {
  --bs-btn-color: #2aa1af;
  --bs-btn-hover-color: #22818c;
  --bs-btn-active-color: #22818c;
}
[data-pc-preset=preset-7] .text-bg-primary {
  color: #ffffff !important;
  background-color: RGBA(42, 161, 175, var(--bs-bg-opacity, 1)) !important;
}
[data-pc-preset=preset-7] .accordion {
  --bs-accordion-btn-focus-border-color: #2aa1af;
  --bs-accordion-btn-focus-box-shadow: 0 0 0 0.2rem rgba(42, 161, 175, 0.25);
  --bs-accordion-active-color: #2aa1af;
  --bs-accordion-active-bg: #eaf6f7;
  --bs-accordion-btn-active-icon: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%232aa1af'%3e%3cpath fill-rule=' evenodd' d=' M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/%3e%3c/svg%3e");
}
[data-pc-preset=preset-7] .alert-primary {
  --bs-alert-color: #196169;
  --bs-alert-bg: #d4ecef;
  --bs-alert-border-color: #bfe3e7;
  --bs-alert-link-color: #144e54;
}
[data-pc-preset=preset-7] .list-group {
  --bs-list-group-active-bg: #2aa1af;
  --bs-list-group-active-border-color: #2aa1af;
}
[data-pc-preset=preset-7] .list-group-item-primary {
  color: #196169;
  background-color: #d4ecef;
}
[data-pc-preset=preset-7] .nav {
  --bs-nav-link-hover-color: #22818c;
}
[data-pc-preset=preset-7] .nav-pills {
  --bs-nav-pills-link-active-bg: #2aa1af;
}
[data-pc-preset=preset-7] .pagination {
  --bs-pagination-hover-color: #22818c;
  --bs-pagination-focus-color: #22818c;
  --bs-pagination-focus-box-shadow: 0 0 0 0.2rem rgba(42, 161, 175, 0.25);
  --bs-pagination-active-bg: #2aa1af;
  --bs-pagination-active-border-color: #2aa1af;
}
[data-pc-preset=preset-7] .progress {
  --bs-progress-bar-bg: #2aa1af;
}
[data-pc-preset=preset-7] .form-check .form-check-input.input-primary:checked {
  border-color: #2aa1af;
  background-color: #2aa1af;
}
[data-pc-preset=preset-7] .form-check .form-check-input.input-light-primary:checked {
  border-color: #eaf6f7;
  background-color: #eaf6f7;
}
[data-pc-preset=preset-7] .form-check .form-check-input.input-light-primary:checked[type=checkbox] {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'%3e%3cpath fill='none' stroke='%232aa1af' stroke-linecap='round' stroke-linejoin='round' stroke-width='3' d='M6 10l3 3l6-6'/%3e%3c/svg%3e");
}
[data-pc-preset=preset-7] .form-check .form-check-input.input-light-primary:checked[type=radio] {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='2' fill='%232aa1af'/%3e%3c/svg%3e");
}
[data-pc-preset=preset-7] .form-check .form-check-input.input-primary:focus[type=checkbox], [data-pc-preset=preset-7] .form-check .form-check-input.input-primary:focus[type=radio], [data-pc-preset=preset-7] .form-check .form-check-input.input-light-primary:focus[type=checkbox], [data-pc-preset=preset-7] .form-check .form-check-input.input-light-primary:focus[type=radio] {
  box-shadow: 0 0 0 0.2rem rgba(42, 161, 175, 0.25);
  border-color: #2aa1af;
}
[data-pc-preset=preset-7] .form-check.form-switch .form-check-input.input-light-primary:checked {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='%232aa1af'/%3e%3c/svg%3e");
}
[data-pc-preset=preset-7] .btn-light-primary {
  background: #eaf6f7;
  color: #2aa1af;
  border-color: #eaf6f7;
}
[data-pc-preset=preset-7] .btn-light-primary .material-icons-two-tone {
  background-color: #2aa1af;
}
[data-pc-preset=preset-7] .btn-light-primary:hover {
  background: #2aa1af;
  color: #fff;
  border-color: #2aa1af;
}
[data-pc-preset=preset-7] .btn-light-primary.focus, [data-pc-preset=preset-7] .btn-light-primary:focus {
  background: #2aa1af;
  color: #fff;
  border-color: #2aa1af;
}
[data-pc-preset=preset-7] .btn-light-primary:not(:disabled):not(.disabled).active, [data-pc-preset=preset-7] .btn-light-primary:not(:disabled):not(.disabled):active, .show > [data-pc-preset=preset-7] .btn-light-primary.dropdown-toggle {
  background: #2aa1af;
  color: #fff;
  border-color: #2aa1af;
}
[data-pc-preset=preset-7] .btn-check:active + .btn-light-primary,
[data-pc-preset=preset-7] .btn-check:checked + .btn-light-primary {
  background: #2aa1af;
  color: #fff;
  border-color: #2aa1af;
}
[data-pc-preset=preset-7] .btn-link-primary {
  background: transparent;
  color: #2aa1af;
  border-color: transparent;
}
[data-pc-preset=preset-7] .btn-link-primary .material-icons-two-tone {
  background-color: #2aa1af;
}
[data-pc-preset=preset-7] .btn-link-primary:hover {
  background: #eaf6f7;
  color: #2aa1af;
  border-color: #eaf6f7;
}
[data-pc-preset=preset-7] .btn-link-primary.focus, [data-pc-preset=preset-7] .btn-link-primary:focus {
  background: #eaf6f7;
  color: #2aa1af;
  border-color: #eaf6f7;
}
[data-pc-preset=preset-7] .btn-link-primary:not(:disabled):not(.disabled).active, [data-pc-preset=preset-7] .btn-link-primary:not(:disabled):not(.disabled):active, .show > [data-pc-preset=preset-7] .btn-link-primary.dropdown-toggle {
  background: #eaf6f7;
  color: #2aa1af;
  border-color: #eaf6f7;
}
[data-pc-preset=preset-7] .btn-check:active + .btn-link-primary,
[data-pc-preset=preset-7] .btn-check:checked + .btn-link-primary {
  background: #eaf6f7;
  color: #2aa1af;
  border-color: #eaf6f7;
}
[data-pc-preset=preset-7] .slider-selection {
  background-image: linear-gradient(to bottom, #7ad4df 0, #7ad4df 100%);
}
[data-pc-preset=preset-7] .slider-selection.tick-slider-selection {
  background-image: linear-gradient(to bottom, #65ceda 0, #65ceda 100%);
}
[data-pc-preset=preset-7] .swal-button:not([disabled]):hover {
  background-color: #258e9a;
}
[data-pc-preset=preset-7] .swal-button:active {
  background-color: #258e9a;
}
[data-pc-preset=preset-7] .swal-button:focus {
  box-shadow: 0 0 0 1px #fff, 0 0 0 3px rgba(42, 161, 175, 0.29);
}
[data-pc-preset=preset-7] .swal-content__input:focus {
  border-color: rgba(42, 161, 175, 0.29);
}
[data-pc-preset=preset-7] .swal-content__textarea:focus {
  border-color: rgba(42, 161, 175, 0.29);
}
[data-pc-preset=preset-7] .swal2-styled:focus {
  box-shadow: 0 0 0 1px #fff, 0 0 0 3px rgba(42, 161, 175, 0.4) !important;
}
[data-pc-preset=preset-7] .slider-tick.in-selection {
  background-image: linear-gradient(to bottom, #65ceda 0, #65ceda 100%);
}
[data-pc-preset=preset-7] .table-primary {
  --bs-table-color: #ffffff;
  --bs-table-bg: #2aa1af;
  --bs-table-border-color: #3faab7;
  --bs-table-striped-bg: #35a6b3;
  --bs-table-striped-color: #ffffff;
  --bs-table-active-bg: #3faab7;
  --bs-table-active-color: #ffffff;
  --bs-table-hover-bg: #2ea3b1;
  --bs-table-hover-color: #ffffff;
  color: var(--bs-table-color);
  border-color: var(--bs-table-border-color);
}

[data-pc-preset=preset-8] {
  --pc-sidebar-active-color: #00a854;
  --bs-blue: #00a854;
  --bs-primary: #00a854;
  --bs-primary-rgb: 0, 168, 84;
  --bs-primary-light: #e6f6ee;
  --bs-link-color: #00a854;
  --bs-link-color-rgb: 0, 168, 84;
  --bs-link-hover-color: #008643;
  --bs-link-hover-color-rgb: to-rgb(shift-color($pc-primary, $link-shade-percentage));
  --dt-row-selected: 0, 168, 84;
}
[data-pc-preset=preset-8] .bg-light-primary {
  background: #e6f6ee;
  color: #00a854;
}
[data-pc-preset=preset-8] .link-primary {
  color: #00a854 !important;
}
[data-pc-preset=preset-8] .link-primary:hover, [data-pc-preset=preset-8] .link-primary:focus {
  color: #008643 !important;
}
[data-pc-preset=preset-8] .btn-primary {
  --bs-btn-color: #ffffff;
  --bs-btn-bg: #00a854;
  --bs-btn-border-color: #00a854;
  --bs-btn-hover-color: #ffffff;
  --bs-btn-hover-bg: #008f47;
  --bs-btn-hover-border-color: #008643;
  --bs-btn-focus-shadow-rgb: 38, 181, 110;
  --bs-btn-active-color: #ffffff;
  --bs-btn-active-bg: #008643;
  --bs-btn-active-border-color: #007e3f;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  --bs-btn-disabled-color: #ffffff;
  --bs-btn-disabled-bg: #00a854;
  --bs-btn-disabled-border-color: #00a854;
}
[data-pc-preset=preset-8] .btn-link {
  --bs-btn-color: #00a854;
  --bs-btn-hover-color: #008643;
  --bs-btn-active-color: #008643;
}
[data-pc-preset=preset-8] .text-bg-primary {
  color: #ffffff !important;
  background-color: RGBA(0, 168, 84, var(--bs-bg-opacity, 1)) !important;
}
[data-pc-preset=preset-8] .accordion {
  --bs-accordion-btn-focus-border-color: #00a854;
  --bs-accordion-btn-focus-box-shadow: 0 0 0 0.2rem rgba(0, 168, 84, 0.25);
  --bs-accordion-active-color: #00a854;
  --bs-accordion-active-bg: #e6f6ee;
  --bs-accordion-btn-active-icon: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%2300a854'%3e%3cpath fill-rule=' evenodd' d=' M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/%3e%3c/svg%3e");
}
[data-pc-preset=preset-8] .alert-primary {
  --bs-alert-color: #006532;
  --bs-alert-bg: #cceedd;
  --bs-alert-border-color: #b3e5cc;
  --bs-alert-link-color: #005128;
}
[data-pc-preset=preset-8] .list-group {
  --bs-list-group-active-bg: #00a854;
  --bs-list-group-active-border-color: #00a854;
}
[data-pc-preset=preset-8] .list-group-item-primary {
  color: #006532;
  background-color: #cceedd;
}
[data-pc-preset=preset-8] .nav {
  --bs-nav-link-hover-color: #008643;
}
[data-pc-preset=preset-8] .nav-pills {
  --bs-nav-pills-link-active-bg: #00a854;
}
[data-pc-preset=preset-8] .pagination {
  --bs-pagination-hover-color: #008643;
  --bs-pagination-focus-color: #008643;
  --bs-pagination-focus-box-shadow: 0 0 0 0.2rem rgba(0, 168, 84, 0.25);
  --bs-pagination-active-bg: #00a854;
  --bs-pagination-active-border-color: #00a854;
}
[data-pc-preset=preset-8] .progress {
  --bs-progress-bar-bg: #00a854;
}
[data-pc-preset=preset-8] .form-check .form-check-input.input-primary:checked {
  border-color: #00a854;
  background-color: #00a854;
}
[data-pc-preset=preset-8] .form-check .form-check-input.input-light-primary:checked {
  border-color: #e6f6ee;
  background-color: #e6f6ee;
}
[data-pc-preset=preset-8] .form-check .form-check-input.input-light-primary:checked[type=checkbox] {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'%3e%3cpath fill='none' stroke='%2300a854' stroke-linecap='round' stroke-linejoin='round' stroke-width='3' d='M6 10l3 3l6-6'/%3e%3c/svg%3e");
}
[data-pc-preset=preset-8] .form-check .form-check-input.input-light-primary:checked[type=radio] {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='2' fill='%2300a854'/%3e%3c/svg%3e");
}
[data-pc-preset=preset-8] .form-check .form-check-input.input-primary:focus[type=checkbox], [data-pc-preset=preset-8] .form-check .form-check-input.input-primary:focus[type=radio], [data-pc-preset=preset-8] .form-check .form-check-input.input-light-primary:focus[type=checkbox], [data-pc-preset=preset-8] .form-check .form-check-input.input-light-primary:focus[type=radio] {
  box-shadow: 0 0 0 0.2rem rgba(0, 168, 84, 0.25);
  border-color: #00a854;
}
[data-pc-preset=preset-8] .form-check.form-switch .form-check-input.input-light-primary:checked {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='%2300a854'/%3e%3c/svg%3e");
}
[data-pc-preset=preset-8] .btn-light-primary {
  background: #e6f6ee;
  color: #00a854;
  border-color: #e6f6ee;
}
[data-pc-preset=preset-8] .btn-light-primary .material-icons-two-tone {
  background-color: #00a854;
}
[data-pc-preset=preset-8] .btn-light-primary:hover {
  background: #00a854;
  color: #fff;
  border-color: #00a854;
}
[data-pc-preset=preset-8] .btn-light-primary.focus, [data-pc-preset=preset-8] .btn-light-primary:focus {
  background: #00a854;
  color: #fff;
  border-color: #00a854;
}
[data-pc-preset=preset-8] .btn-light-primary:not(:disabled):not(.disabled).active, [data-pc-preset=preset-8] .btn-light-primary:not(:disabled):not(.disabled):active, .show > [data-pc-preset=preset-8] .btn-light-primary.dropdown-toggle {
  background: #00a854;
  color: #fff;
  border-color: #00a854;
}
[data-pc-preset=preset-8] .btn-check:active + .btn-light-primary,
[data-pc-preset=preset-8] .btn-check:checked + .btn-light-primary {
  background: #00a854;
  color: #fff;
  border-color: #00a854;
}
[data-pc-preset=preset-8] .btn-link-primary {
  background: transparent;
  color: #00a854;
  border-color: transparent;
}
[data-pc-preset=preset-8] .btn-link-primary .material-icons-two-tone {
  background-color: #00a854;
}
[data-pc-preset=preset-8] .btn-link-primary:hover {
  background: #e6f6ee;
  color: #00a854;
  border-color: #e6f6ee;
}
[data-pc-preset=preset-8] .btn-link-primary.focus, [data-pc-preset=preset-8] .btn-link-primary:focus {
  background: #e6f6ee;
  color: #00a854;
  border-color: #e6f6ee;
}
[data-pc-preset=preset-8] .btn-link-primary:not(:disabled):not(.disabled).active, [data-pc-preset=preset-8] .btn-link-primary:not(:disabled):not(.disabled):active, .show > [data-pc-preset=preset-8] .btn-link-primary.dropdown-toggle {
  background: #e6f6ee;
  color: #00a854;
  border-color: #e6f6ee;
}
[data-pc-preset=preset-8] .btn-check:active + .btn-link-primary,
[data-pc-preset=preset-8] .btn-check:checked + .btn-link-primary {
  background: #e6f6ee;
  color: #00a854;
  border-color: #e6f6ee;
}
[data-pc-preset=preset-8] .slider-selection {
  background-image: linear-gradient(to bottom, #29ff94 0, #29ff94 100%);
}
[data-pc-preset=preset-8] .slider-selection.tick-slider-selection {
  background-image: linear-gradient(to bottom, #0fff87 0, #0fff87 100%);
}
[data-pc-preset=preset-8] .swal-button:not([disabled]):hover {
  background-color: #008f47;
}
[data-pc-preset=preset-8] .swal-button:active {
  background-color: #008f47;
}
[data-pc-preset=preset-8] .swal-button:focus {
  box-shadow: 0 0 0 1px #fff, 0 0 0 3px rgba(0, 168, 84, 0.29);
}
[data-pc-preset=preset-8] .swal-content__input:focus {
  border-color: rgba(0, 168, 84, 0.29);
}
[data-pc-preset=preset-8] .swal-content__textarea:focus {
  border-color: rgba(0, 168, 84, 0.29);
}
[data-pc-preset=preset-8] .swal2-styled:focus {
  box-shadow: 0 0 0 1px #fff, 0 0 0 3px rgba(0, 168, 84, 0.4) !important;
}
[data-pc-preset=preset-8] .slider-tick.in-selection {
  background-image: linear-gradient(to bottom, #0fff87 0, #0fff87 100%);
}
[data-pc-preset=preset-8] .table-primary {
  --bs-table-color: #ffffff;
  --bs-table-bg: #00a854;
  --bs-table-border-color: #1ab165;
  --bs-table-striped-bg: #0dac5d;
  --bs-table-striped-color: #ffffff;
  --bs-table-active-bg: #1ab165;
  --bs-table-active-color: #ffffff;
  --bs-table-hover-bg: #05aa57;
  --bs-table-hover-color: #ffffff;
  color: var(--bs-table-color);
  border-color: var(--bs-table-border-color);
}

[data-pc-preset=preset-9] {
  --pc-sidebar-active-color: #009688;
  --bs-blue: #009688;
  --bs-primary: #009688;
  --bs-primary-rgb: 0, 150, 136;
  --bs-primary-light: #e6f5f3;
  --bs-link-color: #009688;
  --bs-link-color-rgb: 0, 150, 136;
  --bs-link-hover-color: #00786d;
  --bs-link-hover-color-rgb: to-rgb(shift-color($pc-primary, $link-shade-percentage));
  --dt-row-selected: 0, 150, 136;
}
[data-pc-preset=preset-9] .bg-light-primary {
  background: #e6f5f3;
  color: #009688;
}
[data-pc-preset=preset-9] .link-primary {
  color: #009688 !important;
}
[data-pc-preset=preset-9] .link-primary:hover, [data-pc-preset=preset-9] .link-primary:focus {
  color: #00786d !important;
}
[data-pc-preset=preset-9] .btn-primary {
  --bs-btn-color: #ffffff;
  --bs-btn-bg: #009688;
  --bs-btn-border-color: #009688;
  --bs-btn-hover-color: #ffffff;
  --bs-btn-hover-bg: #008074;
  --bs-btn-hover-border-color: #00786d;
  --bs-btn-focus-shadow-rgb: 38, 166, 154;
  --bs-btn-active-color: #ffffff;
  --bs-btn-active-bg: #00786d;
  --bs-btn-active-border-color: #007166;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  --bs-btn-disabled-color: #ffffff;
  --bs-btn-disabled-bg: #009688;
  --bs-btn-disabled-border-color: #009688;
}
[data-pc-preset=preset-9] .btn-link {
  --bs-btn-color: #009688;
  --bs-btn-hover-color: #00786d;
  --bs-btn-active-color: #00786d;
}
[data-pc-preset=preset-9] .text-bg-primary {
  color: #ffffff !important;
  background-color: RGBA(0, 150, 136, var(--bs-bg-opacity, 1)) !important;
}
[data-pc-preset=preset-9] .accordion {
  --bs-accordion-btn-focus-border-color: #009688;
  --bs-accordion-btn-focus-box-shadow: 0 0 0 0.2rem rgba(0, 150, 136, 0.25);
  --bs-accordion-active-color: #009688;
  --bs-accordion-active-bg: #e6f5f3;
  --bs-accordion-btn-active-icon: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23009688'%3e%3cpath fill-rule=' evenodd' d=' M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/%3e%3c/svg%3e");
}
[data-pc-preset=preset-9] .alert-primary {
  --bs-alert-color: #005a52;
  --bs-alert-bg: #cceae7;
  --bs-alert-border-color: #b3e0db;
  --bs-alert-link-color: #004842;
}
[data-pc-preset=preset-9] .list-group {
  --bs-list-group-active-bg: #009688;
  --bs-list-group-active-border-color: #009688;
}
[data-pc-preset=preset-9] .list-group-item-primary {
  color: #005a52;
  background-color: #cceae7;
}
[data-pc-preset=preset-9] .nav {
  --bs-nav-link-hover-color: #00786d;
}
[data-pc-preset=preset-9] .nav-pills {
  --bs-nav-pills-link-active-bg: #009688;
}
[data-pc-preset=preset-9] .pagination {
  --bs-pagination-hover-color: #00786d;
  --bs-pagination-focus-color: #00786d;
  --bs-pagination-focus-box-shadow: 0 0 0 0.2rem rgba(0, 150, 136, 0.25);
  --bs-pagination-active-bg: #009688;
  --bs-pagination-active-border-color: #009688;
}
[data-pc-preset=preset-9] .progress {
  --bs-progress-bar-bg: #009688;
}
[data-pc-preset=preset-9] .form-check .form-check-input.input-primary:checked {
  border-color: #009688;
  background-color: #009688;
}
[data-pc-preset=preset-9] .form-check .form-check-input.input-light-primary:checked {
  border-color: #e6f5f3;
  background-color: #e6f5f3;
}
[data-pc-preset=preset-9] .form-check .form-check-input.input-light-primary:checked[type=checkbox] {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'%3e%3cpath fill='none' stroke='%23009688' stroke-linecap='round' stroke-linejoin='round' stroke-width='3' d='M6 10l3 3l6-6'/%3e%3c/svg%3e");
}
[data-pc-preset=preset-9] .form-check .form-check-input.input-light-primary:checked[type=radio] {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='2' fill='%23009688'/%3e%3c/svg%3e");
}
[data-pc-preset=preset-9] .form-check .form-check-input.input-primary:focus[type=checkbox], [data-pc-preset=preset-9] .form-check .form-check-input.input-primary:focus[type=radio], [data-pc-preset=preset-9] .form-check .form-check-input.input-light-primary:focus[type=checkbox], [data-pc-preset=preset-9] .form-check .form-check-input.input-light-primary:focus[type=radio] {
  box-shadow: 0 0 0 0.2rem rgba(0, 150, 136, 0.25);
  border-color: #009688;
}
[data-pc-preset=preset-9] .form-check.form-switch .form-check-input.input-light-primary:checked {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='%23009688'/%3e%3c/svg%3e");
}
[data-pc-preset=preset-9] .btn-light-primary {
  background: #e6f5f3;
  color: #009688;
  border-color: #e6f5f3;
}
[data-pc-preset=preset-9] .btn-light-primary .material-icons-two-tone {
  background-color: #009688;
}
[data-pc-preset=preset-9] .btn-light-primary:hover {
  background: #009688;
  color: #fff;
  border-color: #009688;
}
[data-pc-preset=preset-9] .btn-light-primary.focus, [data-pc-preset=preset-9] .btn-light-primary:focus {
  background: #009688;
  color: #fff;
  border-color: #009688;
}
[data-pc-preset=preset-9] .btn-light-primary:not(:disabled):not(.disabled).active, [data-pc-preset=preset-9] .btn-light-primary:not(:disabled):not(.disabled):active, .show > [data-pc-preset=preset-9] .btn-light-primary.dropdown-toggle {
  background: #009688;
  color: #fff;
  border-color: #009688;
}
[data-pc-preset=preset-9] .btn-check:active + .btn-light-primary,
[data-pc-preset=preset-9] .btn-check:checked + .btn-light-primary {
  background: #009688;
  color: #fff;
  border-color: #009688;
}
[data-pc-preset=preset-9] .btn-link-primary {
  background: transparent;
  color: #009688;
  border-color: transparent;
}
[data-pc-preset=preset-9] .btn-link-primary .material-icons-two-tone {
  background-color: #009688;
}
[data-pc-preset=preset-9] .btn-link-primary:hover {
  background: #e6f5f3;
  color: #009688;
  border-color: #e6f5f3;
}
[data-pc-preset=preset-9] .btn-link-primary.focus, [data-pc-preset=preset-9] .btn-link-primary:focus {
  background: #e6f5f3;
  color: #009688;
  border-color: #e6f5f3;
}
[data-pc-preset=preset-9] .btn-link-primary:not(:disabled):not(.disabled).active, [data-pc-preset=preset-9] .btn-link-primary:not(:disabled):not(.disabled):active, .show > [data-pc-preset=preset-9] .btn-link-primary.dropdown-toggle {
  background: #e6f5f3;
  color: #009688;
  border-color: #e6f5f3;
}
[data-pc-preset=preset-9] .btn-check:active + .btn-link-primary,
[data-pc-preset=preset-9] .btn-check:checked + .btn-link-primary {
  background: #e6f5f3;
  color: #009688;
  border-color: #e6f5f3;
}
[data-pc-preset=preset-9] .slider-selection {
  background-image: linear-gradient(to bottom, #17ffe9 0, #17ffe9 100%);
}
[data-pc-preset=preset-9] .slider-selection.tick-slider-selection {
  background-image: linear-gradient(to bottom, #00fce4 0, #00fce4 100%);
}
[data-pc-preset=preset-9] .swal-button:not([disabled]):hover {
  background-color: #007d71;
}
[data-pc-preset=preset-9] .swal-button:active {
  background-color: #007d71;
}
[data-pc-preset=preset-9] .swal-button:focus {
  box-shadow: 0 0 0 1px #fff, 0 0 0 3px rgba(0, 150, 136, 0.29);
}
[data-pc-preset=preset-9] .swal-content__input:focus {
  border-color: rgba(0, 150, 136, 0.29);
}
[data-pc-preset=preset-9] .swal-content__textarea:focus {
  border-color: rgba(0, 150, 136, 0.29);
}
[data-pc-preset=preset-9] .swal2-styled:focus {
  box-shadow: 0 0 0 1px #fff, 0 0 0 3px rgba(0, 150, 136, 0.4) !important;
}
[data-pc-preset=preset-9] .slider-tick.in-selection {
  background-image: linear-gradient(to bottom, #00fce4 0, #00fce4 100%);
}
[data-pc-preset=preset-9] .table-primary {
  --bs-table-color: #ffffff;
  --bs-table-bg: #009688;
  --bs-table-border-color: #1aa194;
  --bs-table-striped-bg: #0d9b8e;
  --bs-table-striped-color: #ffffff;
  --bs-table-active-bg: #1aa194;
  --bs-table-active-color: #ffffff;
  --bs-table-hover-bg: #05988a;
  --bs-table-hover-color: #ffffff;
  color: var(--bs-table-color);
  border-color: var(--bs-table-border-color);
}
/*# sourceMappingURL=data:application/json;charset=utf8;base64,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 */
