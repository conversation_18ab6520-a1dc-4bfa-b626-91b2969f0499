/**======================================================================
=========================================================================
Template Name: Able Pro - Bootstrap Admin Template
Author: codedthemes
Support: https://codedthemes.authordesk.app
File: style.css
=========================================================================
=================================================================================== */
h1,
h2 {
  font-weight: 700;
}

:root {
  --bs-body-bg: #fafafb;
  --bs-body-bg-rgb: 250, 250, 251;
  --pc-heading-color: #343a40;
  --pc-active-background: #e9ecef;
  --pc-sidebar-background: #fff;
  --pc-sidebar-color: #141414;
  --pc-sidebar-color-rgb: 20, 20, 20;
  --pc-sidebar-active-color: var(--bs-primary);
  --pc-sidebar-shadow: 1px 0 0 0px rgb(240 240 240);
  --pc-sidebar-caption-color: #495057;
  --pc-header-background: #fff;
  --pc-header-color: #141414;
  --pc-header-shadow: 0 1px 0 0px rgb(240 240 240);
  --pc-card-box-shadow: none;
  --pc-header-submenu-background: #ffffff;
  --pc-header-submenu-color: #6c757d;
}

[data-pc-theme_contrast=true] {
  --bs-body-bg: #ffffff;
  --pc-sidebar-background: transparent;
  --pc-sidebar-active-color: #1890ff;
  --pc-sidebar-shadow: 1px 0 3px 0px #dee2e6;
  --pc-sidebar-border: none;
  --pc-card-box-shadow: 0px 8px 24px rgba(27, 46, 94, 0.08);
}

section {
  padding: 100px 0;
}

.landing-page {
  overflow-x: hidden;
  background: #fafafb;
}
@media (min-width: 1600px) {
  .landing-page .container {
    max-width: 1200px;
  }
}

.navbar {
  position: fixed;
  padding: 16px 0;
  width: 100%;
  background: transparent;
  z-index: 1099;
  top: 0;
}
.navbar.top-nav-collapse.default {
  background: transparent;
  box-shadow: none;
}
.navbar.default, .navbar.top-nav-collapse {
  background: #141414;
  box-shadow: 0 8px 6px -10px rgba(0, 0, 0, 0.5);
}

header {
  overflow: hidden;
  position: relative;
  padding: 100px 0;
  display: flex;
  align-items: center;
  min-height: 100vh;
  background: #141414;
}
header::after {
  content: "";
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0px;
  left: 0px;
  z-index: 2;
  background: linear-gradient(329.36deg, rgb(0, 0, 0) 14.79%, rgba(67, 67, 67, 0.28) 64.86%);
}
header .container {
  position: relative;
  z-index: 5;
}

@media (max-width: 991.98px) {
  section {
    padding: 40px 0;
  }
}
@media (max-width: 767.98px) {
  header {
    text-align: center;
    padding: 100px 0 50px;
  }
}
/*# sourceMappingURL=data:application/json;charset=utf8;base64,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 */
