function formatVND(value) {
  return parseInt(value).toLocaleString('vi-VN');
}

function updateSliderUI() {
  const $min = $('.range-min');
  const $max = $('.range-max');
  const minVal = parseInt($min.val());
  const maxVal = parseInt($max.val());
  const rangeMin = parseInt($min.attr('min'));
  const rangeMax = parseInt($min.attr('max'));

  const percentMin = ((minVal - rangeMin) / (rangeMax - rangeMin)) * 100;
  const percentMax = ((maxVal - rangeMin) / (rangeMax - rangeMin)) * 100;

  $('.thumb-min').css('left', percentMin + '%');
  $('.thumb-max').css('left', percentMax + '%');

  $('.thumb-min .thumb-value').text(formatVND(minVal));
  $('.thumb-max .thumb-value').text(formatVND(maxVal));

  $('.slider-track::before').css('left', percentMin + '%');
  $('.slider-track::before').css('width', (percentMax - percentMin) + '%');

  $('.input-min').val(minVal);
  $('.input-max').val(maxVal);
}

$(document).ready(function () {
  $('.range-min, .range-max').on('input', updateSliderUI);
  $('.input-min').on('change', function () {
    $('.range-min').val($(this).val());
    updateSliderUI();
  });
  $('.input-max').on('change', function () {
    $('.range-max').val($(this).val());
    updateSliderUI();
  });

  updateSliderUI();
});
