 function updateNavMenu() {
    const maxVisible = window.innerWidth >= 1900 ? 4
                      : window.innerWidth < 1024      ? 2
                      : 3;
    const $allItems   = $('.menu-item.has-children');
    const $seeMoreLi  = $('.see-more').hide();
    const $seeMoreUl  = $('#seeMoreMenu').empty();

    $allItems.show();
    if ($allItems.length > maxVisible) {
      $allItems.each(function(i) {
        if (i >= maxVisible) {
          const $clone = $(this).clone(true).removeAttr('data-index');
          $seeMoreUl.append($clone);
          $(this).hide();
        }
      });
      $seeMoreLi.removeClass('d-none').show();
    }
  }

  $(document).on('click', '.menu-item.has-children > .menu-link, .see-more > .menu-link', function(e) {
    e.preventDefault();
    e.stopPropagation();
    
    const $li = $(this).parent();
    $li.toggleClass('open').siblings('.open').removeClass('open');
});

$(document).on('click', '.submenu .has-children > .menu-link', function(e) {
    e.preventDefault();
    e.stopPropagation();
    const $li = $(this).parent();
    console.log($li);
    
    $li.toggleClass('open').siblings('.open').removeClass('open');
  });

$(document).on('click', '#seeMoreMenu .has-children > .menu-link', function(e) {
    e.preventDefault();
    e.stopPropagation();
    const $li = $(this).parent();
    console.log($li);
    
    $li.toggleClass('open').siblings('.open').removeClass('open');
  });



  $(document).on('click', function(e) {
    if (!$(e.target).closest('.l-header__nav').length) {
      $('.menu-item.open').removeClass('open');
    }
  });

  updateNavMenu();
  $(window).on('resize', updateNavMenu);

  $('.js-nav-hide').removeClass('js-nav-hide');