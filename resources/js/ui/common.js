// resources/js/utils.js

import Swal from "sweetalert2";

export function confirmAction({
    title = "Xác nhận",
    text = "Bạn có chắc muốn thực hiện hành động này?",
    confirmButtonText = "Đồng ý",
    cancelButtonText = "Hủy",
    onConfirm = () => {},
}) {
    Swal.fire({
        title,
        text,
        icon: "warning",
        showCancelButton: true,
        confirmButtonColor: "#3085d6",
        cancelButtonColor: "#d33",
        confirmButtonText,
        cancelButtonText,
    }).then((result) => {
        if (result.isConfirmed) {
            onConfirm();
        }
    });
}

export function showSuccessToast(message = "Thành công!") {
    Swal.fire({
        toast: true,
        position: "top-end",
        icon: "success",
        title: message,
        showConfirmButton: false,
        timer: 4000,
        showCloseButton: true,
        timerProgressBar: true,
    });
}

export function showErrorToast(message = "Đ<PERSON> xảy ra lỗi!") {
    Swal.fire({
        toast: true,
        position: "top-end",
        icon: "error",
        title: message,
        showConfirmButton: false,
        timer: 4000,
        showCloseButton: true,
        timerProgressBar: true,
    });
}

export function showLoading(message = "Đang xử lý...") {
    Swal.fire({
        title: message,
        allowOutsideClick: false,
        didOpen: () => {
            Swal.showLoading();
        },
    });
}

export function hideLoading() {
    Swal.close();
}

export function copyToClipboard(text) {
    navigator.clipboard
        .writeText(text)
        .then(() => {
            showSuccessToast("Đã sao chép!");
        })
        .catch(() => {
            showErrorToast("Không thể sao chép!");
        });
}

export function showAnimationLoading() {
    $("#global-loading").fadeIn(150);
}

export function hideAnimationLoading() {
    $("#global-loading").fadeOut(150);
}
