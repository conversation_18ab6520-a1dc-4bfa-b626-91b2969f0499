import Choices from "choices.js";

export function initChoices() {
    if (window.multiSelectChoices) {
        window.multiSelectChoices.forEach(function (cfg) {
            const element = document.getElementById(cfg.id);
            if (element) {
                new Choices(element, {
                    removeItemButton: true,
                    placeholderValue: cfg.placeholder,
                    searchEnabled: true,
                    renderSelectedChoices: 'always',
                    shouldSort: false   
                });
            }
        });
    }
}
