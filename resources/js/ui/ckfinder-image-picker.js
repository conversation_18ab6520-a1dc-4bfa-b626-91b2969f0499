
window.openCKFinder = function (inputId, previewId) {
    CKFinder.popup({
        chooseFiles: true,
        width: 800,
        height: 600,
        onInit: function (finder) {
            finder.on('files:choose', function (evt) {
                const file = evt.data.files.first();
                const url = file.getUrl();

                $('#' + inputId).val(url);
                $('#' + previewId).attr('src', url);
                $('#' + inputId + '-remove').removeClass('d-none');
            });

            finder.on('file:choose:resizedImage', function (evt) {
                const url = evt.data.resizedUrl;

                $('#' + inputId).val(url);
                $('#' + previewId).attr('src', url);
                $('#' + inputId + '-remove').removeClass('d-none');
            });
        }
    });
};

window.setupCKFinderMultiImage = function(buttonSelector) {
    $(document).on('click', buttonSelector, function () {
        const inputId = $(this).data('input-id');
        const name = $(this).data('name');
        const previewContainer = $('#' + inputId + '-preview');

        previewContainer.find('.default-image-wrapper').remove();

        CKFinder.modal({
            chooseFiles: true,
            width: 800,
            height: 600,
            onInit: function (finder) {
                finder.on('files:choose', function (evt) {
                    const files = evt.data.files.models;

                    files.forEach(file => {
                        const url = file.getUrl();
                        const imageHtml = `
                            <div class="image-wrapper position-relative">
                                <img src="${url}" style="width: 250px; height: 200px; object-fit: cover;" class="img-thumbnail">
                                <input type="hidden" name="${name}[]" value="${url}">
                                <button type="button" class="btn btn-sm btn-danger position-absolute top-0 end-0 remove-image">&times;</button>
                            </div>
                        `;
                        previewContainer.append(imageHtml);
                    });
                });

                finder.on('file:choose:resizedImage', function (evt) {
                    const url = evt.data.resizedUrl;
                    const imageHtml = `
                        <div class="image-wrapper position-relative">
                            <img src="${url}" style="width: 250px; height: 200px; object-fit: cover;" class="img-thumbnail">
                            <input type="hidden" name="${name}[]" value="${url}">
                            <button type="button" class="btn btn-sm btn-danger position-absolute top-0 end-0 remove-image">&times;</button>
                        </div>
                    `;
                    previewContainer.append(imageHtml);
                });
            }
        });
    });

    $(document).on('click', '.remove-image', function () {
        const wrapper = $(this).closest('.image-wrapper');
        const previewContainer = wrapper.parent();

        wrapper.remove();

        if (previewContainer.find('.image-wrapper').length === 0) {
            const defaultImage = previewContainer.data('default-image') || '';
            const defaultHtml = `
                <div class="image-wrapper position-relative default-image-wrapper">
                    <img src="${defaultImage}" style="width: 250px; height: 200px; object-fit: cover;" class="img-thumbnail">
                </div>
            `;
            previewContainer.append(defaultHtml);
        }
    });
};



window.removePickedImage = function (inputId, previewId, defaultImage) {
    $('#' + inputId).val('');
    $('#' + previewId).attr('src', defaultImage);
    $('#' + inputId + '-remove').addClass('d-none');
};