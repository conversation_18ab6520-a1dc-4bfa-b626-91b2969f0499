import select2 from "select2"
select2(); 

export function initSelect2() {
    if (typeof $.fn.select2 === 'undefined') {
        console.warn('Select2 is not loaded. Skipping initSelect2.');
        return;
    }

    $('.select2-initialized').each(function() {
        const $element = $(this);
        const options = {
            closeOnSelect: true,
            placeholder: $element.data('placeholder') || '-- Chọn --', 
            allowClear: $element.data('allow-clear') || false,
        };

        if ($element.data('ajax-url')) {
            options.ajax = {
                url: $element.data('ajax-url'),
                dataType: 'json',
                delay: 250, 
                data: function (params) {
                    return {
                        search: params.term, 
                        page: params.page
                    };
                },
                processResults: function (data, params) {
                    params.page = params.page || 1;
                    return {
                        results: data.items, 
                        pagination: {
                            more: (params.page * data.page_size) < data.total_count 
                        }
                    };
                },
                cache: true
            };
            options.minimumInputLength = $element.data('min-length') || 1;
        }

        $element.select2(options);
    });
}