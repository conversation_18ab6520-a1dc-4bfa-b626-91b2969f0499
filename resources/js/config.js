import tinymce from "tinymce/tinymce";
import "tinymce/icons/default";
import "tinymce/themes/silver/theme";
import "tinymce/models/dom";

import "tinymce/plugins/link";
import "tinymce/plugins/lists";
import "tinymce/plugins/code";
import "tinymce/plugins/table";
import "tinymce/plugins/image";
import "tinymce/plugins/preview";
import "tinymce/plugins/fullscreen";

import "tinymce/plugins/media";
import "tinymce/plugins/anchor";

import Swiper from "swiper/bundle";

export function initTinyEditor(selector = ".tinymce-editor") {
    tinymce.init({
        selector: selector,
        height: 500,
        menubar: false,
        base_url: "/tinymce",
        suffix: ".min",
        skin: "oxide",
        content_css: "/tinymce/skins/ui/oxide/content.min.css",
        plugins: [
            "advlist",
            "autolink",
            "lists",
            "link",
            "image",
            "charmap",
            "preview",
            "anchor",
            "searchreplace",
            "visualblocks",
            "code",
            "fullscreen",
            "insertdatetime",
            "media",
            "table",
            "emoticons",
            "wordcount",
            "autosave",
        ],
        toolbar:
            "undo redo | styles | formatselect | bold italic underline | alignleft aligncenter alignright alignjustify | " +
            "bullist numlist outdent indent | link image media table | charmap emoticons hr | searchreplace | preview fullscreen | code",
        images_upload_handler: (blobInfo, progress) =>
            new Promise((resolve, reject) => {
                const xhr = new XMLHttpRequest();

                xhr.withCredentials = false;

                xhr.open("POST", "/images/upload");

                xhr.upload.onprogress = (e) => {
                    progress((e.loaded / e.total) * 100);
                };

                xhr.setRequestHeader(
                    "X-CSRF-TOKEN",
                    document
                        .querySelector('meta[name="csrf-token"]')
                        .getAttribute("content")
                );

                xhr.onload = () => {
                    if (xhr.status === 403) {
                        reject({
                            message: "HTTP Error: " + xhr.status,
                            remove: true,
                        });
                        return;
                    }

                    if (xhr.status < 200 || xhr.status >= 300) {
                        reject("HTTP Error: " + xhr.status);
                        return;
                    }

                    const json = JSON.parse(xhr.responseText);

                    if (!json || typeof json.location != "string") {
                        reject("Invalid JSON: " + xhr.responseText);
                        return;
                    }

                    resolve(json.location);
                };

                xhr.onerror = () => {
                    reject(
                        "Image upload failed due to a XHR Transport error. Code: " +
                            xhr.status
                    );
                };

                const formData = new FormData();

                formData.append("file", blobInfo.blob(), blobInfo.filename());

                xhr.send(formData);
            }),

        setup(editor) {
            editor.on("init", () => {
                console.log(`TinyMCE Editor initialized on ${selector}`);
            });
        },
        branding: false,
    });
}

export function showToastNotifications() {
    const $body = $("body");
    const successMessage = $body.data("toast-success");
    const errorMessage = $body.data("toast-error");

    if (successMessage) {
        showSuccessToast(successMessage);
        $body.removeData("toast-success");
    }

    if (errorMessage) {
        showErrorToast(errorMessage);
        $body.removeData("toast-error");
    }
}

export function initSlider(className, options = {}) {
    const defaultOptions = {
        slidesPerView: 3,
        spaceBetween: 10,
        autoplay: true,
        loop: true,
        navigation: true,
        pagination: true,
        autoplay: {
            delay: 3000,
        },
        breakpoints: {
            0: {
                slidesPerView: 1,
            },
            768: {
                slidesPerView: 3,
            },
            1024: {
                slidesPerView: 5,
            },
        },
    };

    const finalOptions = {
        ...defaultOptions,
        ...options,
        autoplay: options.autoplay
            ? { delay: options.autoplaySpeed || 3000 }
            : false,
        navigation: options.arrows
            ? {
                  nextEl: `.${className} .arrow-right`,
                  prevEl: `.${className} .arrow-left`,
              }
            : false,
        pagination: options.dots
            ? {
                  el: `.${className} .swiper-pagination`,
                  clickable: true,
              }
            : false,
    };

    new Swiper(`.${className}`, finalOptions);
}       



