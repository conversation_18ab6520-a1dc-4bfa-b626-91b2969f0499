import "@js/shared/bootstrap";
import axios from "axios";
window.axios = axios;
import { initToasts } from "@js/ui/toast";
import "@js/ui/updateNavMenu";
import { showToastNotifications, initSlider } from "@js/config";
import "./auth";
import "@js/ui/swiper";
import "@js/components/priceRangeSlider";

$(function () {
    initToasts();
    showToastNotifications();

    var $header = $(".l-header");
    var headerOffset = $header.offset().top;

    $(window).on("scroll", function () {
        if ($(this).scrollTop() > headerOffset) {
            if (!$header.hasClass("sticky")) {
                $header.addClass("sticky");
            }
        } else {
            $header.removeClass("sticky");
        }
    });

    var $btn = $("#scrollTopBtn");

    $(window).on("scroll", function () {
        if ($(this).scrollTop() > 200) {
            $btn.fadeIn();
        } else {
            $btn.fadeOut();
        }
    });

    $btn.on("click", function () {
        $("html, body").animate({ scrollTop: 0 }, 100);
    });
});
