export default function initLocationSelect() {
    const $provinceSelect = $('[name="province_code"]');
    const $districtSelect = $('[name="district_code"]');
    const $wardSelect = $('[name="ward_code"]');
    const $streetSelect = $('[name="street"]');
    const $fullAddressInput = $('[name="full_address"]');

    function populateSelect($select, data, selectedValue = "") {
        $select.html('<option value="">-- Chọn --</option>');
        $.each(data, function (index, item) {
            $select.append(
                $("<option>", {
                    value: item.code,
                    text: item.name,
                    selected: item.code == selectedValue,
                })
            );
        });
    }

    function clearSelects($selects) {
        $selects.each(function () {
            $(this).html('<option value="">-- Chọn --</option>');
        });
    }

    function updateFullAddress() {
        const getSelectedText = ($select) => {
            const value = $select.val();
            return value ? $select.find("option:selected").text().trim() : null;
        };

        const province = getSelectedText($provinceSelect);
        const district = getSelectedText($districtSelect);
        const ward = getSelectedText($wardSelect);
        const street = getSelectedText($streetSelect);

        const addressParts = [street, ward, district, province].filter(Boolean);

        $fullAddressInput.val(addressParts.join(", "));
    }

    function initDataFromSelected() {
        const selectedProvince = $provinceSelect.val();
        const selectedDistrict = $districtSelect.val();
        const selectedWard = $wardSelect.val();
        const selectedStreet = $streetSelect.val();

        if (selectedProvince) {
            $.getJSON(`/location-full/${selectedProvince}`).done(
                (data) => {
                    populateSelect(
                        $districtSelect,
                        data.districts,
                        selectedDistrict
                    );
                    populateSelect($wardSelect, data.wards, selectedWard);
                    populateSelect($streetSelect, data.streets, selectedStreet);

                    if (selectedDistrict) {
                        $.getJSON(
                            `/location-full/${selectedProvince}/${selectedDistrict}`
                        ).done((data2) => {
                            populateSelect(
                                $wardSelect,
                                data2.wards,
                                selectedWard
                            );
                            populateSelect(
                                $streetSelect,
                                data2.streets,
                                selectedStreet
                            );
                        });
                    }
                }
            );
        }
    }

    $provinceSelect.on("change", function () {
        const provinceCode = $(this).val();
        clearSelects($districtSelect.add($wardSelect).add($streetSelect));
        if (provinceCode) {
            $.getJSON(`/location-full/${provinceCode}`).done((data) => {
                populateSelect($districtSelect, data.districts);
                populateSelect($wardSelect, data.wards);
                populateSelect($streetSelect, data.streets);
                updateFullAddress();
            });
        } else {
            updateFullAddress();
        }
    });

    $districtSelect.on("change", function () {
        const districtCode = $(this).val();
        const provinceCode = $provinceSelect.val();

        clearSelects($wardSelect.add($streetSelect));

        if (provinceCode && districtCode) {
            $.getJSON(
                `/location-full/${provinceCode}/${districtCode}`
            ).done((data) => {
                populateSelect($wardSelect, data.wards);
                populateSelect($streetSelect, data.streets);
                updateFullAddress();
            });
        } else {
            updateFullAddress();
        }
    });

    $wardSelect.on("change", updateFullAddress);
    $streetSelect.on("change", updateFullAddress);

    initDataFromSelected();
}
