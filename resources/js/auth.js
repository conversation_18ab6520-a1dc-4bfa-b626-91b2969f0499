(function ($) {
    "use strict";

    const getFormPayload = ($form) =>
        Object.fromEntries(new FormData($form[0]).entries());

    const clearErrors = (prefix, fields) => {
        fields.forEach((field) => {
            const $el = $(`#${prefix}${field}`);
            $el.removeClass("is-invalid");
            $(`#error_${prefix}${field}`).text("");
        });
    };

    const handleError = (error, prefix) => {
        const resp = error.response;
        if (resp?.status === 422 && resp.data.errors) {
            Object.entries(resp.data.errors).forEach(([field, msgs]) => {
                console.log(`${prefix}${field}`);

                const $el = $(`#${prefix}${field}`);
                $el.addClass("is-invalid");
                $(`#error_${prefix}${field}`).text(msgs[0]);
            });
        } else {
            showErrorToast(resp?.data?.message || "<PERSON><PERSON> x<PERSON>y ra lỗi");
        }
    };

    const setLoading = ($btn, isLoading = true) => {
        if (isLoading) {
            $btn.data("text", $btn.html())
                .prop("disabled", true)
                .html(
                    '<span class="spinner-border spinner-border-sm me-1"></span>Đang xử lý...'
                );
        } else {
            $btn.prop("disabled", false).html($btn.data("text"));
        }
    };

    $(document)
        .on("input", ".otp-input", function () {
            const $inputs = $(".otp-input");
            const otp = $inputs
                .toArray()
                .map((i) => i.value)
                .join("");
            $("#phoneVerificationOtp").val(otp);
            $(this).next(".otp-input").focus();
        })
        .on("keydown", ".otp-input", function (e) {
            if (e.key === "Backspace" && !this.value) {
                $(this).prev(".otp-input").focus();
            }
        });

    const modalsConfig = [
        {
            id: "#phoneVerificationModal",
            form: "#phoneVerificationForm",
            prefix: "phoneVerification_",
            fields: ["otp"],
        },
        {
            id: "#registerModal",
            form: "#registerForm",
            prefix: "reg_",
            fields: [
                "fullname",
                "email_or_phone",
                "email",
                "phone",
                "password",
                "password_confirmation",
            ],
        },
        {
            id: "#loginModal",
            form: "#loginForm",
            prefix: "login_",
            fields: ["identifier", "password"],
        },
        {
            id: "#verificationModal",
            form: null,
            prefix: "resend_",
            fields: ["identifier"],
        },
    ];

    modalsConfig.forEach(({ id, form, prefix, fields }) => {
        $(id).on("hidden.bs.modal", () => {
            if (form) {
                const $f = $(form)[0];
                $f.reset();
            }
            clearErrors(prefix, fields);
            if (id === "#phoneVerificationModal") {
                $(".otp-input").val("");
            }
        });
    });

    // Form Submissions
    const formsConfig = [
        {
            selector: "#registerForm",
            prefix: "reg_",
            onSuccess: (data, $form) => {
                bootstrap.Modal.getInstance($("#registerModal")[0])?.hide();
                $form[0].reset();
                if (data.data.phone) {
                    const modal = new bootstrap.Modal(
                        $("#phoneVerificationModal")[0]
                    );
                    $("#phoneVerificationIdentifier").val(data.data.phone);
                    modal.show();
                }
                showSuccessToast(data.message, "success");
            },
        },
        {
            selector: "#loginForm",
            prefix: "login_",
            onSuccess: (data, $form) => {
                if (data.status === "pending_verification") {
                    bootstrap.Modal.getInstance($("#loginModal")[0])?.hide();
                    const modal = new bootstrap.Modal(
                        $("#verificationModal")[0]
                    );
                    $("#resend_identifier").val(data.identifier);
                    $("#resendVerificationBtn").data("method", data.method);
                    modal.show();
                } else if (data.status === "success") {
                    bootstrap.Modal.getInstance($("#loginModal")[0])?.hide();
                    $form[0].reset();
                    showSuccessToast(data.message || "Đăng nhập thành công");
                    setTimeout(() => location.reload(), 1200);
                }
            },
        },
    ];

    // toggle pass

    $(".toggle-password").on("click", function () {
        const $input = $(".l_password");
        const $icon = $("#togglePasswordIcon");
        if ($input.attr("type") === "password") {
            $input.attr("type", "text");
            $icon.removeClass("mdi-eye-off").addClass("mdi-eye");
        } else {
            $input.attr("type", "password");
            $icon.removeClass("mdi-eye").addClass("mdi-eye-off");
        }

        const $inputReg = $("#reg_password");
        const $iconReg = $("#toggleRegPasswordIcon");
        if ($inputReg.attr("type") === "password") {
            $inputReg.attr("type", "text");
            $iconReg.removeClass("mdi-eye-off").addClass("mdi-eye");
        } else {
            $inputReg.attr("type", "password");
            $iconReg.removeClass("mdi-eye").addClass("mdi-eye-off");
        }
    });

    $(".toggle-conf-password").on("click", function () {
        const $input = $("#reg_password_confirmation");
        const $icon = $("#toggleLRegConfPasswordIcon");
        if ($input.attr("type") === "password") {
            $input.attr("type", "text");
            $icon.removeClass("mdi-eye-off").addClass("mdi-eye");
        } else {
            $input.attr("type", "password");
            $icon.removeClass("mdi-eye").addClass("mdi-eye-off");
        }
    });

    formsConfig.forEach(({ selector, prefix, onSuccess }) => {
        $(selector).on("submit", function (e) {
            e.preventDefault();
            const $form = $(this);
            const $btn = $form.find('button[type="submit"]');
            clearErrors(prefix, Object.keys(getFormPayload($form)));
            setLoading($btn, true);

            axios
                .post($form.attr("action"), getFormPayload($form))
                .then(({ data }) => onSuccess(data, $form))
                .catch((error) => handleError(error, prefix))
                .finally(() => setLoading($btn, false));
        });
    });

    // Resend Verification
    $("#resendVerificationBtn").on("click", function () {
        const $btn = $(this);
        const identifier = $("#resend_identifier").val();
        const url = $btn.data("url");
        clearErrors("resend_", ["identifier"]);
        setLoading($btn, true);

        axios
            .post(url, { identifier })
            .then(({ data }) => {
                if (data.method === "phone") {
                    bootstrap.Modal.getInstance(
                        $("#verificationModal")[0]
                    )?.hide();
                    const modal = new bootstrap.Modal(
                        $("#phoneVerificationModal")[0]
                    );
                    $("#phoneVerificationIdentifier").val(data.value);
                    modal.show();
                }
                showSuccessToast(data.message);
            })
            .catch((error) => handleError(error, "resend_"))
            .finally(() => setLoading($btn, false));
    });

    // Phone Verification Submit & Resend OTP
    $("#phoneVerificationForm").on("submit", function (e) {
        e.preventDefault();
        const $form = $(this);
        const $btn = $form.find('button[type="submit"]');
        clearErrors("phoneVerification_", ["otp"]);
        setLoading($btn, true);

        axios
            .post($form.attr("action"), {
                phone: $("#phoneVerificationIdentifier").val(),
                otp: $("#phoneVerificationOtp").val(),
            })
            .then(({ data }) => {
                showSuccessToast(data.message || "Xác thực thành công");
                bootstrap.Modal.getInstance(
                    $("#phoneVerificationModal")[0]
                )?.hide();
            })
            .catch((error) => handleError(error, "phoneVerification_"))
            .finally(() => setLoading($btn, false));
    });

    $("#resendPhoneOtpBtn").on("click", function () {
        const $btn = $(this);
        const identifier = $("#phoneVerificationIdentifier").val();
        const url = $btn.data("url");
        clearErrors("phoneVerification_", ["otp"]);
        setLoading($btn, true);

        axios
            .post(url, { identifier })
            .then(({ data }) => {
                showSuccessToast(data.message || "Đã gửi lại mã OTP");
                clearErrors("phoneVerification_", ["otp"]);
                $(".otp-input, #phoneVerificationOtp").val("");
            })
            .catch(() => showErrorToast("Không thể gửi lại mã OTP"))
            .finally(() => setLoading($btn, false));
    });

    $("#logout-link").on("click", function (e) {
        e.preventDefault();
        $("#logout-form").submit();
    });
})(jQuery);
