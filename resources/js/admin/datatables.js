import "datatables.net";
import "datatables.net-bs5";
import "datatables.net-bs5/css/dataTables.bootstrap5.min.css";
import "datatables.net-buttons-bs5/css/buttons.bootstrap5.min.css";
import "datatables.net-responsive-bs5/css/responsive.bootstrap5.min.css";
import "datatables.net-buttons-bs5";
import "datatables.net-buttons/js/buttons.html5.js";
import "datatables.net-responsive-bs5";

import {deleteAction,toggleStatusAction} from "./util";

export function initDataTables() {
    $(".data-table").each(function () {
        const url = $(this).data("url");
        const columns = $(this).data("columns");

        if (!url || !columns) {
            console.warn(
                "Không tìm thấy url hoặc data-columns cho .data-table"
            );
            return;
        }

        const dataTableOptions = {
            processing: true,
            serverSide: true,
            ajax: url,
            columns: columns,
            responsive: true,
            language: {
                url: "/js/datatables/i18n/vi.json",
            },
        };

        const dataTable = $(this).DataTable(dataTableOptions);

        deleteAction(dataTable);
        toggleStatusAction(dataTable);
    });
}

$(function () {
    initDataTables();
});
