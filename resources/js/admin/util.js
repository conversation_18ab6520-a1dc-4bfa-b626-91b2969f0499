import { showAnimationLoading, hideAnimationLoading } from "@js/ui/common";

export function initColorPickerSync(textInputSelector, colorPickerSelector) {
    $(document).ready(function () {
        const $colorTextInput = $(textInputSelector);
        const $colorPickerInput = $(colorPickerSelector);
        if ($colorTextInput.length && $colorPickerInput.length) {
            $colorPickerInput.on("input", function () {
                $colorTextInput.val($(this).val());
            });
            $colorTextInput.on("input", function () {
                const hexRegex = /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/;
                const inputValue = $(this).val();
                if (hexRegex.test(inputValue)) {
                    $colorPickerInput.val(inputValue);
                }
            });
            setTimeout(() => {
                $colorPickerInput.val($colorTextInput.val());
            }, 50);
        }
    });
}

export function previewImage(
    event,
    previewId,
    removeButtonId,
    currentImage,
    defaultImage
) {
    const $input = $(event.target);
    const $image = $("#" + previewId);
    const $removeButton = $("#" + removeButtonId);
    const $hiddenRemovedInput = $(
        '[name="' + $input.attr("name") + '_removed"]'
    );

    if ($input[0].files[0]) {
        const reader = new FileReader();

        reader.onload = function (e) {
            $image.attr("src", e.target.result);
            $removeButton.removeClass("d-none").text("Xóa ảnh mới chọn");
            $hiddenRemovedInput.val("0");
        };

        reader.readAsDataURL($input[0].files[0]);
    } else {
        $image.attr("src", currentImage || defaultImage);
        $removeButton.addClass("d-none");
        $hiddenRemovedInput.val("0");
    }
}

export function removeImage(
    inputId,
    previewId,
    defaultImageSrc,
    removedInputId
) {
    const $fileInput = $("#" + inputId);
    const $imagePreview = $("#" + previewId);
    const $hiddenRemovedInput = $("#" + removedInputId);
    const $removeButton = $("#" + inputId + "-remove-button");

    $fileInput.val(null);
    $imagePreview.attr("src", defaultImageSrc);
    $hiddenRemovedInput.val("1");
    $removeButton.addClass("d-none");
}

export function resetPassword(e) {
    e.preventDefault();
    const $form = $("#reset-password-form");
    confirmAction({
        title: "Đặt lại mật khẩu",
        text: "Bạn có chắc chắn muốn đặt lại mật khẩu?",
        confirmButtonText: "Đặt lại",
        cancelButtonText: "Hủy",
        onConfirm: function () {
            $form.submit();
        },
    });
}

export function deleteAction(dataTableInstance) {
    $("body").on("click", ".delete-btn", function (e) {
        e.preventDefault();
        console.log("Delete button clicked!");
        const idToDelete = $(this).data("id");
        const name = $(this).data("name");
        const deleteUrl = $(this).data("delete-url");
        const token = $('meta[name="csrf-token"]').attr("content");

        if (!idToDelete || !deleteUrl || !token) {
            console.error(
                "Missing data for delete. Check data-id, data-delete-url, or CSRF token."
            );
            showErrorToast("Đã xảy ra lỗi, Thử lại sau.");
            return;
        }
        confirmAction({
            title: "Xác nhận xóa",
            text: `Bạn có chắc muốn xóa "${name}" không? Hành động này không thể hoàn tác.`,
            confirmButtonText: "Xóa",
            cancelButtonText: "Hủy bỏ",
            onConfirm: () => {
                if (typeof showLoading === "function") {
                    showLoading("");
                }
                $.ajax({
                    url: deleteUrl,
                    type: "POST",
                    data: {
                        _token: token,
                        _method: "DELETE",
                    },
                    success: function (response) {
                        if (typeof Swal !== "undefined" && Swal.isVisible()) {
                            hideLoading();
                        }
                        if (response.success) {
                            if (
                                dataTableInstance &&
                                typeof dataTableInstance.ajax.reload ===
                                    "function"
                            ) {
                                dataTableInstance.ajax.reload(null, false);
                            } else {
                                console.warn(
                                    "dataTableInstance is not valid. Cannot reload table."
                                );
                            }
                            showSuccessToast(
                                response.message || "Xóa thành công!"
                            );
                        } else {
                            showErrorToast(
                                response.message ||
                                    "Xóa thất bại: " +
                                        (response.message || "Đã xảy ra lỗi.")
                            );
                        }
                    },
                    error: function (xhr, status, error) {
                        if (typeof Swal !== "undefined" && Swal.isVisible()) {
                            hideLoading();
                        }
                        console.error(
                            "AJAX Delete Error:",
                            error,
                            xhr.responseText
                        );
                        const errorMessage = "Đã xảy ra lỗi khi xóa bản ghi.";
                        showErrorToast(errorMessage);
                    },
                });
            },
        });
    });
}

export function toggleStatusAction(dataTable) {
    $("body").on("click", ".toggle-status-btn", function (e) {
        e.preventDefault();
        const $button = $(this);
        const memberId = $button.data("id");
        const fieldName = $button.data("field");
        const currentStatus = $button.data("status");
        const newStatus = currentStatus === 1 ? 0 : 1;
        const toggleUrl = $button.data("toggle-url");
        const token = $('meta[name="csrf-token"]').attr("content");
        if (!memberId || !fieldName || !toggleUrl || !token) {
            console.error(
                "Missing data for status toggle. Check data-id, data-field, data-toggle-url, or CSRF token."
            );
            return;
        }
        if (typeof showLoading === "function") {
            showLoading("");
        }

        $button.prop("disabled", true).addClass("disabled");
        $.ajax({
            url: toggleUrl,
            type: "POST",
            data: {
                _token: token,
                status: newStatus,
                field: fieldName,
            },
            success: function (response) {
                if (typeof Swal !== "undefined" && Swal.isVisible()) {
                    hideLoading();
                }
                if (response.success) {
                    dataTable.ajax.reload(null, false);
                    if (typeof showSuccessToast === "function") {
                        showSuccessToast(
                            response.message || "Trạng thái đã được cập nhật!"
                        );
                    } else {
                        console.log(
                            response.message || "Trạng thái đã được cập nhật!"
                        );
                    }
                } else {
                    if (typeof showErrorToast === "function") {
                        showErrorToast(
                            response.message || "Cập nhật trạng thái thất bại!"
                        );
                    } else {
                        console.error(
                            response.message || "Cập nhật trạng thái thất bại!"
                        );
                    }
                    $button.prop("disabled", false).removeClass("disabled");
                }
            },
            error: function (xhr, status, error) {
                if (typeof Swal !== "undefined" && Swal.isVisible()) {
                    hideLoading();
                }
                console.error("AJAX Error:", error, xhr.responseText);
                const errorMessage =
                    xhr.responseJSON && xhr.responseJSON.message
                        ? xhr.responseJSON.message
                        : "Đã xảy ra lỗi khi cập nhật trạng thái.";
                if (typeof showErrorToast === "function") {
                    showErrorToast(errorMessage);
                } else {
                    console.error(errorMessage);
                }
                $button.prop("disabled", false).removeClass("disabled");
            },
        });
    });
}

export function initBeautiServices({
    typeSelectId = "type_ids_select",
    servicesContainerId = "services-container",
    addBtnId = "add-service",
    templateId = "service-item-template",
} = {}) {
    const $typeSelect = $("#" + typeSelectId);
    const $servicesContainer = $("#" + servicesContainerId);
    const $addBtn = $("#" + addBtnId);
    const $template = $("#" + templateId);

    if (
        !$template.length ||
        !$typeSelect.length ||
        !$addBtn.length ||
        !$servicesContainer.length
    ) {
        console.warn("initBeautiServices: Missing DOM elements.");
        return;
    }

    const rawTemplate = $template.html()?.trim();
    if (!rawTemplate) {
        console.warn("initBeautiServices: Empty template.");
        return;
    }

    let serviceIndex = $servicesContainer.children(".service-item").length;

    function onTypeChange() {
        const selectedTypes = $typeSelect.val() || [];
        const hasAny = selectedTypes.length > 0;
        $addBtn.prop("disabled", !hasAny);
        if (!hasAny) {
            $servicesContainer.empty();
            serviceIndex = 0;
        }
    }

    async function fetchServices(typeIds) {
        try {
            showAnimationLoading();
            const result = await $.getJSON(
                `/admin/services-by-types?beauti_types=${typeIds}`
            );
            return result;
        } catch (error) {
            throw new Error("Lấy dịch vụ thất bại");
        } finally {
            hideAnimationLoading();
        }
    }

    function makeItemHtml(idx, data = {}) {
        return rawTemplate
            .replace(/__NAME__/g, `services[${idx}]`)
            .replace(/__INDEX__/g, idx)
            .replace(/__PIVOT_ID__/g, data.id || "")
            .replace(/__PRICE__/g, data.price_from || "")
            .replace(/__CHECKED__/g, data.status ? "checked" : "");
    }

    function refreshIndexes() {
        $servicesContainer.children(".service-item").each((i, el) => {
            $(el)
                .find("[name]")
                .each((_, fld) => {
                    const $fld = $(fld);
                    const newName = $fld
                        .attr("name")
                        .replace(/services\[\d+\]/, `services[${i}]`);
                    $fld.attr("name", newName);
                });
        });
        serviceIndex = $servicesContainer.children(".service-item").length;
    }

    $typeSelect.on("change", onTypeChange);
    onTypeChange();

    $addBtn.on("click", async () => {
        const selectedTypes = $typeSelect.val() || [];
        if (!selectedTypes.length) return;

        let services;
        try {
            services = await fetchServices(selectedTypes);
        } catch (err) {
            return typeof showErrorToast === "function"
                ? showErrorToast(err.message)
                : alert(err.message);
        }

        if (!services.length) {
            return typeof showErrorToast === "function"
                ? showErrorToast("Chưa có dịch vụ cho loại hình đã chọn.")
                : alert("Chưa có dịch vụ cho loại hình đã chọn.");
        }

        const idx = serviceIndex;
        const html = makeItemHtml(idx, { status: true });
        const $item = $(html);

        const $sel = $item.find(`select[name="services[${idx}][service_id]"]`);
        let opts = '<option value="">Chọn dịch vụ</option>';
        services.forEach((s) => {
            opts += `<option value="${s.id}">${s.name}</option>`;
        });
        $sel.html(opts);

        if ($.fn.select2) {
            $sel.select2({
                placeholder: "-- Chọn dịch vụ --",
                dropdownAutoWidth: true,
                width: "100%",
            });
        }

        $item.on("click", ".remove-service", () => {
            $item.remove();
            refreshIndexes();
        });

        $servicesContainer.append($item);
        serviceIndex++;
    });

    $servicesContainer.on("click", ".remove-service", function () {
        $(this).closest(".service-item").remove();
        refreshIndexes();
    });
}

export function initBeautiPackageForm() {
    const $businessId = $('[name="business_id"]');
    const oldStoreId = $("#store_id_old").val();
    if (oldStoreId) {
        $("#store_id").val(oldStoreId).trigger("change");
    }

    const oldServiceId = $("#service_id_old").val();
    if (oldServiceId) {
        $("#service_id").val(oldServiceId).trigger("change");
    }

    $businessId.on("change", function () {
        let businessId = $(this).val();
        if (!businessId) return;

        showAnimationLoading();
        $('[name="store_id"]').html("<option>Đang tải...</option>");

        $.getJSON(`/admin/getStores?business_id=${businessId}`)
            .done((data) => {
                let opts = '<option value="">-- Chọn cơ sở --</option>';
                data.forEach(
                    (s) =>
                        (opts += `<option value="${s.id}">${s.name}</option>`)
                );
                $('[name="store_id"]').html(opts);
                $('[name="service_id"]').html(
                    '<option value="">-- Chọn dịch vụ --</option>'
                );
            })
            .always(() => {
                hideAnimationLoading();
            });
    });

    if ($businessId.val()) {
        if (!oldStoreId) {
            $businessId.trigger("change");
        }
    }

    $('[name="store_id"]').on("change", function () {
        let storeId = $(this).val();
        $('[name="service_id"]').html("<option>Đang tải...</option>");
        showAnimationLoading();
        $.getJSON(`/admin/getServices?store_id=${storeId}`)
            .done((data) => {
                let opts = '<option value="">-- Chọn dịch vụ --</option>';
                data.forEach(
                    (s) =>
                        (opts += `<option value="${s.id}">${s.name}</option>`)
                );
                $('[name="service_id"]').html(opts);
                const oldServiceId = $("#select-service").data("old");
                if (oldServiceId) {
                    $('[name="service_id"]').val(oldServiceId);
                }
            })
            .always(() => {
                hideAnimationLoading();
            });
    });
}

export function autoPop() {
    $("#user_id").on("change", function () {
        const selected = $(this).find("option:selected");

        const name = selected.data("name");
        const email = selected.data("email");
        const phone = selected.data("phone");
        const address = selected.data("address");

        $('input[name="name"]').val(name || "");
        $('input[name="email"]').val(email || "");
        $('input[name="phone"]').val(phone || "");
        $('input[name="address"]').val(address || "");
    });
}

