import "@js/shared/bootstrap";
import "@lib/js/plugins/popper.min.js";
import "@lib/js/plugins/simplebar.min.js";
import "@lib/js/fonts/custom-font.js";
import "@lib/js/pages/dashboard-default.js";
import "@lib/js/fonts/custom-ant-icon.js";
import "@lib/js/pcoded.js";
import "@js/admin/datatables";

import { initTinyEditor } from "@js/config";
import {
    resetPassword,
    previewImage,
    removeImage,
    initBeautiServices,
    initBeautiPackageForm,
    autoPop
} from "@js/admin/util";
import { initColorPickerSync } from "@js/admin/util";
import { initToasts } from "@js/ui/toast";
import { initChoices } from "@js/ui/choices";
import { initSelect2 } from "@js/ui/select2";
import initLocationSelect from "@js/shared/location-select";

$(function () {
    initChoices();
    initSelect2();
    initToasts();
    initColorPickerSync("#color", "#color-picker");
    initLocationSelect();
    window.resetPassword = resetPassword;
    window.previewImage = previewImage;
    window.removeImage = removeImage;
    initTinyEditor();
    initBeautiServices();
    initBeautiPackageForm();
    autoPop();
});
